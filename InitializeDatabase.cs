using System;
using System.IO;
using ZinStore.Data.Context;

namespace ZinStore.DatabaseSetup
{
    /// <summary>
    /// Utilitaire pour initialiser la base de données ZinStore
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Initialisation de la base de données ZinStore ===");
            Console.WriteLine();

            try
            {
                // Chemin de la base de données
                string appPath = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
                string dataPath = Path.Combine(appPath, "Data");
                string dbPath = Path.Combine(dataPath, "ZinStore.db");

                Console.WriteLine($"Chemin de l'application: {appPath}");
                Console.WriteLine($"Chemin de la base de données: {dbPath}");
                Console.WriteLine();

                // Créer le dossier Data s'il n'existe pas
                if (!Directory.Exists(dataPath))
                {
                    Directory.CreateDirectory(dataPath);
                    Console.WriteLine("✓ Dossier Data créé");
                }

                // Vérifier si la base de données existe déjà
                if (File.Exists(dbPath))
                {
                    Console.WriteLine("⚠ La base de données existe déjà.");
                    Console.Write("Voulez-vous la recréer? (y/N): ");
                    string response = Console.ReadLine();
                    
                    if (response?.ToLower() != "y")
                    {
                        Console.WriteLine("Opération annulée.");
                        return;
                    }
                    
                    File.Delete(dbPath);
                    Console.WriteLine("✓ Ancienne base de données supprimée");
                }

                // Initialiser la base de données
                Console.WriteLine("Création de la base de données...");
                
                using (var context = new DatabaseContext())
                {
                    // Tester la connexion
                    if (!context.TestConnection())
                    {
                        throw new Exception("Impossible de se connecter à la base de données.");
                    }
                    Console.WriteLine("✓ Connexion à la base de données établie");

                    // Initialiser la base de données
                    var initializer = new DatabaseInitializer(context);
                    initializer.Initialize();
                    Console.WriteLine("✓ Tables créées avec succès");
                    Console.WriteLine("✓ Données initiales insérées");
                }

                Console.WriteLine();
                Console.WriteLine("=== Base de données initialisée avec succès! ===");
                Console.WriteLine($"Chemin: {dbPath}");
                Console.WriteLine("Utilisateur par défaut: admin");
                Console.WriteLine("Mot de passe par défaut: admin123");
                Console.WriteLine();
                Console.WriteLine("Vous pouvez maintenant lancer l'application ZinStore.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("Détails de l'erreur:");
                Console.WriteLine(ex.ToString());
            }

            Console.WriteLine();
            Console.WriteLine("Appuyez sur une touche pour continuer...");
            Console.ReadKey();
        }
    }
}
