using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class DepenseRepository : BaseRepository<Depense>
    {
        public DepenseRepository(DatabaseContext context) : base(context, "Depenses")
        {
        }

        public override async Task<IEnumerable<Depense>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT d.*, f.Nom as FournisseurNom, u.NomComplet as UtilisateurNom
                FROM Depenses d
                LEFT JOIN Fournisseurs f ON d.FournisseurId = f.Id
                LEFT JOIN Utilisateurs u ON d.UtilisateurId = u.Id
                WHERE d.EstSupprime = 0 
                AND (d.NumeroReference LIKE @SearchTerm OR d.Description LIKE @SearchTerm)
                ORDER BY d.DateDepense DESC";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Depense>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }
    }
}
