<Window x:Class="ZinStore.UI.Views.Achats.AchatFormWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
        Title="Nouvel Achat"
        Height="700"
        Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.DataContext>
        <viewModels:AchatFormViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ShoppingCart" 
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="🛒 Nouvel Achat"
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="N° Bon:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding NumeroBon}" 
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Contenu principal -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Partie gauche - Informations et produits -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Informations générales -->
                <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.ColumnSpan="5"
                                  Text="Informations Générales"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  FontWeight="Bold"
                                  Margin="0,0,0,20"/>

                        <ComboBox Grid.Row="1" Grid.Column="0"
                                 materialDesign:HintAssist.Hint="Fournisseur"
                                 materialDesign:HintAssist.IsFloating="True"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 ItemsSource="{Binding Fournisseurs}"
                                 SelectedItem="{Binding SelectedFournisseur}"
                                 DisplayMemberPath="Nom"/>

                        <DatePicker Grid.Row="1" Grid.Column="2"
                                   materialDesign:HintAssist.Hint="Date d'achat"
                                   materialDesign:HintAssist.IsFloating="True"
                                   Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                   SelectedDate="{Binding DateAchat}"/>

                        <ComboBox Grid.Row="1" Grid.Column="4"
                                 materialDesign:HintAssist.Hint="Mode de paiement"
                                 materialDesign:HintAssist.IsFloating="True"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 SelectedValue="{Binding ModePaiement}">
                            <ComboBoxItem Content="Espèces" Tag="Especes"/>
                            <ComboBoxItem Content="Chèque" Tag="Cheque"/>
                            <ComboBoxItem Content="Virement" Tag="Virement"/>
                            <ComboBoxItem Content="Crédit" Tag="Credit"/>
                        </ComboBox>
                    </Grid>
                </materialDesign:Card>

                <!-- Liste des produits -->
                <materialDesign:Card Grid.Row="1" Padding="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- En-tête produits -->
                        <Border Grid.Row="0"
                               Background="{DynamicResource MaterialDesignToolBarBackground}"
                               Padding="15,10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Text="Produits Achetés"
                                          Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                          FontWeight="Bold"/>
                                
                                <Button Grid.Column="1"
                                       Style="{StaticResource MaterialDesignRaisedButton}"
                                       Command="{Binding AddProduitCommand}"
                                       Content="➕ Ajouter Produit"
                                       Height="35"/>
                            </Grid>
                        </Border>

                        <!-- Recherche produit -->
                        <Border Grid.Row="1" Padding="15">
                            <TextBox materialDesign:HintAssist.Hint="Rechercher un produit à ajouter..."
                                    materialDesign:HintAssist.IsFloating="True"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Text="{Binding SearchProduitText, UpdateSourceTrigger=PropertyChanged}">
                                <TextBox.InputBindings>
                                    <KeyBinding Key="Enter" Command="{Binding SearchProduitCommand}"/>
                                </TextBox.InputBindings>
                            </TextBox>
                        </Border>

                        <!-- DataGrid produits -->
                        <DataGrid Grid.Row="2"
                                 ItemsSource="{Binding ProduitsAchetes}"
                                 Style="{StaticResource MaterialDesignDataGrid}"
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column"
                                 Margin="15">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Produit"
                                                  Binding="{Binding NomProduit}"
                                                  Width="200"/>
                                <DataGridTextColumn Header="Prix Unitaire"
                                                  Binding="{Binding PrixUnitaire, StringFormat='{}{0:N2} DA'}"
                                                  Width="120"/>
                                <DataGridTextColumn Header="Quantité"
                                                  Binding="{Binding Quantite}"
                                                  Width="100"/>
                                <DataGridTextColumn Header="Total"
                                                  Binding="{Binding Total, StringFormat='{}{0:N2} DA'}"
                                                  Width="120"/>
                                <DataGridTemplateColumn Header="Actions" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.RemoveProduitCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Supprimer">
                                                <materialDesign:PackIcon Kind="Delete" Foreground="Red"/>
                                            </Button>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </materialDesign:Card>
            </Grid>

            <!-- Partie droite - Résumé et actions -->
            <StackPanel Grid.Column="2">
                <!-- Résumé financier -->
                <materialDesign:Card Padding="20" Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Text="Résumé Financier"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  FontWeight="Bold"
                                  Margin="0,0,0,15"/>

                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Sous-total HT:" FontWeight="Bold"/>
                            <TextBlock Grid.Column="1" Text="{Binding MontantHT, StringFormat='{}{0:N2} DA'}" FontWeight="Bold"/>
                        </Grid>

                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="TVA (19%):" />
                            <TextBlock Grid.Column="1" Text="{Binding MontantTVA, StringFormat='{}{0:N2} DA'}"/>
                        </Grid>

                        <Separator Margin="0,10"/>

                        <Grid Margin="0,10,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Total TTC:" FontWeight="Bold" FontSize="16"/>
                            <TextBlock Grid.Column="1" 
                                      Text="{Binding MontantTotal, StringFormat='{}{0:N2} DA'}" 
                                      FontWeight="Bold" 
                                      FontSize="16"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Notes -->
                <materialDesign:Card Padding="20" Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Text="Notes"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  FontWeight="Bold"
                                  Margin="0,0,0,10"/>
                        
                        <TextBox materialDesign:HintAssist.Hint="Notes sur l'achat..."
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="{Binding Notes}"
                                Height="80"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>

        <!-- Boutons d'action -->
        <materialDesign:Card Grid.Row="2" Padding="20" Margin="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <CheckBox Content="Imprimer le bon d'achat"
                             IsChecked="{Binding ImprimerBon}"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="💾 Enregistrer"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding SaveCommand}"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Foreground="White"
                           Margin="0,0,10,0"
                           Height="40"
                           Padding="20,0"/>
                    <Button Content="❌ Annuler"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding CancelCommand}"
                           Height="40"
                           Padding="20,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
