# دليل إعداد قاعدة البيانات - ZinStore

## 🎯 نظرة عامة

تم إضافة نظام متقدم لإدارة قاعدة البيانات في ZinStore يتعامل تلقائياً مع حالات عدم وجود قاعدة البيانات أو مشاكل الاتصال.

## ✨ الميزات الجديدة

### 🔄 **التحقق التلقائي عند بدء التطبيق**
- يتحقق التطبيق تلقائياً من وجود قاعدة البيانات عند البدء
- في حالة عدم وجود قاعدة البيانات، يعرض نافذة الإعداد الأولي
- يوفر خيارات متعددة لحل المشكلة

### 🛠️ **نافذة الإعداد الأولي**
تظهر نافذة `DatabaseSetupWindow` عند عدم وجود قاعدة البيانات وتوفر ثلاث خيارات:

#### **1. إنشاء قاعدة بيانات جديدة**
- ينشئ قاعدة بيانات SQLite جديدة في مجلد `Data`
- يقوم بإنشاء جميع الجداول المطلوبة
- يضيف المستخدم الافتراضي:
  - **اسم المستخدم**: `admin`
  - **كلمة المرور**: `admin123`

#### **2. تحديد قاعدة بيانات موجودة**
- يسمح بتصفح واختيار ملف قاعدة بيانات SQLite موجود
- يختبر الاتصال قبل الحفظ
- يحفظ مسار قاعدة البيانات المختارة

#### **3. التكوين اليدوي**
- يفتح نافذة `DatabaseConnectionWindow` للتكوين المتقدم
- يدعم أنواع قواعد بيانات متعددة (SQLite, SQL Server)
- يوفر إدارة ملفات الاتصال الاحتياطية

## 🚀 كيفية الاستخدام

### **عند تشغيل التطبيق لأول مرة:**

1. **شغل التطبيق**
   ```bash
   dotnet run --project ZinStore.UI
   ```

2. **ستظهر نافذة الإعداد الأولي** إذا لم تكن قاعدة البيانات موجودة

3. **اختر الخيار المناسب:**
   - **للمستخدمين الجدد**: انقر "Créer Nouvelle Base"
   - **للمستخدمين الحاليين**: انقر "Parcourir..." لاختيار قاعدة بيانات موجودة
   - **للتكوين المتقدم**: انقر "Configuration Manuelle"

4. **اتبع التعليمات** حسب الخيار المختار

5. **بعد الانتهاء** ستظهر نافذة تسجيل الدخول

### **تسجيل الدخول:**
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 🔧 التكوين المتقدم

### **نافذة التكوين اليدوي**
تسمح بـ:
- تكوين أنواع قواعد بيانات مختلفة
- إدارة ملفات الاتصال الاحتياطية
- اختبار الاتصال قبل الحفظ
- حفظ التكوين في ملف نصي احتياطي

### **نظام الملفات الاحتياطية**
- **الملف الرئيسي**: `connection.txt`
- **ملف النسخ الاحتياطي**: `connection_backup.txt`
- **الموقع**: مجلد التطبيق الرئيسي

## 📁 هيكل الملفات

```
ZinStore/
├── Data/                    # مجلد قاعدة البيانات
│   └── ZinStore.db         # ملف قاعدة البيانات الرئيسي
├── connection.txt          # ملف الاتصال الاحتياطي
├── connection_backup.txt   # نسخة احتياطية من ملف الاتصال
└── ZinStore.exe           # ملف التطبيق
```

## 🛡️ الأمان والموثوقية

### **نظام الأولوية المتدرج:**
1. **ملف التكوين** (App.config) - الأولوية الأولى
2. **ملف الاتصال النصي** (connection.txt) - الأولوية الثانية
3. **قاعدة البيانات الافتراضية** (.\Data\ZinStore.db) - الأولوية الثالثة

### **معالجة الأخطاء:**
- **استرداد تلقائي** في حالة فشل الاتصال
- **رسائل خطأ واضحة** باللغة الفرنسية
- **خيارات متعددة** لحل المشاكل

## 🚨 حل المشاكل الشائعة

### **"قاعدة البيانات غير موجودة"**
```
الحل: استخدم خيار "Créer Nouvelle Base" في نافذة الإعداد
```

### **"خطأ في الاتصال"**
```
الحل: تحقق من مسار قاعدة البيانات أو استخدم التكوين اليدوي
```

### **"صلاحيات غير كافية"**
```
الحل: تأكد من تشغيل التطبيق بصلاحيات كافية لإنشاء الملفات
```

### **"ملف التكوين تالف"**
```
الحل: احذف ملفات connection.txt واتركها تُنشأ تلقائياً
```

## 📞 الدعم

في حالة مواجهة مشاكل:
1. تحقق من رسائل الخطأ المعروضة
2. تأكد من وجود صلاحيات الكتابة في مجلد التطبيق
3. جرب إعادة تشغيل التطبيق كمدير
4. استخدم خيار "Configuration Manuelle" للتكوين المتقدم

## 🎉 ملاحظات مهمة

- **النسخ الاحتياطية**: يتم إنشاء نسخ احتياطية تلقائياً لملفات الاتصال
- **الأمان**: كلمات المرور محمية بتشفير SHA256
- **المرونة**: يدعم النظام أنواع قواعد بيانات متعددة
- **سهولة الاستخدام**: واجهة بسيطة وواضحة باللغة الفرنسية

---

**تم تطوير هذا النظام لضمان تجربة مستخدم سلسة وموثوقة في ZinStore** 🚀
