using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;

namespace ZinStore.Business.Services
{
    public class StockService
    {
        private readonly StockRepository _stockRepository;

        public StockService(DatabaseContext context)
        {
            _stockRepository = new StockRepository(context);
        }

        public async Task<IEnumerable<Stock>> GetAllStockAsync()
        {
            return await _stockRepository.GetAllAsync();
        }

        public async Task<Stock> GetStockByProduitIdAsync(int produitId)
        {
            return await _stockRepository.GetByProduitIdAsync(produitId);
        }

        public async Task<IEnumerable<Stock>> SearchStockAsync(string searchTerm)
        {
            return await _stockRepository.SearchAsync(searchTerm);
        }
    }
}
