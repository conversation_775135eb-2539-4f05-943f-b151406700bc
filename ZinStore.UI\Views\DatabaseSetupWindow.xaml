<Window x:Class="ZinStore.UI.Views.DatabaseSetupWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Configuration Initiale - ZinStore"
        Height="800"
        Width="600"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="30,30,30,33">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,30">
            <materialDesign:PackIcon Kind="DatabaseAlert" 
                                   Width="64" Height="64"
                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                   HorizontalAlignment="Center"/>
            <TextBlock Text="Configuration Initiale Requise"
                      FontSize="24"
                      FontWeight="Bold"
                      HorizontalAlignment="Center"
                      Margin="0,10,0,5"/>
            <TextBlock Text="La base de données n'a pas été trouvée"
                      FontSize="14"
                      Opacity="0.7"
                      HorizontalAlignment="Center"/>
        </StackPanel>

        <!-- Contenu principal -->
        <materialDesign:Card Grid.Row="1" Padding="30" Margin="0,0,0,-24" Grid.RowSpan="2">
            <StackPanel>
                <TextBlock Text="Que souhaitez-vous faire ?"
                          FontSize="16"
                          FontWeight="SemiBold"
                          Margin="0,0,0,20"/>

                <!-- Option 1: Créer nouvelle base -->
                <Border BorderBrush="{DynamicResource PrimaryHueMidBrush}"
                       BorderThickness="2"
                       CornerRadius="8"
                       Padding="20"
                       Margin="0,0,0,15">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="DatabasePlus"
                                                   Width="24" Height="24"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,10,0"/>
                            <TextBlock Text="Créer une nouvelle base de données"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock Text="Créer une nouvelle base de données SQLite avec les données initiales (utilisateur admin, tables vides)."
                                  FontSize="12"
                                  Opacity="0.8"
                                  TextWrapping="Wrap"
                                  Margin="34,0,0,15"/>
                        <Button x:Name="CreateNewButton"
                               Content="Créer Nouvelle Base"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Click="CreateNew_Click"
                               HorizontalAlignment="Left"/>
                    </StackPanel>
                </Border>

                <!-- Option 2: Sélectionner base existante -->
                <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                       BorderThickness="1"
                       CornerRadius="8"
                       Padding="20"
                       Margin="0,0,0,15">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="FolderOpen"
                                                   Width="24" Height="24"
                                                   Foreground="{DynamicResource MaterialDesignBody}"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,10,0"/>
                            <TextBlock Text="Sélectionner une base existante"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock Text="Parcourir et sélectionner un fichier de base de données SQLite existant."
                                  FontSize="12"
                                  Opacity="0.8"
                                  TextWrapping="Wrap"
                                  Margin="34,0,0,15"/>
                        <Button x:Name="SelectExistingButton"
                               Content="Parcourir..."
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Click="SelectExisting_Click"
                               HorizontalAlignment="Left"/>
                    </StackPanel>
                </Border>

                <!-- Option 3: Configuration manuelle -->
                <Border BorderBrush="{DynamicResource MaterialDesignDivider}"
                       BorderThickness="1"
                       CornerRadius="8"
                       Padding="20">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <materialDesign:PackIcon Kind="Settings"
                                                   Width="24" Height="24"
                                                   Foreground="{DynamicResource MaterialDesignBody}"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,10,0"/>
                            <TextBlock Text="Configuration manuelle"
                                      FontSize="14"
                                      FontWeight="SemiBold"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock Text="Configurer manuellement la chaîne de connexion à la base de données."
                                  FontSize="12"
                                  Opacity="0.8"
                                  TextWrapping="Wrap"
                                  Margin="34,0,0,15"/>
                        <Button x:Name="ManualConfigButton"
                               Content="Configuration Manuelle"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Click="ManualConfig_Click"
                               HorizontalAlignment="Left"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </materialDesign:Card>

        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right" 
                   Margin="0,20,0,0">
            <Button Content="Quitter"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Exit_Click"
                   Width="100"
                   Margin="0,0,10,0"/>
        </StackPanel>

        <!-- Indicateur de progression -->
        <Grid x:Name="ProgressOverlay" 
              Grid.RowSpan="3"
              Background="#80000000"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"
                           Margin="0,0,0,20"/>
                <TextBlock x:Name="ProgressText"
                          Text="Création en cours..."
                          Foreground="White"
                          FontSize="14"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
