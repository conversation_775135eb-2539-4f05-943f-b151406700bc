using System;
using System.ComponentModel.DataAnnotations;
using ZinStore.Core.Enums;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les achats
    /// </summary>
    public class Achat : BaseEntity
    {
        [Required(ErrorMessage = "Le numéro de facture est obligatoire")]
        [StringLength(50)]
        public string NumeroFacture { get; set; }

        [StringLength(50)]
        public string NumeroFactureFournisseur { get; set; }

        [Required]
        public DateTime DateAchat { get; set; } = DateTime.Now;

        [Required]
        public int FournisseurId { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        public StatutFacture Statut { get; set; } = StatutFacture.Brouillon;

        public decimal SousTotal { get; set; }

        public decimal MontantTVA { get; set; }

        public decimal MontantRemise { get; set; }

        public decimal MontantTotal { get; set; }

        public decimal MontantPaye { get; set; }

        public decimal MontantRestant { get; set; }

        [StringLength(50)]
        public string ModePaiement { get; set; }

        public DateTime? DateEcheance { get; set; }

        public string Notes { get; set; }

        public bool EstComptant { get; set; } = false;

        public bool EstRecue { get; set; } = false;

        public DateTime? DateReception { get; set; }

        [StringLength(50)]
        public string BonCommande { get; set; }

        [StringLength(50)]
        public string BonLivraison { get; set; }

        // Propriétés de navigation
        public virtual Fournisseur Fournisseur { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }

        // Propriétés calculées
        public bool EstPayee => MontantRestant <= 0;
        public bool EstEnRetard => DateEcheance.HasValue && DateEcheance < DateTime.Now && MontantRestant > 0;
    }
}
