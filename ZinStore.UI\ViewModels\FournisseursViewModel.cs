using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class FournisseursViewModel : BaseViewModel
    {
        private readonly FournisseurService _fournisseurService;

        public FournisseursViewModel()
        {
            _fournisseurService = new FournisseurService(new DatabaseContext());

            Fournisseurs = new ObservableCollection<Fournisseur>();

            AddFournisseurCommand = new RelayCommand(AddFournisseur);
            EditFournisseurCommand = new RelayCommand(EditFournisseur, CanEditFournisseur);
            DeleteFournisseurCommand = new RelayCommand(DeleteFournisseur, CanDeleteFournisseur);
            RefreshCommand = new RelayCommand(async () => await LoadFournisseursAsync());

            _ = LoadFournisseursAsync();
        }

        public ObservableCollection<Fournisseur> Fournisseurs { get; }

        private Fournisseur _selectedFournisseur;
        public Fournisseur SelectedFournisseur
        {
            get => _selectedFournisseur;
            set => SetProperty(ref _selectedFournisseur, value);
        }

        private string _searchText;
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                SearchFournisseurs();
            }
        }

        public ICommand AddFournisseurCommand { get; }
        public ICommand EditFournisseurCommand { get; }
        public ICommand DeleteFournisseurCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadFournisseursAsync()
        {
            try
            {
                IsBusy = true;
                var fournisseurs = await _fournisseurService.GetAllFournisseursAsync();

                Fournisseurs.Clear();
                foreach (var fournisseur in fournisseurs)
                {
                    Fournisseurs.Add(fournisseur);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des fournisseurs: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void SearchFournisseurs()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadFournisseursAsync();
                return;
            }

            try
            {
                var fournisseurs = await _fournisseurService.SearchFournisseursAsync(SearchText);

                Fournisseurs.Clear();
                foreach (var fournisseur in fournisseurs)
                {
                    Fournisseurs.Add(fournisseur);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void AddFournisseur()
        {
            var window = new Views.Fournisseurs.FournisseurFormWindow();
            if (window.ShowDialog() == true && window.FournisseurSaved)
            {
                _ = LoadFournisseursAsync();
            }
        }

        private void EditFournisseur()
        {
            if (SelectedFournisseur == null) return;

            var window = new Views.Fournisseurs.FournisseurFormWindow(SelectedFournisseur);
            if (window.ShowDialog() == true && window.FournisseurSaved)
            {
                _ = LoadFournisseursAsync();
            }
        }

        private async void DeleteFournisseur()
        {
            if (SelectedFournisseur == null) return;

            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir supprimer le fournisseur {SelectedFournisseur.RaisonSociale} ?"))
            {
                try
                {
                    var result = await _fournisseurService.DeleteFournisseurAsync(SelectedFournisseur.Id);
                    if (result.Success)
                    {
                        MessageBoxHelper.ShowSuccess(result.Message);
                        await LoadFournisseursAsync();
                    }
                    else
                    {
                        MessageBoxHelper.ShowError(result.Message);
                    }
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la suppression: {ex.Message}");
                }
            }
        }

        private bool CanEditFournisseur()
        {
            return SelectedFournisseur != null;
        }

        private bool CanDeleteFournisseur()
        {
            return SelectedFournisseur != null;
        }
    }
}
