using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class StockViewModel : BaseViewModel
    {
        private readonly ProduitService _produitService;

        public StockViewModel()
        {
            _produitService = new ProduitService(new DatabaseContext());

            Produits = new ObservableCollection<Produit>();

            AjustementStockCommand = new RelayCommand(AjustementStock);
            InventaireCommand = new RelayCommand(Inventaire);
            ExportCommand = new RelayCommand(Export);
            RefreshCommand = new RelayCommand(async () => await LoadProduitsAsync());

            _ = LoadProduitsAsync();
        }

        public ObservableCollection<Produit> Produits { get; }

        private Produit _selectedProduit;
        public Produit SelectedProduit
        {
            get => _selectedProduit;
            set => SetProperty(ref _selectedProduit, value);
        }

        private string _searchText;
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                SearchProduits();
            }
        }

        private string _selectedFilter = "Tous";
        public string SelectedFilter
        {
            get => _selectedFilter;
            set
            {
                SetProperty(ref _selectedFilter, value);
                FilterProduits();
            }
        }

        // Statistiques
        private int _totalProduits;
        public int TotalProduits
        {
            get => _totalProduits;
            set => SetProperty(ref _totalProduits, value);
        }

        private int _produitsEnRupture;
        public int ProduitsEnRupture
        {
            get => _produitsEnRupture;
            set => SetProperty(ref _produitsEnRupture, value);
        }

        private int _stockFaible;
        public int StockFaible
        {
            get => _stockFaible;
            set => SetProperty(ref _stockFaible, value);
        }

        private decimal _valeurTotale;
        public decimal ValeurTotale
        {
            get => _valeurTotale;
            set => SetProperty(ref _valeurTotale, value);
        }

        public ICommand AjustementStockCommand { get; }
        public ICommand InventaireCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadProduitsAsync()
        {
            try
            {
                IsBusy = true;
                var produits = await _produitService.GetAllProduitsAsync();

                Produits.Clear();
                foreach (var produit in produits)
                {
                    // Calculer les propriétés dérivées
                    produit.ValeurStock = produit.StockActuel * produit.PrixAchat;
                    produit.IsEnRupture = produit.StockActuel <= 0;
                    produit.IsStockFaible = produit.StockActuel > 0 && produit.StockActuel <= produit.StockMinimum;

                    Produits.Add(produit);
                }

                // Calculer les statistiques
                CalculateStatistics();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des produits: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void CalculateStatistics()
        {
            TotalProduits = Produits.Count;
            ProduitsEnRupture = Produits.Count(p => p.IsEnRupture);
            StockFaible = Produits.Count(p => p.IsStockFaible);
            ValeurTotale = Produits.Sum(p => p.ValeurStock);
        }

        private async void SearchProduits()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadProduitsAsync();
                return;
            }

            try
            {
                var produits = await _produitService.SearchProduitsAsync(SearchText);

                Produits.Clear();
                foreach (var produit in produits)
                {
                    produit.ValeurStock = produit.StockActuel * produit.PrixAchat;
                    produit.IsEnRupture = produit.StockActuel <= 0;
                    produit.IsStockFaible = produit.StockActuel > 0 && produit.StockActuel <= produit.StockMinimum;

                    Produits.Add(produit);
                }

                CalculateStatistics();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private async void FilterProduits()
        {
            try
            {
                var allProduits = await _produitService.GetAllProduitsAsync();
                var filteredProduits = allProduits.AsEnumerable();

                switch (SelectedFilter)
                {
                    case "En stock":
                        filteredProduits = filteredProduits.Where(p => p.StockActuel > p.StockMinimum);
                        break;
                    case "Stock faible":
                        filteredProduits = filteredProduits.Where(p => p.StockActuel > 0 && p.StockActuel <= p.StockMinimum);
                        break;
                    case "En rupture":
                        filteredProduits = filteredProduits.Where(p => p.StockActuel <= 0);
                        break;
                    default: // "Tous"
                        break;
                }

                Produits.Clear();
                foreach (var produit in filteredProduits)
                {
                    produit.ValeurStock = produit.StockActuel * produit.PrixAchat;
                    produit.IsEnRupture = produit.StockActuel <= 0;
                    produit.IsStockFaible = produit.StockActuel > 0 && produit.StockActuel <= produit.StockMinimum;

                    Produits.Add(produit);
                }

                CalculateStatistics();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du filtrage: {ex.Message}");
            }
        }

        private void AjustementStock()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'ajustement de stock en cours de développement.");
        }

        private void Inventaire()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'inventaire en cours de développement.");
        }

        private void Export()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'export en cours de développement.");
        }
    }
}
