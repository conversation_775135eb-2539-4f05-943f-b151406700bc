&lt;wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation"&gt;
	&lt;s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/PredefinedNamingRules/=PrivateInstanceFields/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;&lt;/s:String&gt;
	&lt;s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/PredefinedNamingRules/=PrivateStaticFields/@EntryIndexedValue">&lt;Policy Inspect="True" Prefix="" Suffix="" Style="aaBb" /&gt;&lt;/s:String&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Achat/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Achats/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Barre/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Beneficiaire/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Categorie/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Caisse/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Caissier/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Comptabilite/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Crediteur/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Debiteur/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Depense/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Depenses/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Echeance/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Ecriture/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Fournisseur/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Fournisseurs/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Gestionnaire/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Livraison/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Mouvement/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Parametres/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Perissable/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Pondere/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Produit/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Produits/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Quantite/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Rapport/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Rapports/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Remise/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Revenu/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Revenus/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Rupture/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Sauvegarde/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Societe/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Souscrire/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Supprime/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Tresorerie/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Unitaire/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Utilisateur/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Utilisateurs/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Vente/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Ventes/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Vendeur/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
	&lt;s:Boolean x:Key="/Default/UserDictionary/Words/=Zinstore/@EntryIndexedValue"&gt;True&lt;/s:Boolean&gt;
&lt;/wpf:ResourceDictionary&gt;
