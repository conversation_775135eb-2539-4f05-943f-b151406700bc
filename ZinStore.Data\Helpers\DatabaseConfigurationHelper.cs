using System;
using System.Configuration;
using System.IO;
using System.Data.SQLite;
using Microsoft.Data.SqlClient;

namespace ZinStore.Data.Helpers
{
    /// <summary>
    /// Classe d'aide pour la configuration de la base de données
    /// </summary>
    public static class DatabaseConfigurationHelper
    {
        /// <summary>
        /// Types de bases de données supportés
        /// </summary>
        public enum DatabaseType
        {
            SQLite,
            SqlServer,
            MySQL,
            PostgreSQL
        }

        /// <summary>
        /// Obtient le type de base de données à partir de la chaîne de connexion
        /// </summary>
        public static DatabaseType GetDatabaseType(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                throw new ArgumentException("La chaîne de connexion ne peut pas être vide");

            connectionString = connectionString.ToLower();

            if (connectionString.Contains("data source") && !connectionString.Contains("server"))
                return DatabaseType.SQLite;
            else if (connectionString.Contains("server") || connectionString.Contains("data source"))
                return DatabaseType.SqlServer;
            else if (connectionString.Contains("host") && connectionString.Contains("database"))
                return DatabaseType.PostgreSQL;
            else if (connectionString.Contains("server") && connectionString.Contains("database"))
                return DatabaseType.MySQL;

            throw new NotSupportedException("Type de base de données non reconnu");
        }

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        public static bool TestConnection(string connectionString, out string errorMessage)
        {
            errorMessage = string.Empty;

            try
            {
                var dbType = GetDatabaseType(connectionString);

                switch (dbType)
                {
                    case DatabaseType.SQLite:
                        return TestSQLiteConnection(connectionString, out errorMessage);
                    case DatabaseType.SqlServer:
                        return TestSqlServerConnection(connectionString, out errorMessage);
                    default:
                        errorMessage = "Type de base de données non supporté";
                        return false;
                }
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// Teste la connexion SQLite
        /// </summary>
        private static bool TestSQLiteConnection(string connectionString, out string errorMessage)
        {
            errorMessage = string.Empty;

            try
            {
                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();

                    // Test simple query
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT 1";
                        command.ExecuteScalar();
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// Teste la connexion SQL Server
        /// </summary>
        private static bool TestSqlServerConnection(string connectionString, out string errorMessage)
        {
            errorMessage = string.Empty;

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();

                    // Test simple query
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT 1";
                        command.ExecuteScalar();
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                return false;
            }
        }

        /// <summary>
        /// Génère une chaîne de connexion SQLite
        /// </summary>
        public static string GenerateSQLiteConnectionString(string databasePath, bool createIfNotExists = true)
        {
            if (string.IsNullOrEmpty(databasePath))
                throw new ArgumentException("Le chemin de la base de données ne peut pas être vide");

            var connectionString = $"Data Source={databasePath};Version=3;";

            if (!createIfNotExists)
            {
                connectionString += "New=False;";
            }

            return connectionString;
        }

        /// <summary>
        /// Génère une chaîne de connexion SQL Server
        /// </summary>
        public static string GenerateSqlServerConnectionString(string server, string database,
            bool useWindowsAuth = true, string username = null, string password = null, int timeout = 30)
        {
            if (string.IsNullOrEmpty(server))
                throw new ArgumentException("Le nom du serveur ne peut pas être vide");

            if (string.IsNullOrEmpty(database))
                throw new ArgumentException("Le nom de la base de données ne peut pas être vide");

            var connectionString = $"Server={server};Database={database};";

            if (useWindowsAuth)
            {
                connectionString += "Integrated Security=true;";
            }
            else
            {
                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                    throw new ArgumentException("Le nom d'utilisateur et le mot de passe sont requis pour l'authentification SQL");

                connectionString += $"User Id={username};Password={password};";
            }

            connectionString += $"Connect Timeout={timeout};";

            return connectionString;
        }

        /// <summary>
        /// Sauvegarde la chaîne de connexion dans le fichier de configuration
        /// </summary>
        public static void SaveConnectionString(string connectionString, string name = "ZinStoreDB")
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

                // Supprimer la chaîne de connexion existante
                if (config.ConnectionStrings.ConnectionStrings[name] != null)
                {
                    config.ConnectionStrings.ConnectionStrings.Remove(name);
                }

                // Ajouter la nouvelle chaîne de connexion
                config.ConnectionStrings.ConnectionStrings.Add(new ConnectionStringSettings(name, connectionString));

                // Sauvegarder la configuration
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("connectionStrings");
            }
            catch (Exception ex)
            {
                throw new Exception($"Impossible de sauvegarder la chaîne de connexion: {ex.Message}");
            }
        }

        /// <summary>
        /// Obtient la chaîne de connexion actuelle
        /// </summary>
        public static string GetCurrentConnectionString(string name = "ZinStoreDB")
        {
            return ConfigurationManager.ConnectionStrings[name]?.ConnectionString;
        }

        /// <summary>
        /// Crée le répertoire pour la base de données SQLite si nécessaire
        /// </summary>
        public static void EnsureSQLiteDirectory(string databasePath)
        {
            try
            {
                var directory = Path.GetDirectoryName(Path.GetFullPath(databasePath));
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Impossible de créer le répertoire de la base de données: {ex.Message}");
            }
        }

        /// <summary>
        /// Vérifie si la base de données SQLite existe
        /// </summary>
        public static bool SQLiteDatabaseExists(string databasePath)
        {
            try
            {
                return File.Exists(Path.GetFullPath(databasePath));
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Crée une base de données SQLite vide
        /// </summary>
        public static void CreateEmptySQLiteDatabase(string databasePath)
        {
            try
            {
                EnsureSQLiteDirectory(databasePath);

                if (!SQLiteDatabaseExists(databasePath))
                {
                    SQLiteConnection.CreateFile(databasePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Impossible de créer la base de données SQLite: {ex.Message}");
            }
        }

        /// <summary>
        /// Obtient des informations sur la base de données
        /// </summary>
        public static DatabaseInfo GetDatabaseInfo(string connectionString)
        {
            var dbType = GetDatabaseType(connectionString);
            var info = new DatabaseInfo
            {
                Type = dbType,
                ConnectionString = connectionString
            };

            try
            {
                switch (dbType)
                {
                    case DatabaseType.SQLite:
                        var dataSourceStart = connectionString.IndexOf("Data Source=") + 12;
                        var dataSourceEnd = connectionString.IndexOf(";", dataSourceStart);
                        if (dataSourceEnd == -1) dataSourceEnd = connectionString.Length;

                        info.DatabasePath = connectionString.Substring(dataSourceStart, dataSourceEnd - dataSourceStart);
                        info.Exists = SQLiteDatabaseExists(info.DatabasePath);

                        if (info.Exists)
                        {
                            var fileInfo = new FileInfo(info.DatabasePath);
                            info.Size = fileInfo.Length;
                            info.LastModified = fileInfo.LastWriteTime;
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                info.Error = ex.Message;
            }

            return info;
        }
    }

    /// <summary>
    /// Informations sur la base de données
    /// </summary>
    public class DatabaseInfo
    {
        public DatabaseConfigurationHelper.DatabaseType Type { get; set; }
        public string ConnectionString { get; set; }
        public string DatabasePath { get; set; }
        public bool Exists { get; set; }
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public string Error { get; set; }
    }
}
