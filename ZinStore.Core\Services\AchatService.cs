using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ZinStore.Core.Data;
using ZinStore.Core.Models;
using ZinStore.Core.Services.Interfaces;
using ZinStore.Core.Enums;

namespace ZinStore.Core.Services
{
    public class AchatService : IAchatService
    {
        private readonly ZinStoreContext _context;

        public AchatService(ZinStoreContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Achat>> GetAllAchatsAsync()
        {
            return await _context.Achats
                .Include(a => a.Fournisseur)
                .Include(a => a.Utilisateur)
                .OrderByDescending(a => a.DateAchat)
                .ToListAsync();
        }

        public async Task<Achat> GetAchatByIdAsync(int id)
        {
            return await _context.Achats
                .Include(a => a.Fournisseur)
                .Include(a => a.Utilisateur)
                .FirstOrDefaultAsync(a => a.Id == id);
        }

        public async Task<IEnumerable<Achat>> SearchAchatsAsync(string searchText, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            var query = _context.Achats
                .Include(a => a.Fournisseur)
                .Include(a => a.Utilisateur)
                .AsQueryable();

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                query = query.Where(a => 
                    a.NumeroFacture.Contains(searchText) ||
                    a.Fournisseur.Nom.Contains(searchText) ||
                    a.NumeroFactureFournisseur.Contains(searchText));
            }

            if (dateDebut.HasValue)
            {
                query = query.Where(a => a.DateAchat >= dateDebut.Value);
            }

            if (dateFin.HasValue)
            {
                query = query.Where(a => a.DateAchat <= dateFin.Value);
            }

            return await query.OrderByDescending(a => a.DateAchat).ToListAsync();
        }

        public async Task<IEnumerable<Achat>> GetAchatsByFournisseurAsync(int fournisseurId)
        {
            return await _context.Achats
                .Include(a => a.Fournisseur)
                .Include(a => a.Utilisateur)
                .Where(a => a.FournisseurId == fournisseurId)
                .OrderByDescending(a => a.DateAchat)
                .ToListAsync();
        }

        public async Task<IEnumerable<Achat>> GetAchatsByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            return await _context.Achats
                .Include(a => a.Fournisseur)
                .Include(a => a.Utilisateur)
                .Where(a => a.DateAchat >= dateDebut && a.DateAchat <= dateFin)
                .OrderByDescending(a => a.DateAchat)
                .ToListAsync();
        }

        public async Task<Achat> CreateAchatAsync(Achat achat)
        {
            if (string.IsNullOrEmpty(achat.NumeroFacture))
            {
                achat.NumeroFacture = await GenerateNumeroAchatAsync();
            }

            achat.DateCreation = DateTime.Now;
            achat.MontantRestant = achat.MontantTotal - achat.MontantPaye;

            _context.Achats.Add(achat);
            await _context.SaveChangesAsync();

            return await GetAchatByIdAsync(achat.Id);
        }

        public async Task<Achat> UpdateAchatAsync(Achat achat)
        {
            var existingAchat = await _context.Achats.FindAsync(achat.Id);
            if (existingAchat == null)
                throw new ArgumentException("Achat non trouvé");

            existingAchat.NumeroFactureFournisseur = achat.NumeroFactureFournisseur;
            existingAchat.DateAchat = achat.DateAchat;
            existingAchat.FournisseurId = achat.FournisseurId;
            existingAchat.Statut = achat.Statut;
            existingAchat.SousTotal = achat.SousTotal;
            existingAchat.MontantTVA = achat.MontantTVA;
            existingAchat.MontantRemise = achat.MontantRemise;
            existingAchat.MontantTotal = achat.MontantTotal;
            existingAchat.MontantPaye = achat.MontantPaye;
            existingAchat.MontantRestant = achat.MontantTotal - achat.MontantPaye;
            existingAchat.ModePaiement = achat.ModePaiement;
            existingAchat.DateEcheance = achat.DateEcheance;
            existingAchat.Notes = achat.Notes;
            existingAchat.EstComptant = achat.EstComptant;
            existingAchat.EstRecue = achat.EstRecue;
            existingAchat.DateReception = achat.DateReception;
            existingAchat.BonCommande = achat.BonCommande;
            existingAchat.BonLivraison = achat.BonLivraison;
            existingAchat.DateModification = DateTime.Now;

            await _context.SaveChangesAsync();
            return await GetAchatByIdAsync(achat.Id);
        }

        public async Task<bool> DeleteAchatAsync(int id)
        {
            var achat = await _context.Achats.FindAsync(id);
            if (achat == null)
                return false;

            // Vérifier si l'achat peut être supprimé
            if (achat.Statut == StatutFacture.Validee || achat.EstRecue)
            {
                throw new InvalidOperationException("Impossible de supprimer un achat validé ou reçu");
            }

            _context.Achats.Remove(achat);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<string> GenerateNumeroAchatAsync()
        {
            var today = DateTime.Now;
            var prefix = $"ACH{today:yyyyMM}";
            
            var lastNumber = await _context.Achats
                .Where(a => a.NumeroFacture.StartsWith(prefix))
                .OrderByDescending(a => a.NumeroFacture)
                .Select(a => a.NumeroFacture)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (!string.IsNullOrEmpty(lastNumber))
            {
                var numberPart = lastNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int currentNumber))
                {
                    nextNumber = currentNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        public async Task<decimal> GetTotalAchatsAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            var query = _context.Achats.AsQueryable();

            if (dateDebut.HasValue)
            {
                query = query.Where(a => a.DateAchat >= dateDebut.Value);
            }

            if (dateFin.HasValue)
            {
                query = query.Where(a => a.DateAchat <= dateFin.Value);
            }

            return await query.SumAsync(a => a.MontantTotal);
        }

        public async Task<IEnumerable<Achat>> GetAchatsEnAttenteAsync()
        {
            return await _context.Achats
                .Include(a => a.Fournisseur)
                .Include(a => a.Utilisateur)
                .Where(a => a.Statut == StatutFacture.EnAttente || !a.EstRecue)
                .OrderBy(a => a.DateAchat)
                .ToListAsync();
        }

        public async Task<bool> MarquerCommeRecuAsync(int achatId)
        {
            var achat = await _context.Achats.FindAsync(achatId);
            if (achat == null)
                return false;

            achat.EstRecue = true;
            achat.DateReception = DateTime.Now;
            achat.Statut = StatutFacture.Validee;
            achat.DateModification = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> AnnulerAchatAsync(int achatId)
        {
            var achat = await _context.Achats.FindAsync(achatId);
            if (achat == null)
                return false;

            if (achat.EstRecue)
            {
                throw new InvalidOperationException("Impossible d'annuler un achat déjà reçu");
            }

            achat.Statut = StatutFacture.Annulee;
            achat.DateModification = DateTime.Now;

            await _context.SaveChangesAsync();
            return true;
        }
    }
}
