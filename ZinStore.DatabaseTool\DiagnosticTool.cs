using System;
using System.IO;
using System.Threading.Tasks;
using ZinStore.Data.Context;
using ZinStore.Business.Helpers;
using Dapper;

namespace ZinStore.DatabaseTool
{
    /// <summary>
    /// Outil de diagnostic pour vérifier l'état de la base de données
    /// </summary>
    public class DiagnosticTool
    {
        public static async Task RunDiagnostic()
        {
            Console.WriteLine("=== DIAGNOSTIC DE LA BASE DE DONNÉES ===");
            Console.WriteLine();

            try
            {
                // 1. Vérifier la connexion à la base de données
                Console.WriteLine("1. Test de connexion à la base de données...");
                using (var context = new DatabaseContext())
                {
                    Console.WriteLine($"Chaîne de connexion utilisée: {context.ConnectionString}");

                    if (context.TestConnection())
                    {
                        Console.WriteLine("✓ Connexion réussie");

                        // 2. Vérifier les tables
                        Console.WriteLine("\n2. Vérification des tables...");
                        await CheckTables(context);

                        // 3. Vérifier les utilisateurs
                        Console.WriteLine("\n3. Vérification des utilisateurs...");
                        await CheckUsers(context);

                        // 4. Test d'authentification
                        Console.WriteLine("\n4. Test d'authentification...");
                        await TestAuthentication(context);
                    }
                    else
                    {
                        Console.WriteLine("✗ Échec de la connexion");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur: {ex.Message}");
                Console.WriteLine($"Détails: {ex}");
            }

            Console.WriteLine("\n=== FIN DU DIAGNOSTIC ===");
        }

        private static async Task CheckTables(DatabaseContext context)
        {
            try
            {
                using (var connection = context.GetConnection())
                {
                    // Vérifier la table Utilisateurs
                    var tableExists = await connection.QueryFirstOrDefaultAsync<int>(
                        "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='Utilisateurs'");

                    if (tableExists > 0)
                    {
                        Console.WriteLine("✓ Table Utilisateurs existe");

                        // Compter les utilisateurs
                        var userCount = await connection.QueryFirstOrDefaultAsync<int>(
                            "SELECT COUNT(*) FROM Utilisateurs");
                        Console.WriteLine($"  - Nombre d'utilisateurs: {userCount}");
                    }
                    else
                    {
                        Console.WriteLine("✗ Table Utilisateurs n'existe pas");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur lors de la vérification des tables: {ex.Message}");
            }
        }

        private static async Task CheckUsers(DatabaseContext context)
        {
            try
            {
                using (var connection = context.GetConnection())
                {
                    // Lister tous les utilisateurs
                    var users = await connection.QueryAsync(
                        "SELECT NomUtilisateur, NomComplet, EstActif FROM Utilisateurs");

                    Console.WriteLine("Utilisateurs dans la base:");
                    foreach (var user in users)
                    {
                        string status = user.EstActif == 1 ? "Actif" : "Inactif";
                        Console.WriteLine($"  - {user.NomUtilisateur} ({user.NomComplet}) - {status}");
                    }

                    // Vérifier l'utilisateur admin spécifiquement
                    var adminUser = await connection.QueryFirstOrDefaultAsync(
                        "SELECT * FROM Utilisateurs WHERE NomUtilisateur = 'admin'");

                    if (adminUser != null)
                    {
                        Console.WriteLine("\n✓ Utilisateur admin trouvé");
                        Console.WriteLine($"  - Nom complet: {adminUser.NomComplet}");
                        Console.WriteLine($"  - Actif: {(adminUser.EstActif == 1 ? "Oui" : "Non")}");
                        Console.WriteLine($"  - Hash du mot de passe: {adminUser.MotDePasse?.Substring(0, 20)}...");
                    }
                    else
                    {
                        Console.WriteLine("✗ Utilisateur admin non trouvé");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur lors de la vérification des utilisateurs: {ex.Message}");
            }
        }

        private static async Task TestAuthentication(DatabaseContext context)
        {
            try
            {
                // Test avec les identifiants par défaut
                string username = "admin";
                string password = "admin123";
                string hashedPassword = PasswordHelper.HashPassword(password);

                Console.WriteLine($"Test avec: {username} / {password}");
                Console.WriteLine($"Hash généré: {hashedPassword.Substring(0, 20)}...");

                using (var connection = context.GetConnection())
                {
                    var user = await connection.QueryFirstOrDefaultAsync(
                        @"SELECT * FROM Utilisateurs
                          WHERE NomUtilisateur = @username
                          AND MotDePasse = @hashedPassword
                          AND EstActif = 1",
                        new { username, hashedPassword });

                    if (user != null)
                    {
                        Console.WriteLine("✓ Authentification réussie");
                    }
                    else
                    {
                        Console.WriteLine("✗ Authentification échouée");

                        // Vérifier chaque condition séparément
                        var userByName = await connection.QueryFirstOrDefaultAsync(
                            "SELECT * FROM Utilisateurs WHERE NomUtilisateur = @username",
                            new { username });

                        if (userByName == null)
                        {
                            Console.WriteLine("  - Utilisateur non trouvé");
                        }
                        else
                        {
                            Console.WriteLine("  - Utilisateur trouvé");
                            Console.WriteLine($"  - Hash stocké: {userByName.MotDePasse?.Substring(0, 20)}...");
                            Console.WriteLine($"  - Hash calculé: {hashedPassword.Substring(0, 20)}...");
                            Console.WriteLine($"  - Correspondance: {userByName.MotDePasse == hashedPassword}");
                            Console.WriteLine($"  - Actif: {userByName.EstActif == 1}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur lors du test d'authentification: {ex.Message}");
            }
        }
    }
}
