# Guide de Développement - ZinStore

## 🏗️ Architecture du Projet

### Vue d'Ensemble
ZinStore suit une architecture en couches basée sur le pattern MVVM (Model-View-ViewModel) pour assurer une séparation claire des responsabilités et faciliter la maintenance.

```
┌─────────────────┐
│   ZinStore.UI   │  ← Interface utilisateur (WPF + MVVM)
├─────────────────┤
│ ZinStore.Business│  ← Logique métier et services
├─────────────────┤
│  ZinStore.Data  │  ← Accès aux données (Repositories + Dapper)
├─────────────────┤
│  ZinStore.Core  │  ← Modèles et entités de base
└─────────────────┘
```

### Couches du Système

#### 1. ZinStore.Core
- **Responsabilité** : Entités métier, enums, interfaces de base
- **Contenu** :
  - Modèles de données (Client, Produit, Vente, etc.)
  - Énumérations (TypeUtilisateur, StatutFacture, etc.)
  - Classe de base BaseEntity
  - Interfaces communes

#### 2. ZinStore.Data
- **Responsabilité** : Accès aux données et persistance
- **Contenu** :
  - DatabaseContext (gestion de la connexion SQLite)
  - Repositories (pattern Repository)
  - DatabaseInitializer (création et initialisation de la DB)
  - Requêtes SQL avec Dapper

#### 3. ZinStore.Business
- **Responsabilité** : Logique métier et règles de gestion
- **Contenu** :
  - Services métier (ClientService, ProduitService, etc.)
  - Validation des données
  - Calculs et transformations
  - Helpers et utilitaires

#### 4. ZinStore.UI
- **Responsabilité** : Interface utilisateur et interaction
- **Contenu** :
  - Views (XAML)
  - ViewModels (logique de présentation)
  - Converters et behaviors
  - Styles et ressources

#### 5. ZinStore.Reports
- **Responsabilité** : Génération de rapports
- **Contenu** :
  - Templates de rapports
  - Générateurs PDF/Excel
  - Logique de formatage

## 🛠️ Technologies et Frameworks

### Technologies Principales
- **.NET Framework 4.7.2** : Compatibilité Windows 7+
- **WPF** : Interface utilisateur riche
- **SQLite** : Base de données embarquée
- **Dapper** : ORM léger et performant
- **Material Design in XAML** : Interface moderne

### Packages NuGet Utilisés
```xml
<!-- ZinStore.Data -->
<PackageReference Include="Dapper" Version="2.0.123" />
<PackageReference Include="System.Data.SQLite.Core" Version="1.0.117" />

<!-- ZinStore.UI -->
<PackageReference Include="MaterialDesignThemes" Version="4.6.1" />
<PackageReference Include="MaterialDesignColors" Version="2.0.9" />
```

## 🔧 Configuration de l'Environnement

### Prérequis Développeur
1. **Visual Studio 2019+** (Community, Professional, ou Enterprise)
2. **.NET Framework 4.7.2 SDK**
3. **Git** pour le contrôle de version
4. **SQLite Browser** (optionnel, pour inspecter la DB)

### Extensions Visual Studio Recommandées
- **ReSharper** ou **CodeMaid** : Refactoring et nettoyage de code
- **XAML Styler** : Formatage automatique XAML
- **GitLens** : Intégration Git avancée
- **SonarLint** : Analyse de qualité de code

### Configuration du Projet
```bash
# Cloner le repository
git clone https://github.com/zinstore/zinstore.git
cd zinstore

# Restaurer les packages NuGet
nuget restore ZinStore.sln

# Ouvrir dans Visual Studio
start ZinStore.sln
```

## 📝 Standards de Codage

### Conventions de Nommage

#### Classes et Interfaces
```csharp
// Classes : PascalCase
public class ClientService { }
public class ProduitRepository { }

// Interfaces : I + PascalCase
public interface IRepository<T> { }
public interface IClientService { }
```

#### Méthodes et Propriétés
```csharp
// Méthodes : PascalCase avec verbe d'action
public async Task<Client> GetClientByIdAsync(int id) { }
public bool ValidateClientData(Client client) { }

// Propriétés : PascalCase
public string NomComplet { get; set; }
public DateTime DateCreation { get; set; }
```

#### Variables et Paramètres
```csharp
// Variables locales : camelCase
var clientService = new ClientService();
string nomUtilisateur = "admin";

// Paramètres : camelCase
public void AddClient(Client client, string utilisateurCreation) { }

// Champs privés : _camelCase
private readonly IClientRepository _clientRepository;
private string _connectionString;
```

### Structure des Fichiers

#### Services
```csharp
namespace ZinStore.Business.Services
{
    /// <summary>
    /// Service pour la gestion des clients
    /// </summary>
    public class ClientService
    {
        private readonly ClientRepository _clientRepository;

        public ClientService(DatabaseContext context)
        {
            _clientRepository = new ClientRepository(context);
        }

        /// <summary>
        /// Ajoute un nouveau client
        /// </summary>
        /// <param name="client">Le client à ajouter</param>
        /// <returns>Résultat de l'opération</returns>
        public async Task<(bool Success, string Message, int ClientId)> AddClientAsync(Client client)
        {
            // Validation
            var validationResult = ValidateClient(client);
            if (!validationResult.IsValid)
                return (false, validationResult.ErrorMessage, 0);

            // Logique métier
            try
            {
                int clientId = await _clientRepository.AddAsync(client);
                return (true, "Client ajouté avec succès.", clientId);
            }
            catch (Exception ex)
            {
                return (false, $"Erreur : {ex.Message}", 0);
            }
        }

        private ValidationResult ValidateClient(Client client)
        {
            // Logique de validation
        }
    }
}
```

#### ViewModels
```csharp
namespace ZinStore.UI.ViewModels
{
    public class ClientsViewModel : BaseViewModel
    {
        private readonly ClientService _clientService;

        public ClientsViewModel()
        {
            _clientService = new ClientService(new DatabaseContext());
            LoadClientsCommand = new RelayCommand(async () => await LoadClientsAsync());
        }

        // Propriétés observables
        private ObservableCollection<Client> _clients;
        public ObservableCollection<Client> Clients
        {
            get => _clients;
            set => SetProperty(ref _clients, value);
        }

        // Commandes
        public ICommand LoadClientsCommand { get; }

        // Méthodes
        private async Task LoadClientsAsync()
        {
            try
            {
                IsBusy = true;
                var clients = await _clientService.GetAllClientsAsync();
                Clients = new ObservableCollection<Client>(clients);
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
}
```

## 🗄️ Gestion de la Base de Données

### Modèle de Données
```sql
-- Exemple de table
CREATE TABLE Clients (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    CodeClient TEXT NOT NULL UNIQUE,
    Nom TEXT NOT NULL,
    Email TEXT,
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    EstSupprime INTEGER NOT NULL DEFAULT 0
);
```

### Repositories
```csharp
public class ClientRepository : BaseRepository<Client>
{
    public ClientRepository(DatabaseContext context) : base(context, "Clients") { }

    public async Task<Client> GetByCodeAsync(string codeClient)
    {
        var sql = "SELECT * FROM Clients WHERE CodeClient = @CodeClient AND EstSupprime = 0";
        using (var connection = _context.GetConnection())
        {
            return await connection.QueryFirstOrDefaultAsync<Client>(sql, new { CodeClient = codeClient });
        }
    }
}
```

### Migrations
Pour ajouter une nouvelle colonne :
1. Modifier le modèle dans ZinStore.Core
2. Ajouter la migration dans DatabaseInitializer
3. Tester sur une copie de la base de données

## 🎨 Interface Utilisateur

### Structure XAML
```xml
<UserControl x:Class="ZinStore.UI.Views.ClientsView">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0" 
                  Text="Gestion des Clients" 
                  Style="{StaticResource TitleText}"/>

        <!-- Contenu -->
        <materialDesign:Card Grid.Row="1">
            <!-- Contenu de la vue -->
        </materialDesign:Card>
    </Grid>
</UserControl>
```

### Styles et Ressources
- Utiliser les styles définis dans App.xaml
- Respecter les couleurs Material Design
- Assurer la cohérence visuelle

## 🧪 Tests

### Tests Unitaires
```csharp
[TestClass]
public class ClientServiceTests
{
    private ClientService _clientService;
    private Mock<DatabaseContext> _mockContext;

    [TestInitialize]
    public void Setup()
    {
        _mockContext = new Mock<DatabaseContext>();
        _clientService = new ClientService(_mockContext.Object);
    }

    [TestMethod]
    public async Task AddClient_ValidClient_ReturnsSuccess()
    {
        // Arrange
        var client = new Client { Nom = "Test Client" };

        // Act
        var result = await _clientService.AddClientAsync(client);

        // Assert
        Assert.IsTrue(result.Success);
    }
}
```

### Tests d'Intégration
- Tester avec une vraie base de données SQLite en mémoire
- Vérifier les interactions entre couches
- Tester les scénarios complets

## 🚀 Déploiement

### Build de Release
```bash
# Compilation Release
msbuild ZinStore.sln /p:Configuration=Release

# Ou utiliser le script
deploy.bat
```

### Packaging
1. **Version Portable** : Copie des binaires + dépendances
2. **Installateur** : Utiliser WiX ou Inno Setup
3. **ClickOnce** : Pour déploiement automatique

## 📊 Performance

### Optimisations Base de Données
- Utiliser des index sur les colonnes fréquemment recherchées
- Limiter les requêtes N+1 avec des jointures
- Pagination pour les grandes listes

### Optimisations UI
- Virtualisation des listes (VirtualizingStackPanel)
- Binding asynchrone pour les opérations longues
- Cache des images et ressources

## 🔒 Sécurité

### Authentification
- Hachage des mots de passe avec salt
- Session timeout
- Verrouillage après tentatives échouées

### Autorisation
- Contrôle d'accès basé sur les rôles
- Validation côté serveur
- Audit trail des actions sensibles

## 📚 Documentation

### Code Documentation
```csharp
/// <summary>
/// Ajoute un nouveau client au système
/// </summary>
/// <param name="client">Le client à ajouter</param>
/// <returns>
/// Un tuple contenant :
/// - Success : true si l'opération a réussi
/// - Message : message de succès ou d'erreur
/// - ClientId : ID du client créé (0 si échec)
/// </returns>
/// <exception cref="ArgumentNullException">Si client est null</exception>
/// <exception cref="InvalidOperationException">Si le code client existe déjà</exception>
public async Task<(bool Success, string Message, int ClientId)> AddClientAsync(Client client)
```

### Documentation API
- Utiliser XML Documentation
- Générer la documentation avec DocFX
- Maintenir les exemples à jour

## 🤝 Workflow de Développement

### Branches Git
- **main** : Code stable en production
- **develop** : Intégration des nouvelles fonctionnalités
- **feature/xxx** : Développement de fonctionnalités
- **hotfix/xxx** : Corrections urgentes

### Pull Requests
1. Créer une branche feature
2. Développer et tester
3. Créer une PR vers develop
4. Code review obligatoire
5. Tests automatiques passants
6. Merge après approbation

### Versioning
- **Semantic Versioning** (MAJOR.MINOR.PATCH)
- **Tags Git** pour les releases
- **Changelog** maintenu à jour

---

**Happy Coding!** 🚀
