# Base de Données MySQL pour ZinStore

## 📋 Description

Ce dossier contient tous les scripts SQL nécessaires pour créer et configurer la base de données MySQL pour le système de gestion de magasin ZinStore.

## 🗂️ Structure des Fichiers

### Scripts d'Installation (à exécuter dans l'ordre)

1. **`01_create_database.sql`** - Création de la base de données
2. **`02_create_tables.sql`** - Création des tables principales
3. **`03_create_tables_part2.sql`** - Création des tables secondaires
4. **`04_create_triggers.sql`** - Création des triggers automatiques
5. **`05_create_views.sql`** - Création des vues pour les rapports
6. **`06_insert_sample_data.sql`** - Insertion des données d'exemple
7. **`07_create_procedures.sql`** - Création des procédures stockées
8. **`08_create_indexes.sql`** - Création des index pour les performances
9. **`09_setup_complete.sql`** - Configuration finale et vérifications

## 🚀 Installation Rapide

### Méthode 1: Installation Complète
```bash
# Se connecter à MySQL
mysql -u root -p

# Exécuter tous les scripts dans l'ordre
source 01_create_database.sql
source 02_create_tables.sql
source 03_create_tables_part2.sql
source 04_create_triggers.sql
source 05_create_views.sql
source 06_insert_sample_data.sql
source 07_create_procedures.sql
source 08_create_indexes.sql
source 09_setup_complete.sql
```

### Méthode 2: Script Unique
```bash
# Concaténer tous les scripts
cat *.sql > zinstore_complete.sql

# Exécuter le script complet
mysql -u root -p < zinstore_complete.sql
```

## 📊 Structure de la Base de Données

### Tables Principales

#### 👥 Gestion des Utilisateurs
- **`utilisateurs`** - Comptes utilisateurs du système

#### 🏪 Gestion Commerciale
- **`clients`** - Informations des clients
- **`fournisseurs`** - Informations des fournisseurs

#### 📦 Gestion des Produits
- **`categories`** - Catégories de produits
- **`produits`** - Catalogue des produits
- **`stock`** - Gestion des stocks en temps réel

#### 💰 Gestion des Transactions
- **`ventes`** - En-têtes des ventes
- **`ventes_details`** - Détails des lignes de vente
- **`achats`** - En-têtes des achats
- **`achats_details`** - Détails des lignes d'achat

#### 📈 Gestion Financière
- **`revenus`** - Enregistrement des revenus
- **`depenses`** - Enregistrement des dépenses
- **`mouvements_stock`** - Historique des mouvements

#### ⚙️ Configuration
- **`parametres`** - Paramètres système
- **`comptes_generaux`** - Plan comptable

### Vues Principales

- **`v_produits_stock`** - Vue complète produits + stock
- **`v_ventes_details`** - Vue détaillée des ventes
- **`v_achats_details`** - Vue détaillée des achats
- **`v_statistiques_ventes_produits`** - Statistiques par produit
- **`v_statistiques_financieres`** - Rapports financiers
- **`v_clients_statistiques`** - Statistiques clients

### Procédures Stockées

- **`sp_creer_vente_complete`** - Créer une vente avec tous ses détails
- **`sp_ajuster_stock`** - Ajustement manuel du stock
- **`sp_rapport_ventes_periode`** - Rapport de ventes par période
- **`sp_statistiques_financieres_periode`** - Statistiques financières
- **`sp_produits_rupture_stock`** - Produits en rupture

## 🔧 Configuration Requise

### Version MySQL
- **MySQL 8.0+** (recommandé)
- **MySQL 5.7+** (minimum)

### Fonctionnalités Utilisées
- **Colonnes générées** (GENERATED ALWAYS AS)
- **Triggers**
- **Procédures stockées**
- **Vues**
- **Index full-text**
- **Contraintes de clés étrangères**

### Paramètres Recommandés
```sql
-- Dans my.cnf ou my.ini
[mysqld]
innodb_buffer_pool_size = 256M
max_connections = 100
query_cache_size = 64M
tmp_table_size = 64M
max_heap_table_size = 64M
```

## 👤 Comptes Utilisateurs par Défaut

### Compte Administrateur
- **Utilisateur**: `admin`
- **Mot de passe**: `password`
- **Rôle**: Admin

### Comptes de Test
- **manager** / password (Manager)
- **vendeur1** / password (Vendeur)
- **vendeur2** / password (Vendeur)
- **caissier1** / password (Caissier)

> ⚠️ **Important**: Changez ces mots de passe en production!

## 📊 Données d'Exemple Incluses

### Produits (15 articles)
- Électronique (smartphones, laptops, écouteurs)
- Électroménager (réfrigérateurs, machines à laver)
- Mode (vêtements, chaussures)
- Alimentation (huile, riz, thé)
- Automobile (huile moteur, filtres)

### Clients (8 clients)
- Particuliers et entreprises
- Avec limites de crédit configurées

### Fournisseurs (5 fournisseurs)
- Spécialisés par catégorie
- Conditions de paiement variées

### Transactions d'Exemple
- 3 ventes avec différents modes de paiement
- 2 achats en cours
- Mouvements financiers divers

## 🔍 Requêtes Utiles

### Vérifier l'Installation
```sql
-- Lister toutes les tables
SHOW TABLES;

-- Vérifier les données
SELECT COUNT(*) as total_produits FROM produits;
SELECT COUNT(*) as total_clients FROM clients;
SELECT COUNT(*) as total_ventes FROM ventes;
```

### Statistiques Rapides
```sql
-- Chiffre d'affaires du jour
SELECT SUM(montant_total) as ca_jour 
FROM ventes 
WHERE DATE(date_vente) = CURDATE() 
AND statut_paiement = 'Payé';

-- Produits en rupture
CALL sp_produits_rupture_stock();

-- Top 10 des produits vendus
SELECT p.nom, SUM(vd.quantite) as total_vendu
FROM ventes_details vd
JOIN produits p ON vd.produit_id = p.id
GROUP BY p.id, p.nom
ORDER BY total_vendu DESC
LIMIT 10;
```

## 🛠️ Maintenance

### Sauvegarde
```bash
# Sauvegarde complète
mysqldump -u root -p zinstore > zinstore_backup_$(date +%Y%m%d).sql

# Sauvegarde structure seulement
mysqldump -u root -p --no-data zinstore > zinstore_structure.sql

# Sauvegarde données seulement
mysqldump -u root -p --no-create-info zinstore > zinstore_data.sql
```

### Optimisation
```sql
-- Analyser les tables
ANALYZE TABLE produits, ventes, stock;

-- Optimiser les tables (MyISAM seulement)
OPTIMIZE TABLE produits, ventes, stock;

-- Vérifier l'utilisation des index
SHOW INDEX FROM ventes;
```

### Monitoring
```sql
-- Taille des tables
SELECT 
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
FROM information_schema.tables 
WHERE table_schema = 'zinstore'
ORDER BY size_mb DESC;

-- Requêtes lentes
SHOW PROCESSLIST;
```

## 🔒 Sécurité

### Recommandations
1. **Changer les mots de passe par défaut**
2. **Créer des utilisateurs spécifiques pour l'application**
3. **Limiter les privilèges selon les rôles**
4. **Activer les logs d'audit**
5. **Configurer SSL pour les connexions**

### Création d'Utilisateurs
```sql
-- Utilisateur application
CREATE USER 'zinstore_app'@'localhost' IDENTIFIED BY 'mot_de_passe_fort';
GRANT SELECT, INSERT, UPDATE, DELETE ON zinstore.* TO 'zinstore_app'@'localhost';
GRANT EXECUTE ON zinstore.* TO 'zinstore_app'@'localhost';

-- Utilisateur lecture seule
CREATE USER 'zinstore_readonly'@'localhost' IDENTIFIED BY 'mot_de_passe_lecture';
GRANT SELECT ON zinstore.* TO 'zinstore_readonly'@'localhost';
```

## 📞 Support

Pour toute question ou problème:
1. Vérifiez les logs MySQL
2. Consultez la documentation MySQL
3. Vérifiez les contraintes de clés étrangères
4. Testez les requêtes étape par étape

## 📝 Notes de Version

### Version 1.0
- Structure complète de la base de données
- Triggers automatiques pour la gestion du stock
- Vues pour les rapports
- Procédures stockées pour les opérations complexes
- Index optimisés pour les performances
- Données d'exemple pour les tests

### Prochaines Versions
- Partitioning pour les grandes bases
- Réplication master-slave
- Archivage automatique des données anciennes
- Audit trail complet
