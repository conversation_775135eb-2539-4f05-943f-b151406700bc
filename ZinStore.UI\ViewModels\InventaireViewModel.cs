using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Core.Models;
using ZinStore.Business.Services;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class InventaireViewModel : BaseViewModel
    {
        private readonly ProduitService _produitService;
        private DateTime _dateInventaire;
        private string _searchText;
        private bool _appliquerAjustements;
        private int _totalProduits;
        private int _produitsVerifies;
        private int _ecartsDetectes;
        private int _ecartsPositifs;
        private int _ecartsNegatifs;
        private decimal _valeurEcarts;

        public InventaireViewModel()
        {
            // Initialiser le service
            _produitService = new ProduitService(new DatabaseContext());
            
            DateInventaire = DateTime.Now;
            ProduitsInventaire = new ObservableCollection<ProduitInventaire>();
            
            // Commandes
            ScanBarcodeCommand = new RelayCommand(ScanBarcode);
            ImportCommand = new RelayCommand(Import);
            SearchCommand = new RelayCommand(Search);
            ValiderCommand = new RelayCommand(p => ValiderProduit(p as ProduitInventaire));
            AjusterCommand = new RelayCommand(p => AjusterProduit(p as ProduitInventaire));
            GenererRapportCommand = new RelayCommand(GenererRapport);
            SauvegarderCommand = new RelayCommand(Sauvegarder);
            ValiderToutCommand = new RelayCommand(ValiderTout);
            ExporterCommand = new RelayCommand(Exporter);
            FinaliserCommand = new RelayCommand(Finaliser, CanFinaliser);
            FermerCommand = new RelayCommand(Fermer);
            
            // Charger les données de test
            LoadTestData();
        }

        public DateTime DateInventaire
        {
            get => _dateInventaire;
            set => SetProperty(ref _dateInventaire, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public bool AppliquerAjustements
        {
            get => _appliquerAjustements;
            set => SetProperty(ref _appliquerAjustements, value);
        }

        public int TotalProduits
        {
            get => _totalProduits;
            set => SetProperty(ref _totalProduits, value);
        }

        public int ProduitsVerifies
        {
            get => _produitsVerifies;
            set => SetProperty(ref _produitsVerifies, value);
        }

        public int EcartsDetectes
        {
            get => _ecartsDetectes;
            set => SetProperty(ref _ecartsDetectes, value);
        }

        public int EcartsPositifs
        {
            get => _ecartsPositifs;
            set => SetProperty(ref _ecartsPositifs, value);
        }

        public int EcartsNegatifs
        {
            get => _ecartsNegatifs;
            set => SetProperty(ref _ecartsNegatifs, value);
        }

        public decimal ValeurEcarts
        {
            get => _valeurEcarts;
            set => SetProperty(ref _valeurEcarts, value);
        }

        public double ProgressionPourcentage => TotalProduits > 0 ? (double)ProduitsVerifies / TotalProduits * 100 : 0;

        public ObservableCollection<ProduitInventaire> ProduitsInventaire { get; }

        // Commandes
        public ICommand ScanBarcodeCommand { get; }
        public ICommand ImportCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand ValiderCommand { get; }
        public ICommand AjusterCommand { get; }
        public ICommand GenererRapportCommand { get; }
        public ICommand SauvegarderCommand { get; }
        public ICommand ValiderToutCommand { get; }
        public ICommand ExporterCommand { get; }
        public ICommand FinaliserCommand { get; }
        public ICommand FermerCommand { get; }

        private void ScanBarcode()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Fonctionnalité de scan en cours de développement.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du scan: {ex.Message}");
            }
        }

        private void Import()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Fonctionnalité d'import en cours de développement.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'import: {ex.Message}");
            }
        }

        private void Search()
        {
            if (string.IsNullOrWhiteSpace(SearchText)) return;

            try
            {
                var produit = ProduitsInventaire.FirstOrDefault(p => 
                    p.CodeProduit.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                    p.NomProduit.Contains(SearchText, StringComparison.OrdinalIgnoreCase));

                if (produit != null)
                {
                    // Sélectionner le produit dans la grille
                    MessageBoxHelper.ShowInfo($"Produit trouvé: {produit.NomProduit}");
                }
                else
                {
                    MessageBoxHelper.ShowWarning("Produit non trouvé.");
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void ValiderProduit(ProduitInventaire produit)
        {
            if (produit == null) return;

            try
            {
                produit.EstVerifie = true;
                CalculerStatistiques();
                MessageBoxHelper.ShowSuccess($"Produit {produit.NomProduit} validé.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la validation: {ex.Message}");
            }
        }

        private void AjusterProduit(ProduitInventaire produit)
        {
            if (produit == null) return;

            try
            {
                // Ajuster le stock système avec le stock physique
                produit.StockSysteme = produit.StockPhysique;
                produit.EstVerifie = true;
                CalculerStatistiques();
                MessageBoxHelper.ShowSuccess($"Stock ajusté pour {produit.NomProduit}.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ajustement: {ex.Message}");
            }
        }

        private void GenererRapport()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Génération du rapport d'inventaire en cours...");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la génération du rapport: {ex.Message}");
            }
        }

        private void Sauvegarder()
        {
            try
            {
                MessageBoxHelper.ShowSuccess("Inventaire sauvegardé avec succès!");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la sauvegarde: {ex.Message}");
            }
        }

        private void ValiderTout()
        {
            try
            {
                foreach (var produit in ProduitsInventaire.Where(p => !p.EstVerifie))
                {
                    produit.EstVerifie = true;
                }
                CalculerStatistiques();
                MessageBoxHelper.ShowSuccess("Tous les produits ont été validés!");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la validation: {ex.Message}");
            }
        }

        private void Exporter()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Export en cours de développement.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'export: {ex.Message}");
            }
        }

        private void Finaliser()
        {
            try
            {
                if (MessageBoxHelper.ShowConfirmation("Êtes-vous sûr de vouloir finaliser cet inventaire?"))
                {
                    MessageBoxHelper.ShowSuccess("Inventaire finalisé avec succès!");
                    // Fermer la fenêtre
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la finalisation: {ex.Message}");
            }
        }

        private void Fermer()
        {
            // Fermer la fenêtre
        }

        private bool CanFinaliser()
        {
            return ProduitsInventaire.All(p => p.EstVerifie);
        }

        private void CalculerStatistiques()
        {
            TotalProduits = ProduitsInventaire.Count;
            ProduitsVerifies = ProduitsInventaire.Count(p => p.EstVerifie);
            EcartsDetectes = ProduitsInventaire.Count(p => p.AEcart);
            EcartsPositifs = ProduitsInventaire.Count(p => p.Ecart > 0);
            EcartsNegatifs = ProduitsInventaire.Count(p => p.Ecart < 0);
            ValeurEcarts = ProduitsInventaire.Sum(p => Math.Abs(p.Ecart) * 25.50m); // Prix moyen estimé
            
            OnPropertyChanged(nameof(ProgressionPourcentage));
        }

        private void LoadTestData()
        {
            ProduitsInventaire.Clear();
            
            ProduitsInventaire.Add(new ProduitInventaire
            {
                CodeProduit = "PRD001",
                NomProduit = "Produit Test 1",
                StockSysteme = 50,
                StockPhysique = 48,
                EstVerifie = false
            });
            
            ProduitsInventaire.Add(new ProduitInventaire
            {
                CodeProduit = "PRD002",
                NomProduit = "Produit Test 2",
                StockSysteme = 25,
                StockPhysique = 30,
                EstVerifie = true
            });
            
            ProduitsInventaire.Add(new ProduitInventaire
            {
                CodeProduit = "PRD003",
                NomProduit = "Produit Test 3",
                StockSysteme = 100,
                StockPhysique = 100,
                EstVerifie = true
            });
            
            CalculerStatistiques();
        }
    }

    public class ProduitInventaire : BaseViewModel
    {
        private int _stockPhysique;
        private bool _estVerifie;

        public string CodeProduit { get; set; }
        public string NomProduit { get; set; }
        public int StockSysteme { get; set; }

        public int StockPhysique
        {
            get => _stockPhysique;
            set
            {
                SetProperty(ref _stockPhysique, value);
                OnPropertyChanged(nameof(Ecart));
                OnPropertyChanged(nameof(AEcart));
            }
        }

        public bool EstVerifie
        {
            get => _estVerifie;
            set => SetProperty(ref _estVerifie, value);
        }

        public int Ecart => StockPhysique - StockSysteme;
        public bool AEcart => Ecart != 0;
    }
}
