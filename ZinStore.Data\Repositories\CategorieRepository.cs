using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class CategorieRepository : BaseRepository<Categorie>
    {
        public CategorieRepository(DatabaseContext context) : base(context, "Categories")
        {
        }

        public override async Task<IEnumerable<Categorie>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT * FROM Categories 
                WHERE EstSupprime = 0 
                AND (CodeCategorie LIKE @SearchTerm OR Nom LIKE @SearchTerm)
                ORDER BY Nom";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Categorie>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<IEnumerable<Categorie>> GetActiveCategoriesAsync()
        {
            var sql = "SELECT * FROM Categories WHERE EstActive = 1 AND EstSupprime = 0 ORDER BY OrdreAffichage, Nom";
            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Categorie>(sql);
            }
        }

        public async Task<string> GenerateNextCodeAsync(string prefix = "CAT")
        {
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTR(CodeCategorie, LENGTH(@Prefix) + 1) AS INTEGER)), 0) + 1 
                FROM Categories WHERE CodeCategorie LIKE @Pattern AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                var nextNumber = await connection.QuerySingleAsync<int>(sql, 
                    new { Prefix = prefix, Pattern = $"{prefix}%" });
                return $"{prefix}{nextNumber:D4}";
            }
        }
    }
}
