# دليل اختبار النظام الكامل - ZinStore

## 🎯 **الهدف من الاختبار**
التأكد من أن تسجيل الدخول يعمل بشكل صحيح وأن النافذة الرئيسية تظهر مع جميع الوظائف.

## 🚀 **خطوات الاختبار الشاملة**

### **المرحلة 1: تحضير النظام**

#### **1. التأكد من قاعدة البيانات:**
```bash
# اختبار قاعدة البيانات
dotnet run --project ZinStore.DatabaseTool test .\Data\ZinStore.db

# إذا لم تعمل، أعد إنشاؤها
dotnet run --project ZinStore.DatabaseTool create .\Data\ZinStore.db
```

#### **2. بناء المشروع:**
```bash
dotnet build ZinStore.UI --configuration Debug
```

#### **3. تشغيل التطبيق:**
```bash
dotnet run --project ZinStore.UI
```

### **المرحلة 2: اختبار تسجيل الدخول**

#### **1. نافذة تسجيل الدخول:**
- ✅ **يجب أن تظهر**: نافذة تسجيل الدخول الأنيقة
- ✅ **العناصر المطلوبة**:
  - حقل اسم المستخدم
  - حقل كلمة المرور
  - زر "Se connecter"
  - زر "Quitter"

#### **2. إدخال البيانات:**
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **انقر**: زر "Se connecter"

#### **3. النتائج المتوقعة:**
- ✅ **نجاح تسجيل الدخول**
- ✅ **إغلاق نافذة تسجيل الدخول**
- ✅ **فتح النافذة الرئيسية**

### **المرحلة 3: اختبار النافذة الرئيسية**

#### **1. العناصر الأساسية:**
- ✅ **الشريط العلوي**:
  - شعار ZinStore
  - قائمة التنقل الرئيسية
  - معلومات المستخدم: "Administrateur"
  - زر "Déconnexion"

#### **2. قائمة التنقل:**
- ✅ **Tableau de Bord** - لوحة التحكم
- ✅ **Clients** - إدارة العملاء
- ✅ **Fournisseurs** - إدارة الموردين
- ✅ **Produits** - إدارة المنتجات
- ✅ **Ventes** - إدارة المبيعات
- ✅ **Achats** - إدارة المشتريات
- ✅ **Stock** - إدارة المخزون
- ✅ **Rapports** - التقارير
- ✅ **Paramètres** - الإعدادات

#### **3. المحتوى الرئيسي:**
- ✅ **لوحة التحكم** تظهر بشكل افتراضي
- ✅ **إحصائيات ومؤشرات** الأداء
- ✅ **رسوم بيانية** وتقارير مرئية

#### **4. الشريط السفلي:**
- ✅ **رسالة الحالة**: "Tableau de bord"
- ✅ **التاريخ والوقت** الحالي
- ✅ **رقم الإصدار**: "Version 1.0"

### **المرحلة 4: اختبار الوظائف**

#### **1. اختبار التنقل:**
```
انقر على كل زر في القائمة وتأكد من:
- تغيير المحتوى الرئيسي
- تحديث رسالة الحالة
- عدم وجود أخطاء
```

#### **2. اختبار قائمة الإعدادات:**
```
انقر على "Paramètres" وتأكد من ظهور:
- Configuration Base de Données
- Paramètres Généraux  
- À propos
```

#### **3. اختبار إعدادات قاعدة البيانات:**
```
انقر على "Configuration Base de Données" وتأكد من:
- فتح نافذة إعدادات قاعدة البيانات
- وجود قسم "Fichier de Connexion de Secours"
- عمل جميع الأزرار بدون أخطاء
```

#### **4. اختبار نظام ملف الاتصال:**
```
في نافذة إعدادات قاعدة البيانات:
1. افتح قسم "Fichier de Connexion de Secours"
2. انقر "Info Fichier" - يجب أن تظهر معلومات الملف
3. انقر "Sauvegarder vers Fichier" - يجب أن تظهر رسالة نجاح
4. انقر "Charger depuis Fichier" - يجب أن تحمل السلسلة
```

### **المرحلة 5: اختبار تسجيل الخروج**

#### **1. تسجيل الخروج:**
```
انقر على زر "Déconnexion" وتأكد من:
- ظهور رسالة تأكيد
- إغلاق النافذة الرئيسية عند الموافقة
- فتح نافذة تسجيل الدخول مرة أخرى
```

#### **2. إعادة تسجيل الدخول:**
```
سجل الدخول مرة أخرى للتأكد من:
- عمل النظام بشكل متكرر
- عدم وجود مشاكل في الذاكرة
- استمرارية الأداء
```

## 🔍 **نقاط التحقق المهمة**

### **✅ تسجيل الدخول:**
- [ ] نافذة تسجيل الدخول تظهر
- [ ] البيانات admin/admin123 تعمل
- [ ] رسائل الخطأ تظهر للبيانات الخاطئة
- [ ] النافذة الرئيسية تفتح بعد النجاح

### **✅ النافذة الرئيسية:**
- [ ] اسم المستخدم يظهر: "Administrateur"
- [ ] جميع أزرار القائمة تعمل
- [ ] لوحة التحكم تظهر بشكل افتراضي
- [ ] الوقت والتاريخ يتحدثان

### **✅ نظام ملف الاتصال:**
- [ ] قائمة الإعدادات تعمل
- [ ] نافذة إعدادات قاعدة البيانات تفتح
- [ ] قسم ملف الاتصال يعمل
- [ ] جميع أزرار إدارة الملف تعمل

### **✅ تسجيل الخروج:**
- [ ] رسالة التأكيد تظهر
- [ ] النافذة الرئيسية تغلق
- [ ] نافذة تسجيل الدخول تفتح
- [ ] إعادة تسجيل الدخول تعمل

## 🚨 **استكشاف الأخطاء**

### **إذا لم تظهر النافذة الرئيسية:**
```bash
# تحقق من رسائل الخطأ في وحدة التحكم
# أعد بناء المشروع
dotnet clean
dotnet build

# تحقق من قاعدة البيانات
dotnet run --project ZinStore.DatabaseTool test .\Data\ZinStore.db
```

### **إذا ظهرت أخطاء في الواجهة:**
```bash
# تحقق من ملفات XAML
# تأكد من وجود جميع المراجع
# أعد تشغيل التطبيق
```

### **إذا لم تعمل الأزرار:**
```bash
# تحقق من ViewModel bindings
# تأكد من تنفيذ ICommand بشكل صحيح
# راجع رسائل Debug في وحدة التحكم
```

## 🎉 **النتائج المتوقعة**

### **عند نجاح جميع الاختبارات:**
- ✅ **تسجيل دخول سلس** بدون أخطاء
- ✅ **نافذة رئيسية كاملة الوظائف**
- ✅ **تنقل سهل** بين الأقسام
- ✅ **نظام ملف اتصال** يعمل بكفاءة
- ✅ **تسجيل خروج آمن** مع إمكانية العودة

### **الميزات الجديدة المتاحة:**
- 🔄 **نظام ملف الاتصال الاحتياطي**
- 🛡️ **مصادقة محسنة** مع Singleton
- 🎛️ **واجهة إدارة قاعدة البيانات**
- 📊 **لوحة تحكم تفاعلية**
- ⚙️ **إعدادات متقدمة**

**النظام الآن جاهز للاستخدام الكامل! 🚀**

---

**ملاحظة**: إذا واجهت أي مشاكل، راجع ملف `LOGIN_TEST_GUIDE.md` للحصول على تفاصيل إضافية حول استكشاف الأخطاء.
