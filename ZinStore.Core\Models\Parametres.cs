using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les paramètres de l'application
    /// </summary>
    public class Parametres : BaseEntity
    {
        // Informations de l'entreprise
        [Required(ErrorMessage = "Le nom de la société est obligatoire")]
        [StringLength(200)]
        public string NomSociete { get; set; }

        [StringLength(500)]
        public string AdresseSociete { get; set; }

        [StringLength(20)]
        public string TelephoneSociete { get; set; }

        [StringLength(20)]
        public string FaxSociete { get; set; }

        [EmailAddress]
        [StringLength(100)]
        public string EmailSociete { get; set; }

        [StringLength(100)]
        public string SiteWebSociete { get; set; }

        [StringLength(20)]
        public string NumeroRegistreCommerce { get; set; }

        [StringLength(20)]
        public string NumeroIdentificationFiscale { get; set; }

        [StringLength(20)]
        public string NumeroArticleImposition { get; set; }

        public string LogoSociete { get; set; }

        // Paramètres de TVA
        public decimal TauxTVADefaut { get; set; } = 19;

        public bool UtiliserTVA { get; set; } = true;

        // Paramètres de devise
        [StringLength(10)]
        public string DeviseDefaut { get; set; } = "DZD";

        [StringLength(10)]
        public string SymboleDevise { get; set; } = "DA";

        // Paramètres de numérotation
        [StringLength(10)]
        public string PrefixeFactureVente { get; set; } = "FV";

        [StringLength(10)]
        public string PrefixeFactureAchat { get; set; } = "FA";

        [StringLength(10)]
        public string PrefixeClient { get; set; } = "CL";

        [StringLength(10)]
        public string PrefixeFournisseur { get; set; } = "FR";

        [StringLength(10)]
        public string PrefixeProduit { get; set; } = "PR";

        public int ProchainNumeroFactureVente { get; set; } = 1;

        public int ProchainNumeroFactureAchat { get; set; } = 1;

        public int ProchainNumeroClient { get; set; } = 1;

        public int ProchainNumeroFournisseur { get; set; } = 1;

        public int ProchainNumeroProduit { get; set; } = 1;

        // Paramètres de stock
        public bool AlerteStockMinimum { get; set; } = true;

        public bool GestionLots { get; set; } = false;

        public bool GestionDatesExpiration { get; set; } = false;

        // Paramètres de sauvegarde
        public bool SauvegardeAutomatique { get; set; } = true;

        public int FrequenceSauvegarde { get; set; } = 24; // en heures

        [StringLength(500)]
        public string CheminSauvegarde { get; set; }

        // Paramètres d'impression
        [StringLength(100)]
        public string ImprimanteDefaut { get; set; }

        public bool ImprimerAutomatiquement { get; set; } = false;

        public int NombreCopiesFacture { get; set; } = 1;

        // Paramètres divers
        public bool ModeDebug { get; set; } = false;

        [StringLength(10)]
        public string LangueInterface { get; set; } = "FR";

        public string NotesGenerales { get; set; }
    }
}
