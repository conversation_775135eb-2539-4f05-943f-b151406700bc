using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using Dapper;
using ZinStore.Data.Helpers;

namespace ZinStore.Data.Context
{
    /// <summary>
    /// Contexte de base de données SQLite
    /// </summary>
    public class DatabaseContext : IDisposable
    {
        private readonly string _connectionString;

        public DatabaseContext()
        {
            _connectionString = GetConnectionString();
        }

        public DatabaseContext(string connectionString)
        {
            _connectionString = connectionString;
        }

        /// <summary>
        /// Obtient la chaîne de connexion à la base de données
        /// </summary>
        private string GetConnectionString()
        {
            // 1. Vérifier d'abord dans le fichier de configuration
            string connectionString = ConfigurationManager.ConnectionStrings["ZinStoreDB"]?.ConnectionString;

            // 2. Si pas trouvé, essayer le fichier de connexion texte
            if (string.IsNullOrEmpty(connectionString))
            {
                try
                {
                    connectionString = ConnectionFileManager.ReadConnectionString();
                    if (!string.IsNullOrEmpty(connectionString))
                    {
                        // Valider la chaîne de connexion lue
                        if (ConnectionFileManager.ValidateConnectionString(connectionString))
                        {
                            return connectionString;
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log l'erreur mais continuer avec la méthode par défaut
                    System.Diagnostics.Debug.WriteLine($"Erreur lors de la lecture du fichier de connexion: {ex.Message}");
                }
            }

            // 3. Si toujours pas trouvé, utiliser une base de données par défaut
            if (string.IsNullOrEmpty(connectionString))
            {
                string dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "ZinStore.db");

                // Créer le dossier Data s'il n'existe pas
                string dataFolder = Path.GetDirectoryName(dbPath);
                if (!Directory.Exists(dataFolder))
                {
                    Directory.CreateDirectory(dataFolder);
                }

                connectionString = $"Data Source={dbPath};Version=3;";

                // Sauvegarder cette chaîne de connexion par défaut dans le fichier texte
                try
                {
                    ConnectionFileManager.WriteConnectionString(connectionString);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Impossible de sauvegarder la chaîne de connexion par défaut: {ex.Message}");
                }
            }

            return connectionString;
        }

        /// <summary>
        /// Obtient une connexion à la base de données
        /// </summary>
        public IDbConnection GetConnection()
        {
            return new SQLiteConnection(_connectionString);
        }

        /// <summary>
        /// Commence une transaction
        /// </summary>
        public IDbTransaction BeginTransaction()
        {
            return GetConnection().BeginTransaction();
        }

        /// <summary>
        /// Teste la connexion à la base de données
        /// </summary>
        public bool TestConnection()
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    connection.Open();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Obtient la chaîne de connexion actuelle
        /// </summary>
        public string ConnectionString => _connectionString;

        /// <summary>
        /// Obtient la liste des noms de tables dans la base de données
        /// </summary>
        public List<string> GetTableNames()
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    connection.Open();
                    var sql = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' ORDER BY name";
                    return connection.Query<string>(sql).ToList();
                }
            }
            catch
            {
                return new List<string>();
            }
        }

        /// <summary>
        /// Sauvegarde la chaîne de connexion actuelle dans le fichier texte
        /// </summary>
        public void SaveConnectionStringToFile()
        {
            try
            {
                ConnectionFileManager.WriteConnectionString(_connectionString);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la sauvegarde de la chaîne de connexion: {ex.Message}");
            }
        }

        /// <summary>
        /// Charge une nouvelle chaîne de connexion depuis le fichier texte
        /// </summary>
        public static string LoadConnectionStringFromFile()
        {
            try
            {
                return ConnectionFileManager.ReadConnectionString();
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors du chargement de la chaîne de connexion: {ex.Message}");
            }
        }

        /// <summary>
        /// Vérifie si un fichier de connexion existe
        /// </summary>
        public static bool ConnectionFileExists()
        {
            return ConnectionFileManager.ConnectionFileExists();
        }

        /// <summary>
        /// Obtient des informations sur le fichier de connexion
        /// </summary>
        public static ConnectionFileInfo GetConnectionFileInfo()
        {
            return ConnectionFileManager.GetConnectionFileInfo();
        }

        /// <summary>
        /// Crée un fichier de connexion par défaut
        /// </summary>
        public static void CreateDefaultConnectionFile()
        {
            try
            {
                ConnectionFileManager.CreateDefaultConnectionFile();
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la création du fichier de connexion par défaut: {ex.Message}");
            }
        }

        /// <summary>
        /// Teste la connexion avec une chaîne de connexion spécifique
        /// </summary>
        public static bool TestConnectionString(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
            {
                return false;
            }

            try
            {
                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();
                    return connection.State == ConnectionState.Open;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Libère les ressources
        /// </summary>
        public void Dispose()
        {
            // No resources to dispose in this implementation
        }
    }
}
