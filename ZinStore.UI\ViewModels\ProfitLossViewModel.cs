using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class ProfitLossViewModel : BaseViewModel
    {
        private string _selectedPeriod = "ThisMonth";
        private DateTime? _dateFrom;
        private DateTime? _dateTo;
        private string _companyName = "ZinStore";
        private string _reportPeriod;
        private DateTime _generatedDate;
        private string _generatedBy = "Administrateur";

        // Totaux financiers
        private decimal _totalRevenue;
        private decimal _totalCostOfSales;
        private decimal _grossMargin;
        private decimal _totalOperatingExpenses;
        private decimal _netResult;

        // Indicateurs
        private decimal _grossMarginPercentage;
        private decimal _netMarginPercentage;
        private decimal _roi;
        private decimal _previousPeriodComparison;
        private decimal _yearOverYearComparison;

        public ProfitLossViewModel()
        {
            RevenueItems = new ObservableCollection<ReportLineItem>();
            CostOfSalesItems = new ObservableCollection<ReportLineItem>();
            OperatingExpenseItems = new ObservableCollection<ReportLineItem>();

            // Commandes
            GenerateReportCommand = new RelayCommand(async () => await GenerateReportAsync());
            ExportCommand = new RelayCommand(ExportReport);
            PrintCommand = new RelayCommand(PrintReport);
            RefreshCommand = new RelayCommand(async () => await RefreshDataAsync());
            ComparePeriodsCommand = new RelayCommand(ComparePeriods);
            DrillDownCommand = new RelayCommand(DrillDown);
            ScheduleReportCommand = new RelayCommand(ScheduleReport);

            // Initialiser les dates par défaut
            SetDefaultDates();
            GeneratedDate = DateTime.Now;

            _ = LoadDataAsync();
        }

        // Propriétés de base
        public string SelectedPeriod
        {
            get => _selectedPeriod;
            set
            {
                SetProperty(ref _selectedPeriod, value);
                SetDefaultDates();
                _ = LoadDataAsync();
            }
        }

        public DateTime? DateFrom
        {
            get => _dateFrom;
            set
            {
                SetProperty(ref _dateFrom, value);
                UpdateReportPeriod();
                _ = LoadDataAsync();
            }
        }

        public DateTime? DateTo
        {
            get => _dateTo;
            set
            {
                SetProperty(ref _dateTo, value);
                UpdateReportPeriod();
                _ = LoadDataAsync();
            }
        }

        public string CompanyName
        {
            get => _companyName;
            set => SetProperty(ref _companyName, value);
        }

        public string ReportPeriod
        {
            get => _reportPeriod;
            set => SetProperty(ref _reportPeriod, value);
        }

        public DateTime GeneratedDate
        {
            get => _generatedDate;
            set => SetProperty(ref _generatedDate, value);
        }

        public string GeneratedBy
        {
            get => _generatedBy;
            set => SetProperty(ref _generatedBy, value);
        }

        // Totaux financiers
        public decimal TotalRevenue
        {
            get => _totalRevenue;
            set => SetProperty(ref _totalRevenue, value);
        }

        public decimal TotalCostOfSales
        {
            get => _totalCostOfSales;
            set => SetProperty(ref _totalCostOfSales, value);
        }

        public decimal GrossMargin
        {
            get => _grossMargin;
            set => SetProperty(ref _grossMargin, value);
        }

        public decimal TotalOperatingExpenses
        {
            get => _totalOperatingExpenses;
            set => SetProperty(ref _totalOperatingExpenses, value);
        }

        public decimal NetResult
        {
            get => _netResult;
            set => SetProperty(ref _netResult, value);
        }

        // Indicateurs
        public decimal GrossMarginPercentage
        {
            get => _grossMarginPercentage;
            set => SetProperty(ref _grossMarginPercentage, value);
        }

        public decimal NetMarginPercentage
        {
            get => _netMarginPercentage;
            set => SetProperty(ref _netMarginPercentage, value);
        }

        public decimal ROI
        {
            get => _roi;
            set => SetProperty(ref _roi, value);
        }

        public decimal PreviousPeriodComparison
        {
            get => _previousPeriodComparison;
            set => SetProperty(ref _previousPeriodComparison, value);
        }

        public decimal YearOverYearComparison
        {
            get => _yearOverYearComparison;
            set => SetProperty(ref _yearOverYearComparison, value);
        }

        // Collections
        public ObservableCollection<ReportLineItem> RevenueItems { get; }
        public ObservableCollection<ReportLineItem> CostOfSalesItems { get; }
        public ObservableCollection<ReportLineItem> OperatingExpenseItems { get; }

        // Commandes
        public ICommand GenerateReportCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand PrintCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand ComparePeriodsCommand { get; }
        public ICommand DrillDownCommand { get; }
        public ICommand ScheduleReportCommand { get; }

        private void SetDefaultDates()
        {
            var now = DateTime.Now;
            
            switch (SelectedPeriod)
            {
                case "Today":
                    DateFrom = now.Date;
                    DateTo = now.Date;
                    break;
                case "ThisWeek":
                    var startOfWeek = now.AddDays(-(int)now.DayOfWeek + 1);
                    DateFrom = startOfWeek.Date;
                    DateTo = now.Date;
                    break;
                case "ThisMonth":
                    DateFrom = new DateTime(now.Year, now.Month, 1);
                    DateTo = now.Date;
                    break;
                case "ThisQuarter":
                    var quarter = (now.Month - 1) / 3 + 1;
                    DateFrom = new DateTime(now.Year, (quarter - 1) * 3 + 1, 1);
                    DateTo = now.Date;
                    break;
                case "ThisYear":
                    DateFrom = new DateTime(now.Year, 1, 1);
                    DateTo = now.Date;
                    break;
            }
            
            UpdateReportPeriod();
        }

        private void UpdateReportPeriod()
        {
            if (DateFrom.HasValue && DateTo.HasValue)
            {
                ReportPeriod = $"Du {DateFrom.Value:dd/MM/yyyy} au {DateTo.Value:dd/MM/yyyy}";
            }
        }

        private async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                await LoadRevenueDataAsync();
                await LoadCostOfSalesDataAsync();
                await LoadOperatingExpensesDataAsync();
                CalculateTotalsAndIndicators();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task LoadRevenueDataAsync()
        {
            try
            {
                // TODO: Charger depuis la base de données
                await Task.Delay(200); // Simulation

                RevenueItems.Clear();
                
                // Données d'exemple
                RevenueItems.Add(new ReportLineItem { Description = "Ventes de produits", Amount = 125000 });
                RevenueItems.Add(new ReportLineItem { Description = "Services", Amount = 15000 });
                RevenueItems.Add(new ReportLineItem { Description = "Autres revenus", Amount = 3500 });
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des revenus: {ex.Message}");
            }
        }

        private async Task LoadCostOfSalesDataAsync()
        {
            try
            {
                // TODO: Charger depuis la base de données
                await Task.Delay(200); // Simulation

                CostOfSalesItems.Clear();
                
                // Données d'exemple
                CostOfSalesItems.Add(new ReportLineItem { Description = "Coût des marchandises vendues", Amount = 75000 });
                CostOfSalesItems.Add(new ReportLineItem { Description = "Frais de transport", Amount = 2500 });
                CostOfSalesItems.Add(new ReportLineItem { Description = "Emballage", Amount = 1200 });
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement du coût des ventes: {ex.Message}");
            }
        }

        private async Task LoadOperatingExpensesDataAsync()
        {
            try
            {
                // TODO: Charger depuis la base de données
                await Task.Delay(200); // Simulation

                OperatingExpenseItems.Clear();
                
                // Données d'exemple
                OperatingExpenseItems.Add(new ReportLineItem { Description = "Salaires et charges sociales", Amount = 25000 });
                OperatingExpenseItems.Add(new ReportLineItem { Description = "Loyer", Amount = 8000 });
                OperatingExpenseItems.Add(new ReportLineItem { Description = "Électricité et eau", Amount = 1500 });
                OperatingExpenseItems.Add(new ReportLineItem { Description = "Téléphone et internet", Amount = 800 });
                OperatingExpenseItems.Add(new ReportLineItem { Description = "Assurances", Amount = 1200 });
                OperatingExpenseItems.Add(new ReportLineItem { Description = "Marketing et publicité", Amount = 3000 });
                OperatingExpenseItems.Add(new ReportLineItem { Description = "Fournitures de bureau", Amount = 500 });
                OperatingExpenseItems.Add(new ReportLineItem { Description = "Maintenance et réparations", Amount = 1000 });
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des charges d'exploitation: {ex.Message}");
            }
        }

        private void CalculateTotalsAndIndicators()
        {
            try
            {
                // Calcul des totaux
                TotalRevenue = RevenueItems.Sum(x => x.Amount);
                TotalCostOfSales = CostOfSalesItems.Sum(x => x.Amount);
                GrossMargin = TotalRevenue - TotalCostOfSales;
                TotalOperatingExpenses = OperatingExpenseItems.Sum(x => x.Amount);
                NetResult = GrossMargin - TotalOperatingExpenses;

                // Calcul des indicateurs
                GrossMarginPercentage = TotalRevenue > 0 ? (GrossMargin / TotalRevenue) * 100 : 0;
                NetMarginPercentage = TotalRevenue > 0 ? (NetResult / TotalRevenue) * 100 : 0;
                ROI = TotalCostOfSales > 0 ? (NetResult / TotalCostOfSales) * 100 : 0;

                // Simulations de comparaisons
                PreviousPeriodComparison = 12.5m;
                YearOverYearComparison = 8.3m;
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du calcul des totaux: {ex.Message}");
            }
        }

        private async Task GenerateReportAsync()
        {
            try
            {
                IsBusy = true;
                GeneratedDate = DateTime.Now;
                await LoadDataAsync();
                MessageBoxHelper.ShowSuccess("Rapport généré avec succès.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la génération du rapport: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task RefreshDataAsync()
        {
            await LoadDataAsync();
        }

        private async void ExportReport()
        {
            try
            {
                IsBusy = true;
                BusyMessage = "Export en cours...";

                await Task.Run(() =>
                {
                    // Créer un nom de fichier avec timestamp
                    var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                    var fileName = $"Rapport_Profits_Pertes_{timestamp}.csv";
                    var filePath = System.IO.Path.Combine(
                        Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                        fileName);

                    // Créer le contenu CSV
                    var csv = new System.Text.StringBuilder();
                    csv.AppendLine($"Rapport Profits et Pertes - {CompanyName}");
                    csv.AppendLine($"Période: {ReportPeriod}");
                    csv.AppendLine($"Généré le: {GeneratedDate:dd/MM/yyyy HH:mm}");
                    csv.AppendLine();

                    csv.AppendLine("REVENUS");
                    foreach (var item in RevenueItems)
                    {
                        csv.AppendLine($"{item.Description},{item.Amount:F2}");
                    }
                    csv.AppendLine($"TOTAL REVENUS,{TotalRevenue:F2}");
                    csv.AppendLine();

                    csv.AppendLine("COÛT DES VENTES");
                    foreach (var item in CostOfSalesItems)
                    {
                        csv.AppendLine($"{item.Description},{item.Amount:F2}");
                    }
                    csv.AppendLine($"TOTAL COÛT DES VENTES,{TotalCostOfSales:F2}");
                    csv.AppendLine();

                    csv.AppendLine($"MARGE BRUTE,{GrossMargin:F2}");
                    csv.AppendLine();

                    csv.AppendLine("CHARGES D'EXPLOITATION");
                    foreach (var item in OperatingExpenseItems)
                    {
                        csv.AppendLine($"{item.Description},{item.Amount:F2}");
                    }
                    csv.AppendLine($"TOTAL CHARGES D'EXPLOITATION,{TotalOperatingExpenses:F2}");
                    csv.AppendLine();

                    csv.AppendLine($"RÉSULTAT NET,{NetResult:F2}");

                    // Écrire le fichier
                    System.IO.File.WriteAllText(filePath, csv.ToString(), System.Text.Encoding.UTF8);

                    System.Threading.Thread.Sleep(1000); // Simuler le processus
                });

                MessageBoxHelper.ShowSuccess($"Rapport exporté avec succès sur le Bureau:\n{System.IO.Path.GetFileName($"Rapport_Profits_Pertes_{DateTime.Now:yyyyMMdd_HHmmss}.csv")}");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'export: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
                BusyMessage = string.Empty;
            }
        }

        private void PrintReport()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Préparation de l'impression...\n\nFonctionnalité d'impression complète en cours de développement.");
                // TODO: Implémenter l'impression avec PrintDialog
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'impression: {ex.Message}");
            }
        }

        private void ComparePeriods()
        {
            try
            {
                var message = "Comparaison avec les périodes précédentes:\n\n";
                message += $"vs Période précédente: {PreviousPeriodComparison:+0.0;-0.0}%\n";
                message += $"vs Même période année dernière: {YearOverYearComparison:+0.0;-0.0}%\n\n";
                message += "Analyse détaillée de comparaison en cours de développement.";

                MessageBoxHelper.ShowInfo(message);
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la comparaison: {ex.Message}");
            }
        }

        private void DrillDown()
        {
            try
            {
                var message = "Analyse détaillée disponible:\n\n";
                message += $"• Marge brute: {GrossMarginPercentage:F1}%\n";
                message += $"• Marge nette: {NetMarginPercentage:F1}%\n";
                message += $"• ROI: {ROI:F1}%\n\n";
                message += "Analyse approfondie en cours de développement.";

                MessageBoxHelper.ShowInfo(message);
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'analyse: {ex.Message}");
            }
        }

        private void ScheduleReport()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Programmation de rapport automatique:\n\n" +
                    "• Rapports quotidiens\n" +
                    "• Rapports hebdomadaires\n" +
                    "• Rapports mensuels\n" +
                    "• Rapports trimestriels\n\n" +
                    "Configuration avancée en cours de développement.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la programmation: {ex.Message}");
            }
        }
    }

    // Modèle pour les lignes de rapport
    public class ReportLineItem
    {
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public string Category { get; set; }
        public string SubCategory { get; set; }
    }
}
