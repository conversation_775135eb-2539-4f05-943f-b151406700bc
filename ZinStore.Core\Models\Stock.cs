using System;
using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour le stock des produits
    /// </summary>
    public class Stock : BaseEntity
    {
        [Required]
        public int ProduitId { get; set; }

        [Required]
        public decimal QuantiteDisponible { get; set; }

        public decimal QuantiteReservee { get; set; } = 0;

        public decimal QuantiteMinimum { get; set; } = 0;

        public decimal QuantiteMaximum { get; set; } = 0;

        public decimal CoutMoyenPondere { get; set; } = 0;

        public decimal ValeurStock { get; set; } = 0;

        public DateTime? DateDernierMouvement { get; set; }

        public DateTime? DateDernierInventaire { get; set; }

        [StringLength(100)]
        public string Emplacement { get; set; }

        public string Notes { get; set; }

        // Propriétés de navigation
        public virtual Produit Produit { get; set; }

        // Propriétés calculées
        public decimal QuantiteNette => QuantiteDisponible - QuantiteReservee;
        public bool EstEnRupture => QuantiteNette <= QuantiteMinimum;
        public bool EstSurstock => QuantiteDisponible >= QuantiteMaximum && QuantiteMaximum > 0;
    }
}
