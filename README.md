# ZinStore - Système de Gestion de Supermarché

## 📋 Description

ZinStore est un système complet de gestion de supermarché développé en C# avec WPF, conçu spécialement pour les entreprises algériennes. Le système offre une interface moderne en français et couvre tous les aspects de la gestion commerciale et comptable.

## 🚀 Fonctionnalités

### 🧾 Modules Principaux
- **Gestion des Ventes** - Facturation, point de vente, gestion des clients
- **Gestion des Achats** - Commandes fournisseurs, réception de marchandises
- **Gestion du Stock** - Inventaire, mouvements, alertes de rupture
- **Gestion des Clients** - Fichier client, comptes, historique
- **Gestion des Fournisseurs** - Fichier fournisseur, conditions commerciales
- **Gestion des Produits** - Catalogue, catégories, prix
- **Comptabilité Générale** - Écritures, comptes, bilan, compte de résultat
- **Rapports et Statistiques** - Tableaux de bord, analyses

### 🔐 Sécurité et Utilisateurs
- Système d'authentification sécurisé
- Gestion des rôles et permissions
- Traçabilité des opérations
- Sauvegarde automatique

### 🎨 Interface Utilisateur
- Interface moderne avec Material Design
- Entièrement en français
- Responsive et intuitive
- Thèmes personnalisables

## 🛠️ Technologies Utilisées

- **Framework**: .NET Framework 4.7.2
- **Interface**: WPF (Windows Presentation Foundation)
- **Design**: Material Design in XAML
- **Base de données**: SQLite
- **ORM**: Dapper
- **Architecture**: MVVM (Model-View-ViewModel)
- **Rapports**: FastReport.NET (à implémenter)

## 📁 Structure du Projet

```
ZinStore/
├── ZinStore.Core/          # Modèles et entités de base
├── ZinStore.Data/          # Accès aux données (Repositories, Context)
├── ZinStore.Business/      # Logique métier et services
├── ZinStore.UI/           # Interface utilisateur WPF
├── ZinStore.Reports/      # Génération de rapports
└── ZinStore.sln          # Solution Visual Studio
```

## 🔧 Installation et Configuration

### Prérequis
- Windows 7 ou supérieur
- .NET Framework 4.7.2 ou supérieur
- Visual Studio 2019 ou supérieur (pour le développement)

### Installation
1. Cloner le repository
2. Ouvrir `ZinStore.sln` dans Visual Studio
3. Restaurer les packages NuGet
4. Compiler la solution
5. Exécuter le projet `ZinStore.UI`

### Configuration
- La base de données SQLite est créée automatiquement au premier lancement
- Utilisateur par défaut: `admin` / `admin123`
- La configuration se trouve dans `App.config`

## 👥 Utilisateurs par Défaut

| Utilisateur | Mot de passe | Rôle |
|-------------|--------------|------|
| admin | admin123 | Administrateur |

⚠️ **Important**: Changez le mot de passe par défaut lors de la première utilisation.

## 📊 Base de Données

Le système utilise SQLite comme base de données locale avec les tables principales:
- Utilisateurs
- Clients
- Fournisseurs
- Produits et Catégories
- Ventes et Achats
- Stock et Mouvements
- Comptabilité

## 🔄 Développement

### Architecture MVVM
Le projet suit le pattern MVVM pour une séparation claire des responsabilités:
- **Models**: Entités métier (ZinStore.Core)
- **Views**: Interfaces XAML (ZinStore.UI/Views)
- **ViewModels**: Logique de présentation (ZinStore.UI/ViewModels)

### Services
- **AuthenticationService**: Gestion de l'authentification
- **ClientService**: Gestion des clients
- **ProduitService**: Gestion des produits
- **VenteService**: Gestion des ventes
- **StockService**: Gestion du stock

## 📈 Roadmap

### Version 1.0 (Actuelle)
- [x] Architecture de base
- [x] Authentification
- [x] Interface principale
- [x] Gestion des clients (basique)
- [ ] Gestion complète des produits
- [ ] Module de vente
- [ ] Gestion du stock

### Version 1.1 (Prochaine)
- [ ] Module d'achat complet
- [ ] Rapports avancés
- [ ] Sauvegarde/Restauration
- [ ] Import/Export Excel

### Version 2.0 (Future)
- [ ] Mode multi-magasins
- [ ] API REST
- [ ] Application mobile
- [ ] Synchronisation cloud

## 🤝 Contribution

Les contributions sont les bienvenues! Pour contribuer:
1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

Pour toute question ou support:
- Email: <EMAIL>
- Documentation: [Wiki du projet](https://github.com/zinstore/zinstore/wiki)
- Issues: [GitHub Issues](https://github.com/zinstore/zinstore/issues)

## 🙏 Remerciements

- Material Design in XAML pour les composants UI
- Dapper pour l'ORM léger
- SQLite pour la base de données embarquée
- La communauté .NET pour les ressources et l'aide

---

**ZinStore** - Votre partenaire pour la gestion de supermarché en Algérie 🇩🇿
