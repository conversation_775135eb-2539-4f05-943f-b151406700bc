using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class ProduitsViewModel : BaseViewModel
    {
        private readonly ProduitService _produitService;

        public ProduitsViewModel()
        {
            _produitService = new ProduitService(new DatabaseContext());

            Produits = new ObservableCollection<Produit>();

            AddProduitCommand = new RelayCommand(AddProduit);
            EditProduitCommand = new RelayCommand(EditProduit, CanEditProduit);
            DeleteProduitCommand = new RelayCommand(DeleteProduit, CanDeleteProduit);
            RefreshCommand = new RelayCommand(async () => await LoadProduitsAsync());
            ScanBarcodeCommand = new RelayCommand(ScanBarcode);
            SearchCommand = new RelayCommand(() => SearchProduits());

            _ = LoadProduitsAsync();
        }

        public ObservableCollection<Produit> Produits { get; }

        private Produit _selectedProduit;
        public Produit SelectedProduit
        {
            get => _selectedProduit;
            set => SetProperty(ref _selectedProduit, value);
        }

        private string _searchText;
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                SearchProduits();
            }
        }

        public bool IsEmpty => !IsBusy && !Produits.Any();

        public ICommand AddProduitCommand { get; }
        public ICommand EditProduitCommand { get; }
        public ICommand DeleteProduitCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand ScanBarcodeCommand { get; }
        public ICommand SearchCommand { get; }

        private async Task LoadProduitsAsync()
        {
            try
            {
                IsBusy = true;
                var produits = await _produitService.GetAllProduitsAsync();

                Produits.Clear();
                foreach (var produit in produits)
                {
                    Produits.Add(produit);
                }
                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des produits: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void SearchProduits()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadProduitsAsync();
                return;
            }

            try
            {
                IsBusy = true;
                var produits = await _produitService.SearchProduitsAsync(SearchText);

                Produits.Clear();
                foreach (var produit in produits)
                {
                    Produits.Add(produit);
                }
                OnPropertyChanged(nameof(IsEmpty));

                // Afficher un message si aucun résultat
                if (!produits.Any())
                {
                    MessageBoxHelper.ShowInfo($"Aucun produit trouvé pour la recherche: '{SearchText}'");
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddProduit()
        {
            var window = new Views.Produits.ProduitFormWindow();
            if (window.ShowDialog() == true && window.ProduitSaved)
            {
                _ = LoadProduitsAsync();
            }
        }

        private void EditProduit()
        {
            if (SelectedProduit == null) return;

            var window = new Views.Produits.ProduitFormWindow(SelectedProduit);
            if (window.ShowDialog() == true && window.ProduitSaved)
            {
                _ = LoadProduitsAsync();
            }
        }

        private async void DeleteProduit()
        {
            if (SelectedProduit == null) return;

            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir supprimer le produit {SelectedProduit.Nom} ?"))
            {
                try
                {
                    // TODO: Implémenter DeleteProduitAsync dans ProduitService
                    await Task.Delay(100); // Simulation async
                    MessageBoxHelper.ShowInfo("Fonctionnalité de suppression en cours de développement.");
                    // var result = await _produitService.DeleteProduitAsync(SelectedProduit.Id);
                    // if (result.Success)
                    // {
                    //     MessageBoxHelper.ShowSuccess(result.Message);
                    //     await LoadProduitsAsync();
                    // }
                    // else
                    // {
                    //     MessageBoxHelper.ShowError(result.Message);
                    // }
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la suppression: {ex.Message}");
                }
            }
        }

        private bool CanEditProduit()
        {
            return SelectedProduit != null;
        }

        private bool CanDeleteProduit()
        {
            return SelectedProduit != null;
        }

        private async void ScanBarcode()
        {
            try
            {
                // Simuler l'ouverture d'un scanner de code-barres
                var barcodeDialog = new Views.Common.BarcodeInputDialog();
                if (barcodeDialog.ShowDialog() == true)
                {
                    var scannedBarcode = barcodeDialog.ScannedBarcode;

                    if (!string.IsNullOrWhiteSpace(scannedBarcode))
                    {
                        // Rechercher le produit par code-barres
                        var produits = await _produitService.SearchProduitsAsync(scannedBarcode);
                        var produitTrouve = produits?.FirstOrDefault(p => p.CodeBarre == scannedBarcode);

                        if (produitTrouve != null)
                        {
                            // Sélectionner le produit trouvé
                            SelectedProduit = Produits.FirstOrDefault(p => p.Id == produitTrouve.Id);

                            // Filtrer la liste pour ne montrer que ce produit
                            Produits.Clear();
                            Produits.Add(produitTrouve);

                            MessageBoxHelper.ShowSuccess($"Produit trouvé: {produitTrouve.Nom}");
                        }
                        else
                        {
                            MessageBoxHelper.ShowWarning($"Aucun produit trouvé avec le code-barres: {scannedBarcode}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du scan: {ex.Message}");
            }
        }
    }
}
