-- =====================================================
-- Script d'insertion des données d'exemple ZinStore
-- Données de test pour développement et démonstration
-- Version: MySQL 8.0+
-- =====================================================

USE zinstore;

-- Désactiver les vérifications de clés étrangères temporairement
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. INSERTION DES UTILISATEURS
-- =====================================================

INSERT INTO utilisateurs (nom_utilisateur, mot_de_passe, nom_complet, email, telephone, role, est_actif) VALUES
('admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrateur Système', '<EMAIL>', '0555-000-001', 'Admin', TRUE),
('manager', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ahmed Benali', '<EMAIL>', '0555-000-002', 'Manager', TRUE),
('vendeur1', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Fatima Zohra', '<EMAIL>', '0555-000-003', 'Vendeur', TRUE),
('vendeur2', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mohamed Larbi', '<EMAIL>', '0555-000-004', 'Vendeur', TRUE),
('caissier1', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Leila Mansouri', '<EMAIL>', '0555-000-005', 'Caissier', TRUE);

-- =====================================================
-- 2. INSERTION DES CATÉGORIES
-- =====================================================

INSERT INTO categories (nom, description, code_categorie, est_active) VALUES
('Électronique', 'Appareils électroniques et accessoires', 'ELEC', TRUE),
('Informatique', 'Ordinateurs, smartphones et accessoires', 'INFO', TRUE),
('Électroménager', 'Appareils électroménagers pour la maison', 'ELME', TRUE),
('Mode & Vêtements', 'Vêtements et accessoires de mode', 'MODE', TRUE),
('Maison & Jardin', 'Articles pour la maison et le jardin', 'MAIS', TRUE),
('Sport & Loisirs', 'Articles de sport et loisirs', 'SPOR', TRUE),
('Beauté & Santé', 'Produits de beauté et de santé', 'BEAU', TRUE),
('Alimentation', 'Produits alimentaires et boissons', 'ALIM', TRUE),
('Automobile', 'Pièces et accessoires automobiles', 'AUTO', TRUE),
('Livres & Médias', 'Livres, films et musique', 'LIVR', TRUE);

-- =====================================================
-- 3. INSERTION DES FOURNISSEURS
-- =====================================================

INSERT INTO fournisseurs (nom, contact_nom, telephone, email, adresse, ville, numero_fournisseur, conditions_paiement, delai_livraison_jours, est_actif) VALUES
('TechnoPlus SARL', 'Karim Boudjelal', '021-456-789', '<EMAIL>', 'Zone Industrielle Rouiba', 'Alger', 'FOUR001', '30 jours fin de mois', 7, TRUE),
('ElectroMag Distribution', 'Nadia Hamidi', '031-789-123', '<EMAIL>', 'Rue Larbi Ben Mhidi', 'Constantine', 'FOUR002', '45 jours', 10, TRUE),
('ModeFashion Import', 'Yacine Messaoudi', '041-321-654', '<EMAIL>', 'Boulevard de la Soummam', 'Oran', 'FOUR003', '60 jours', 14, TRUE),
('AlimFresh Wholesale', 'Samira Benaissa', '025-147-258', '<EMAIL>', 'Marché de Gros Boufarik', 'Blida', 'FOUR004', '15 jours', 3, TRUE),
('AutoParts Algérie', 'Rachid Khelil', '038-963-741', '<EMAIL>', 'Zone Industrielle', 'Annaba', 'FOUR005', '30 jours', 5, TRUE);

-- =====================================================
-- 4. INSERTION DES CLIENTS
-- =====================================================

INSERT INTO clients (nom, prenom, telephone, email, adresse, ville, type_client, numero_client, credit_limite, est_actif) VALUES
('Benali', 'Ahmed', '0555-123-456', '<EMAIL>', '15 Rue Didouche Mourad', 'Alger', 'Particulier', 'CLI001', 5000.00, TRUE),
('Zohra', 'Fatima', '0666-789-123', '<EMAIL>', '25 Avenue de lIndépendance', 'Oran', 'Particulier', 'CLI002', 3000.00, TRUE),
('Khelil', 'Mohamed', '0777-456-789', '<EMAIL>', '10 Boulevard Boumediene', 'Constantine', 'Particulier', 'CLI003', 4000.00, TRUE),
('SARL TechSolutions', NULL, '021-555-000', '<EMAIL>', 'Cyber Parc Sidi Abdellah', 'Alger', 'Entreprise', 'CLI004', 50000.00, TRUE),
('Mansouri', 'Leila', '0555-987-654', '<EMAIL>', '5 Rue de la Révolution', 'Annaba', 'Particulier', 'CLI005', 2500.00, TRUE),
('EURL Commerce Plus', NULL, '031-444-555', '<EMAIL>', 'Zone dActivité Palma', 'Constantine', 'Entreprise', 'CLI006', 75000.00, TRUE),
('Boudjelal', 'Karim', '0666-321-987', '<EMAIL>', '30 Rue 8 Mai 1945', 'Sétif', 'Particulier', 'CLI007', 3500.00, TRUE),
('Hamidi', 'Nadia', '0777-159-753', '<EMAIL>', '12 Avenue Houari Boumediene', 'Blida', 'Particulier', 'CLI008', 4500.00, TRUE);

-- =====================================================
-- 5. INSERTION DES PRODUITS
-- =====================================================

INSERT INTO produits (nom, description, code_produit, code_barre, categorie_id, prix_achat, prix_vente, unite_mesure, quantite_par_unite, stock_minimum, stock_maximum, fournisseur_id, est_actif) VALUES
-- Électronique & Informatique
('Smartphone Samsung Galaxy A54', 'Smartphone 128GB, écran 6.4 pouces', 'SAMS-A54-128', '8801643880934', 2, 35000.00, 45000.00, 'Pièce', 1, 5, 50, 1, TRUE),
('Laptop HP Pavilion 15', 'Ordinateur portable Intel i5, 8GB RAM, 512GB SSD', 'HP-PAV-15-I5', '0194850123456', 2, 85000.00, 110000.00, 'Pièce', 1, 3, 20, 1, TRUE),
('Écouteurs Bluetooth JBL', 'Écouteurs sans fil avec étui de charge', 'JBL-BT-TUNE', '6925281954321', 1, 8500.00, 12000.00, 'Pièce', 1, 10, 100, 1, TRUE),
('Tablette Samsung Tab A8', 'Tablette 10.5 pouces, 64GB', 'SAMS-TAB-A8', '8801643765432', 2, 28000.00, 38000.00, 'Pièce', 1, 5, 30, 1, TRUE),

-- Électroménager
('Réfrigérateur LG 350L', 'Réfrigérateur No Frost, classe A++', 'LG-REF-350', '8806084123456', 3, 65000.00, 85000.00, 'Pièce', 1, 2, 10, 2, TRUE),
('Machine à laver Samsung 7kg', 'Lave-linge automatique 7kg', 'SAMS-LAV-7KG', '8801643234567', 3, 45000.00, 62000.00, 'Pièce', 1, 2, 8, 2, TRUE),
('Micro-ondes Panasonic 25L', 'Four micro-ondes 25 litres', 'PANA-MO-25L', '5025232123456', 3, 18000.00, 25000.00, 'Pièce', 1, 5, 25, 2, TRUE),

-- Mode & Vêtements
('T-shirt Homme Coton', 'T-shirt 100% coton, tailles S-XXL', 'TSHIRT-H-COT', '2001234567890', 4, 800.00, 1500.00, 'Pièce', 1, 20, 200, 3, TRUE),
('Jean Femme Slim', 'Jean slim femme, tailles 36-44', 'JEAN-F-SLIM', '2001234567891', 4, 2500.00, 4200.00, 'Pièce', 1, 15, 150, 3, TRUE),
('Chaussures Sport Nike', 'Baskets de sport, pointures 38-45', 'NIKE-SPORT-BAS', '0886737123456', 4, 8500.00, 14000.00, 'Pièce', 1, 10, 80, 3, TRUE),

-- Alimentation
('Huile dOlive 1L', 'Huile dolive extra vierge 1 litre', 'HUILE-OLIVE-1L', '6111234567890', 8, 450.00, 650.00, 'Pièce', 1, 50, 500, 4, TRUE),
('Riz Basmati 5kg', 'Riz Basmati premium 5kg', 'RIZ-BASM-5KG', '6111234567891', 8, 1200.00, 1800.00, 'Pièce', 1, 30, 300, 4, TRUE),
('Thé Vert 200g', 'Thé vert en vrac 200g', 'THE-VERT-200G', '6111234567892', 8, 350.00, 550.00, 'Pièce', 1, 40, 400, 4, TRUE),

-- Automobile
('Huile Moteur 5W30 4L', 'Huile moteur synthétique 5W30', 'HUILE-MOT-5W30', '3267025123456', 9, 2800.00, 4200.00, 'Pièce', 1, 20, 100, 5, TRUE),
('Filtre à Air Universel', 'Filtre à air pour véhicules', 'FILTRE-AIR-UNI', '3267025123457', 9, 850.00, 1400.00, 'Pièce', 1, 25, 150, 5, TRUE);

-- =====================================================
-- 6. INSERTION DES PARAMÈTRES SYSTÈME
-- =====================================================

INSERT INTO parametres (cle_parametre, valeur_parametre, type_parametre, categorie, description, est_modifiable) VALUES
('nom_entreprise', 'ZinStore', 'String', 'Général', 'Nom de lentreprise', TRUE),
('adresse_entreprise', '123 Rue de la République, Alger', 'String', 'Général', 'Adresse de lentreprise', TRUE),
('telephone_entreprise', '021-123-456', 'String', 'Général', 'Téléphone de lentreprise', TRUE),
('email_entreprise', '<EMAIL>', 'String', 'Général', 'Email de lentreprise', TRUE),
('taux_tva_defaut', '19.00', 'Decimal', 'Finance', 'Taux de TVA par défaut (%)', TRUE),
('devise', 'DA', 'String', 'Finance', 'Devise utilisée', TRUE),
('format_facture', 'FV{YYYY}{MM}{NNNN}', 'String', 'Vente', 'Format des numéros de facture', TRUE),
('format_commande', 'CMD{YYYY}{MM}{NNNN}', 'String', 'Achat', 'Format des numéros de commande', TRUE),
('stock_alerte_active', 'true', 'Boolean', 'Stock', 'Activer les alertes de stock', TRUE),
('sauvegarde_auto', 'true', 'Boolean', 'Système', 'Sauvegarde automatique', TRUE),
('delai_session_minutes', '480', 'Integer', 'Sécurité', 'Délai de session en minutes', TRUE),
('nombre_tentatives_connexion', '3', 'Integer', 'Sécurité', 'Nombre de tentatives de connexion', TRUE);

-- =====================================================
-- 7. INSERTION DU PLAN COMPTABLE DE BASE
-- =====================================================

INSERT INTO comptes_generaux (numero_compte, nom_compte, type_compte, sous_type, niveau, est_actif) VALUES
-- Classe 1 - Comptes de capitaux
('10', 'Capital et réserves', 'Capitaux', 'Capital', 1, TRUE),
('101', 'Capital social', 'Capitaux', 'Capital', 2, TRUE),
('106', 'Réserves', 'Capitaux', 'Réserves', 2, TRUE),

-- Classe 2 - Comptes dimmobilisations
('20', 'Immobilisations incorporelles', 'Actif', 'Immobilisations', 1, TRUE),
('21', 'Immobilisations corporelles', 'Actif', 'Immobilisations', 1, TRUE),
('213', 'Constructions', 'Actif', 'Immobilisations', 2, TRUE),
('2154', 'Matériel informatique', 'Actif', 'Immobilisations', 2, TRUE),

-- Classe 3 - Comptes de stocks
('30', 'Stocks de marchandises', 'Actif', 'Stocks', 1, TRUE),
('301', 'Marchandises', 'Actif', 'Stocks', 2, TRUE),

-- Classe 4 - Comptes de tiers
('40', 'Fournisseurs et comptes rattachés', 'Passif', 'Dettes', 1, TRUE),
('401', 'Fournisseurs', 'Passif', 'Dettes', 2, TRUE),
('41', 'Clients et comptes rattachés', 'Actif', 'Créances', 1, TRUE),
('411', 'Clients', 'Actif', 'Créances', 2, TRUE),

-- Classe 5 - Comptes financiers
('50', 'Valeurs mobilières de placement', 'Actif', 'Financier', 1, TRUE),
('51', 'Banques, établissements financiers', 'Actif', 'Financier', 1, TRUE),
('512', 'Banques', 'Actif', 'Financier', 2, TRUE),
('53', 'Caisse', 'Actif', 'Financier', 1, TRUE),
('531', 'Caisse', 'Actif', 'Financier', 2, TRUE),

-- Classe 6 - Comptes de charges
('60', 'Achats', 'Charge', 'Exploitation', 1, TRUE),
('607', 'Achats de marchandises', 'Charge', 'Exploitation', 2, TRUE),
('61', 'Services extérieurs', 'Charge', 'Exploitation', 1, TRUE),
('613', 'Locations', 'Charge', 'Exploitation', 2, TRUE),
('62', 'Autres services extérieurs', 'Charge', 'Exploitation', 1, TRUE),
('626', 'Frais postaux et télécommunications', 'Charge', 'Exploitation', 2, TRUE),

-- Classe 7 - Comptes de produits
('70', 'Ventes de produits fabriqués', 'Produit', 'Exploitation', 1, TRUE),
('707', 'Ventes de marchandises', 'Produit', 'Exploitation', 2, TRUE);

-- Réactiver les vérifications de clés étrangères
SET FOREIGN_KEY_CHECKS = 1;
