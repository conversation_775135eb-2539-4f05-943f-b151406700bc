<UserControl x:Class="ZinStore.UI.Views.Settings.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.DataContext>
        <viewModels:SettingsViewModel/>
    </UserControl.DataContext>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- En-tête -->
            <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Settings" 
                                               Width="32" Height="32"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="⚙️ Paramètres du Système"
                                  Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                  FontWeight="Bold"
                                  VerticalAlignment="Center"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                                  Margin="10,0,0,0"/>
                    </StackPanel>

                    <Button Grid.Column="2"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding SaveSettingsCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ContentSave" VerticalAlignment="Center"/>
                            <TextBlock Text="Sauvegarder" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </Grid>
            </materialDesign:Card>

            <!-- Contenu principal -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Colonne gauche -->
                <StackPanel Grid.Column="0">
                    <!-- Informations de l'entreprise -->
                    <materialDesign:Card Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="Informations de l'Entreprise" 
                                      FontWeight="Bold" 
                                      FontSize="16" 
                                      Margin="0,0,0,20"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                            <TextBox materialDesign:HintAssist.Hint="Nom de l'entreprise"
                                    Text="{Binding CompanyName}"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Margin="0,0,0,15"/>

                            <TextBox materialDesign:HintAssist.Hint="Adresse"
                                    Text="{Binding CompanyAddress}"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Height="60"
                                    TextWrapping="Wrap"
                                    AcceptsReturn="True"
                                    Margin="0,0,0,15"/>

                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox Grid.Column="0"
                                        materialDesign:HintAssist.Hint="Téléphone"
                                        Text="{Binding CompanyPhone}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <TextBox Grid.Column="2"
                                        materialDesign:HintAssist.Hint="Email"
                                        Text="{Binding CompanyEmail}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                            </Grid>

                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox Grid.Column="0"
                                        materialDesign:HintAssist.Hint="Registre de Commerce"
                                        Text="{Binding CompanyRC}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <TextBox Grid.Column="2"
                                        materialDesign:HintAssist.Hint="Numéro Fiscal"
                                        Text="{Binding CompanyTaxNumber}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Paramètres de vente -->
                    <materialDesign:Card Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="Paramètres de Vente" 
                                      FontWeight="Bold" 
                                      FontSize="16" 
                                      Margin="0,0,0,20"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBox Grid.Column="0"
                                        materialDesign:HintAssist.Hint="Taux TVA par défaut (%)"
                                        Text="{Binding DefaultTaxRate}"
                                        Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                                
                                <ComboBox Grid.Column="2"
                                         materialDesign:HintAssist.Hint="Devise"
                                         SelectedValue="{Binding DefaultCurrency}"
                                         Style="{StaticResource MaterialDesignOutlinedComboBox}">
                                    <ComboBoxItem Content="DA - Dinar Algérien" Tag="DA"/>
                                    <ComboBoxItem Content="EUR - Euro" Tag="EUR"/>
                                    <ComboBoxItem Content="USD - Dollar US" Tag="USD"/>
                                </ComboBox>
                            </Grid>

                            <CheckBox Content="Imprimer automatiquement les factures"
                                     IsChecked="{Binding AutoPrintInvoices}"
                                     Style="{StaticResource MaterialDesignCheckBox}"
                                     Margin="0,0,0,10"/>

                            <CheckBox Content="Demander confirmation avant suppression"
                                     IsChecked="{Binding ConfirmBeforeDelete}"
                                     Style="{StaticResource MaterialDesignCheckBox}"
                                     Margin="0,0,0,10"/>

                            <CheckBox Content="Alertes stock faible"
                                     IsChecked="{Binding LowStockAlerts}"
                                     Style="{StaticResource MaterialDesignCheckBox}"
                                     Margin="0,0,0,10"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Gestion des Caisses -->
                    <materialDesign:Card Padding="20">
                        <StackPanel>
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0"
                                          Text="Gestion des Caisses" 
                                          FontWeight="Bold" 
                                          FontSize="16" 
                                          Foreground="{DynamicResource PrimaryHueMidBrush}"
                                          VerticalAlignment="Center"/>
                                
                                <Button Grid.Column="1"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Command="{Binding AddCashRegisterCommand}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus" VerticalAlignment="Center"/>
                                        <TextBlock Text="Nouvelle Caisse" Margin="5,0,0,0"/>
                                    </StackPanel>
                                </Button>
                            </Grid>

                            <DataGrid ItemsSource="{Binding CashRegisters}"
                                     Style="{StaticResource CustomDataGrid}"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     MaxHeight="200">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="Nom" 
                                                      Binding="{Binding Name}" 
                                                      Width="150"/>
                                    <DataGridTextColumn Header="Solde Initial" 
                                                      Binding="{Binding InitialBalance, StringFormat='{}{0:F2} DA'}" 
                                                      Width="120"/>
                                    <DataGridTextColumn Header="Solde Actuel" 
                                                      Binding="{Binding CurrentBalance, StringFormat='{}{0:F2} DA'}" 
                                                      Width="120"/>
                                    <DataGridCheckBoxColumn Header="Active" 
                                                          Binding="{Binding IsActive}" 
                                                          Width="60"/>
                                    <DataGridTemplateColumn Header="Actions" Width="100">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.EditCashRegisterCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="Modifier">
                                                        <materialDesign:PackIcon Kind="Edit" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                                    </Button>
                                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                                           Command="{Binding DataContext.DeleteCashRegisterCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                           CommandParameter="{Binding}"
                                                           ToolTip="Supprimer">
                                                        <materialDesign:PackIcon Kind="Delete" Foreground="Red"/>
                                                    </Button>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>

                <!-- Colonne droite -->
                <StackPanel Grid.Column="2">
                    <!-- Paramètres d'affichage -->
                    <materialDesign:Card Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="Paramètres d'Affichage" 
                                      FontWeight="Bold" 
                                      FontSize="16" 
                                      Margin="0,0,0,20"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                            <ComboBox materialDesign:HintAssist.Hint="Thème"
                                     SelectedValue="{Binding SelectedTheme}"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     Margin="0,0,0,15">
                                <ComboBoxItem Content="Clair" Tag="Light"/>
                                <ComboBoxItem Content="Sombre" Tag="Dark"/>
                                <ComboBoxItem Content="Auto" Tag="Auto"/>
                            </ComboBox>

                            <ComboBox materialDesign:HintAssist.Hint="Langue"
                                     SelectedValue="{Binding SelectedLanguage}"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     Margin="0,0,0,15">
                                <ComboBoxItem Content="Français" Tag="fr"/>
                                <ComboBoxItem Content="العربية" Tag="ar"/>
                                <ComboBoxItem Content="English" Tag="en"/>
                            </ComboBox>

                            <TextBox materialDesign:HintAssist.Hint="Nombre d'éléments par page"
                                    Text="{Binding ItemsPerPage}"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Margin="0,0,0,15"/>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Sauvegarde et sécurité -->
                    <materialDesign:Card Padding="20" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="Sauvegarde et Sécurité" 
                                      FontWeight="Bold" 
                                      FontSize="16" 
                                      Margin="0,0,0,20"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                            <CheckBox Content="Sauvegarde automatique quotidienne"
                                     IsChecked="{Binding AutoBackup}"
                                     Style="{StaticResource MaterialDesignCheckBox}"
                                     Margin="0,0,0,15"/>

                            <TextBox materialDesign:HintAssist.Hint="Dossier de sauvegarde"
                                    Text="{Binding BackupPath}"
                                    Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                    Margin="0,0,0,15"/>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Command="{Binding CreateBackupCommand}"
                                   Margin="0,0,0,15">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabaseExport" VerticalAlignment="Center"/>
                                    <TextBlock Text="Créer Sauvegarde" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>

                            <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                                   Command="{Binding RestoreBackupCommand}">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="DatabaseImport" VerticalAlignment="Center"/>
                                    <TextBlock Text="Restaurer" Margin="5,0,0,0"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </materialDesign:Card>

                    <!-- Informations système -->
                    <materialDesign:Card Padding="20">
                        <StackPanel>
                            <TextBlock Text="Informations Système" 
                                      FontWeight="Bold" 
                                      FontSize="16" 
                                      Margin="0,0,0,20"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="Version:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding AppVersion}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="Base de données:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding DatabaseStatus}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="Dernière sauvegarde:" FontWeight="Bold" Margin="0,0,10,5"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding LastBackupDate, StringFormat='dd/MM/yyyy HH:mm'}" Margin="0,0,0,5"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="Espace disque:" FontWeight="Bold" Margin="0,0,10,0"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding DiskSpace}"/>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
