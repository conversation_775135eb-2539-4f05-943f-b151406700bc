using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.Views.Categories
{
    /// <summary>
    /// Logique d'interaction pour CategorieFormWindow.xaml
    /// </summary>
    public partial class CategorieFormWindow : Window
    {
        private readonly CategorieService _categorieService;
        private Categorie _currentCategorie;
        private bool _isEditMode;

        public bool CategorieSaved { get; private set; }

        public CategorieFormWindow(Categorie categorie = null)
        {
            InitializeComponent();
            _categorieService = new CategorieService(new DatabaseContext());
            
            if (categorie != null)
            {
                _currentCategorie = categorie;
                _isEditMode = true;
                TitleText.Text = "Modifier Catégorie";
                LoadCategorieData();
            }
            else
            {
                _currentCategorie = new Categorie();
                _isEditMode = false;
                TitleText.Text = "Nouvelle Catégorie";
                GenerateCategorieCode();
            }

            // Événements pour l'aperçu en temps réel
            NomTextBox.TextChanged += UpdatePreview;
            DescriptionTextBox.TextChanged += UpdatePreview;
            CouleurTextBox.TextChanged += UpdatePreview;
            IconeTextBox.TextChanged += UpdatePreview;

            UpdatePreview(null, null);
        }

        private void LoadCategorieData()
        {
            try
            {
                CodeCategorieTextBox.Text = _currentCategorie.CodeCategorie;
                NomTextBox.Text = _currentCategorie.Nom;
                DescriptionTextBox.Text = _currentCategorie.Description;
                CouleurTextBox.Text = _currentCategorie.Couleur ?? "#2196F3";
                IconeTextBox.Text = _currentCategorie.Icone ?? "FolderMultiple";
                OrdreAffichageTextBox.Text = _currentCategorie.OrdreAffichage.ToString();
                EstActiveCheckBox.IsChecked = _currentCategorie.EstActive;
                AfficherDansMenuCheckBox.IsChecked = _currentCategorie.AfficherDansMenu;
                AutoriserSousCategoriesCheckBox.IsChecked = _currentCategorie.AutoriserSousCategories;
                MotsClesTextBox.Text = _currentCategorie.MotsCles;
                NotesTextBox.Text = _currentCategorie.Notes;
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
        }

        private void GenerateCategorieCode()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                CodeCategorieTextBox.Text = $"CAT{timestamp.Substring(8)}";
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la génération du code: {ex.Message}");
            }
        }

        private void UpdatePreview(object sender, TextChangedEventArgs e)
        {
            try
            {
                // Mettre à jour le nom
                PreviewNom.Text = string.IsNullOrWhiteSpace(NomTextBox.Text) ? "Nom de la catégorie" : NomTextBox.Text;
                
                // Mettre à jour la description
                PreviewDescription.Text = string.IsNullOrWhiteSpace(DescriptionTextBox.Text) ? "Description de la catégorie" : DescriptionTextBox.Text;
                
                // Mettre à jour la couleur
                try
                {
                    if (!string.IsNullOrWhiteSpace(CouleurTextBox.Text))
                    {
                        var color = (System.Windows.Media.SolidColorBrush)new System.Windows.Media.BrushConverter().ConvertFromString(CouleurTextBox.Text);
                        PreviewIcon.Foreground = color;
                    }
                }
                catch
                {
                    // Couleur invalide, garder la couleur par défaut
                }

                // Mettre à jour l'icône
                try
                {
                    if (!string.IsNullOrWhiteSpace(IconeTextBox.Text))
                    {
                        if (Enum.TryParse<MaterialDesignThemes.Wpf.PackIconKind>(IconeTextBox.Text, out var iconKind))
                        {
                            PreviewIcon.Kind = iconKind;
                        }
                    }
                }
                catch
                {
                    // Icône invalide, garder l'icône par défaut
                }
            }
            catch
            {
                // Ignorer les erreurs de mise à jour de l'aperçu
            }
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                ShowProgress();

                // Remplir l'objet catégorie avec les données du formulaire
                _currentCategorie.CodeCategorie = CodeCategorieTextBox.Text.Trim();
                _currentCategorie.Nom = NomTextBox.Text.Trim();
                _currentCategorie.Description = DescriptionTextBox.Text.Trim();
                _currentCategorie.Couleur = CouleurTextBox.Text.Trim();
                _currentCategorie.Icone = IconeTextBox.Text.Trim();
                _currentCategorie.EstActive = EstActiveCheckBox.IsChecked ?? true;
                _currentCategorie.AfficherDansMenu = AfficherDansMenuCheckBox.IsChecked ?? true;
                _currentCategorie.AutoriserSousCategories = AutoriserSousCategoriesCheckBox.IsChecked ?? false;
                _currentCategorie.MotsCles = MotsClesTextBox.Text.Trim();
                _currentCategorie.Notes = NotesTextBox.Text.Trim();

                // Convertir l'ordre d'affichage
                if (int.TryParse(OrdreAffichageTextBox.Text, out int ordre))
                    _currentCategorie.OrdreAffichage = ordre;

                // Sauvegarder
                bool success;
                string message;
                if (_isEditMode)
                {
                    var result = await _categorieService.UpdateCategorieAsync(_currentCategorie);
                    success = result.Success;
                    message = result.Message;
                }
                else
                {
                    _currentCategorie.DateCreation = DateTime.Now;
                    var result = await _categorieService.AddCategorieAsync(_currentCategorie);
                    success = result.Success;
                    message = result.Message;
                }

                HideProgress();

                if (success)
                {
                    CategorieSaved = true;
                    MessageBoxHelper.ShowSuccess(message);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBoxHelper.ShowError(message);
                }
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBoxHelper.ShowError($"Erreur lors de l'enregistrement: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // Vérifier les champs obligatoires
            if (string.IsNullOrWhiteSpace(CodeCategorieTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("Le code catégorie est obligatoire.");
                CodeCategorieTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(NomTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("Le nom de la catégorie est obligatoire.");
                NomTextBox.Focus();
                return false;
            }

            // Valider la couleur
            if (!string.IsNullOrWhiteSpace(CouleurTextBox.Text))
            {
                try
                {
                    var color = (System.Windows.Media.SolidColorBrush)new System.Windows.Media.BrushConverter().ConvertFromString(CouleurTextBox.Text);
                }
                catch
                {
                    MessageBoxHelper.ShowWarning("La couleur spécifiée n'est pas valide. Utilisez un format comme #FF5722.");
                    CouleurTextBox.Focus();
                    return false;
                }
            }

            // Valider l'ordre d'affichage
            if (!string.IsNullOrWhiteSpace(OrdreAffichageTextBox.Text))
            {
                if (!int.TryParse(OrdreAffichageTextBox.Text, out _))
                {
                    MessageBoxHelper.ShowWarning("L'ordre d'affichage doit être un nombre entier.");
                    OrdreAffichageTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ShowProgress()
        {
            ProgressOverlay.Visibility = Visibility.Visible;
            SaveButton.IsEnabled = false;
        }

        private void HideProgress()
        {
            ProgressOverlay.Visibility = Visibility.Collapsed;
            SaveButton.IsEnabled = true;
        }
    }
}
