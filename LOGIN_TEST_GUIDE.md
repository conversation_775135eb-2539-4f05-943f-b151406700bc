# دليل اختبار تسجيل الدخول - ZinStore

## 🔐 معلومات تسجيل الدخول

### **المستخدم الافتراضي:**
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 🚀 خطوات الاختبار

### **1. تشغيل التطبيق:**
```bash
dotnet run --project ZinStore.UI
```

### **2. نافذة تسجيل الدخول:**
- ستظهر نافذة تسجيل الدخول تلقائياً
- أدخل البيانات التالية:
  - **اسم المستخدم**: admin
  - **كلمة المرور**: admin123

### **3. النقر على زر "Se connecter"**

## 🔍 التشخيص

### **إذا لم يعمل تسجيل الدخول:**

#### **1. تحقق من قاعدة البيانات:**
```bash
dotnet run --project ZinStore.DatabaseTool test .\Data\ZinStore.db
```

#### **2. تحقق من وجود المستخدم:**
```bash
dotnet run --project ZinStore.DatabaseTool info .\Data\ZinStore.db
```

#### **3. إعادة إنشاء قاعدة البيانات:**
```bash
# حذف قاعدة البيانات الحالية
del .\Data\ZinStore.db

# إنشاء قاعدة بيانات جديدة
dotnet run --project ZinStore.DatabaseTool create .\Data\ZinStore.db
```

## 🛠️ استكشاف الأخطاء

### **رسائل الخطأ الشائعة:**

#### **"Nom d'utilisateur ou mot de passe incorrect"**
- تأكد من كتابة `admin` و `admin123` بدقة
- تأكد من عدم وجود مسافات إضافية

#### **"Erreur de connexion"**
- تحقق من وجود ملف قاعدة البيانات في `.\Data\ZinStore.db`
- تحقق من صلاحيات الوصول للملف

#### **التطبيق لا يستجيب**
- أغلق التطبيق وأعد تشغيله
- تحقق من وجود عمليات أخرى تستخدم قاعدة البيانات

## 📊 معلومات تقنية

### **تشفير كلمة المرور:**
- يتم استخدام SHA256 مع salt: "ZinStore2024!@#"
- كلمة المرور "admin123" تصبح hash طويل

### **ملفات قاعدة البيانات:**
- **الموقع**: `.\Data\ZinStore.db`
- **النوع**: SQLite
- **الحجم المتوقع**: ~100 KB

### **نظام ملف الاتصال الاحتياطي:**
- **ملف الاتصال**: `connection.txt`
- **ملف النسخ الاحتياطي**: `connection_backup.txt`

## 🎯 النتائج المتوقعة

### **عند نجاح تسجيل الدخول:**
1. إغلاق نافذة تسجيل الدخول
2. فتح النافذة الرئيسية
3. عرض اسم المستخدم في الشريط العلوي
4. إظهار لوحة التحكم الرئيسية

### **عند فشل تسجيل الدخول:**
1. عرض رسالة خطأ باللون الأحمر
2. بقاء نافذة تسجيل الدخول مفتوحة
3. إمكانية المحاولة مرة أخرى

## 🔧 إعادة تعيين النظام

### **إذا كان هناك مشاكل مستمرة:**

#### **1. حذف جميع الملفات:**
```bash
del .\Data\ZinStore.db
del connection.txt
del connection_backup.txt
```

#### **2. إعادة بناء المشروع:**
```bash
dotnet clean
dotnet build
```

#### **3. إنشاء قاعدة بيانات جديدة:**
```bash
dotnet run --project ZinStore.DatabaseTool create .\Data\ZinStore.db
```

#### **4. تشغيل التطبيق:**
```bash
dotnet run --project ZinStore.UI
```

## 📝 ملاحظات مهمة

- **كلمة المرور حساسة للحالة**: تأكد من كتابة `admin123` بأحرف صغيرة
- **اسم المستخدم حساس للحالة**: تأكد من كتابة `admin` بأحرف صغيرة
- **لا تضع مسافات** قبل أو بعد اسم المستخدم أو كلمة المرور

## 🎉 بعد نجاح تسجيل الدخول

### **يمكنك الوصول إلى:**
- **لوحة التحكم**: عرض الإحصائيات
- **إدارة العملاء**: إضافة وتعديل العملاء
- **إدارة المنتجات**: إدارة المخزون
- **المبيعات**: تسجيل المبيعات
- **التقارير**: عرض التقارير المالية
- **الإعدادات**: تكوين النظام

### **اختبار نظام ملف الاتصال:**
1. اذهب إلى **Paramètres** → **Configuration Base de Données**
2. افتح قسم **"Fichier de Connexion de Secours"**
3. جرب الأزرار المختلفة:
   - **Charger depuis Fichier**
   - **Sauvegarder vers Fichier**
   - **Info Fichier**

**النظام الآن جاهز للاستخدام! 🚀**
