using System;
using System.Threading.Tasks;
using System.Windows;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.Views.Clients
{
    /// <summary>
    /// Logique d'interaction pour ClientFormWindow.xaml
    /// </summary>
    public partial class ClientFormWindow : Window
    {
        private readonly ClientService _clientService;
        private Client _currentClient;
        private bool _isEditMode;

        public bool ClientSaved { get; private set; }

        public ClientFormWindow(Client client = null)
        {
            InitializeComponent();
            _clientService = new ClientService(new DatabaseContext());

            if (client != null)
            {
                _currentClient = client;
                _isEditMode = true;
                TitleText.Text = "Modifier Client";
                LoadClientData();
            }
            else
            {
                _currentClient = new Client();
                _isEditMode = false;
                TitleText.Text = "Nouveau Client";
                GenerateClientCode();
            }
        }

        private void LoadClientData()
        {
            try
            {
                CodeClientTextBox.Text = _currentClient.CodeClient;
                NomTextBox.Text = _currentClient.Nom;
                PrenomTextBox.Text = _currentClient.Prenom;
                TelephoneTextBox.Text = _currentClient.Telephone;
                EmailTextBox.Text = _currentClient.Email;
                AdresseTextBox.Text = _currentClient.Adresse;
                VilleTextBox.Text = _currentClient.Ville;
                CodePostalTextBox.Text = _currentClient.CodePostal;
                SoldeCompteTextBox.Text = _currentClient.SoldeCompte.ToString("F2");
                LimiteCreditTextBox.Text = _currentClient.LimiteCredit.ToString("F2");
                EstActifCheckBox.IsChecked = _currentClient.EstActif;
                NotesTextBox.Text = _currentClient.Notes;
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
        }

        private void GenerateClientCode()
        {
            try
            {
                // Générer un code client automatique
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                CodeClientTextBox.Text = $"CL{timestamp.Substring(8)}";
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la génération du code: {ex.Message}");
            }
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                ShowProgress();

                // Remplir l'objet client avec les données du formulaire
                _currentClient.CodeClient = CodeClientTextBox.Text.Trim();
                _currentClient.Nom = NomTextBox.Text.Trim();
                _currentClient.Prenom = PrenomTextBox.Text.Trim();
                _currentClient.Telephone = TelephoneTextBox.Text.Trim();
                _currentClient.Email = EmailTextBox.Text.Trim();
                _currentClient.Adresse = AdresseTextBox.Text.Trim();
                _currentClient.Ville = VilleTextBox.Text.Trim();
                _currentClient.CodePostal = CodePostalTextBox.Text.Trim();
                _currentClient.EstActif = EstActifCheckBox.IsChecked ?? true;
                _currentClient.Notes = NotesTextBox.Text.Trim();

                // Convertir les montants
                if (decimal.TryParse(SoldeCompteTextBox.Text, out decimal solde))
                    _currentClient.SoldeCompte = solde;

                if (decimal.TryParse(LimiteCreditTextBox.Text, out decimal limite))
                    _currentClient.LimiteCredit = limite;

                // Sauvegarder
                bool success;
                string message;
                if (_isEditMode)
                {
                    var result = await _clientService.UpdateClientAsync(_currentClient);
                    success = result.Success;
                    message = result.Message;
                }
                else
                {
                    _currentClient.DateCreation = DateTime.Now;
                    var result = await _clientService.AddClientAsync(_currentClient);
                    success = result.Success;
                    message = result.Message;
                }

                HideProgress();

                if (success)
                {
                    ClientSaved = true;
                    MessageBoxHelper.ShowSuccess(message);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBoxHelper.ShowError(message);
                }
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBoxHelper.ShowError($"Erreur lors de l'enregistrement: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // Vérifier les champs obligatoires
            if (string.IsNullOrWhiteSpace(CodeClientTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("Le code client est obligatoire.");
                CodeClientTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(NomTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("Le nom est obligatoire.");
                NomTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(PrenomTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("Le prénom est obligatoire.");
                PrenomTextBox.Focus();
                return false;
            }

            // Valider l'email si fourni
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                if (!IsValidEmail(EmailTextBox.Text))
                {
                    MessageBoxHelper.ShowWarning("L'adresse email n'est pas valide.");
                    EmailTextBox.Focus();
                    return false;
                }
            }

            // Valider les montants
            if (!string.IsNullOrWhiteSpace(SoldeCompteTextBox.Text))
            {
                if (!decimal.TryParse(SoldeCompteTextBox.Text, out _))
                {
                    MessageBoxHelper.ShowWarning("Le solde du compte doit être un nombre valide.");
                    SoldeCompteTextBox.Focus();
                    return false;
                }
            }

            if (!string.IsNullOrWhiteSpace(LimiteCreditTextBox.Text))
            {
                if (!decimal.TryParse(LimiteCreditTextBox.Text, out _))
                {
                    MessageBoxHelper.ShowWarning("La limite de crédit doit être un nombre valide.");
                    LimiteCreditTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ShowProgress()
        {
            ProgressOverlay.Visibility = Visibility.Visible;
            SaveButton.IsEnabled = false;
        }

        private void HideProgress()
        {
            ProgressOverlay.Visibility = Visibility.Collapsed;
            SaveButton.IsEnabled = true;
        }
    }
}
