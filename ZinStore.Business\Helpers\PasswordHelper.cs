using System;
using System.Security.Cryptography;
using System.Text;

namespace ZinStore.Business.Helpers
{
    /// <summary>
    /// Classe d'aide pour la gestion des mots de passe
    /// </summary>
    public static class PasswordHelper
    {
        /// <summary>
        /// Hache un mot de passe en utilisant SHA256
        /// </summary>
        public static string HashPassword(string password)
        {
            if (string.IsNullOrEmpty(password))
                return string.Empty;

            using (SHA256 sha256Hash = SHA256.Create())
            {
                // Ajouter un salt pour plus de sécurité
                string saltedPassword = password + "ZinStore2024!@#";
                
                // Calculer le hash
                byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));

                // Convertir en string hexadécimal
                StringBuilder builder = new StringBuilder();
                for (int i = 0; i < bytes.Length; i++)
                {
                    builder.Append(bytes[i].ToString("x2"));
                }
                return builder.ToString();
            }
        }

        /// <summary>
        /// Vérifie si un mot de passe correspond au hash
        /// </summary>
        public static bool VerifyPassword(string password, string hash)
        {
            string hashedPassword = HashPassword(password);
            return string.Equals(hashedPassword, hash, StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Génère un mot de passe aléatoire
        /// </summary>
        public static string GenerateRandomPassword(int length = 8)
        {
            const string validChars = "ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
            StringBuilder sb = new StringBuilder();
            Random random = new Random();

            for (int i = 0; i < length; i++)
            {
                sb.Append(validChars[random.Next(validChars.Length)]);
            }

            return sb.ToString();
        }

        /// <summary>
        /// Évalue la force d'un mot de passe
        /// </summary>
        public static PasswordStrength EvaluatePasswordStrength(string password)
        {
            if (string.IsNullOrEmpty(password))
                return PasswordStrength.VeryWeak;

            int score = 0;

            // Longueur
            if (password.Length >= 8) score++;
            if (password.Length >= 12) score++;

            // Contient des minuscules
            if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[a-z]"))
                score++;

            // Contient des majuscules
            if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[A-Z]"))
                score++;

            // Contient des chiffres
            if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[0-9]"))
                score++;

            // Contient des caractères spéciaux
            if (System.Text.RegularExpressions.Regex.IsMatch(password, @"[!@#$%^&*()_+\-=\[\]{};':""\\|,.<>\/?]"))
                score++;

            switch (score)
            {
                case 0:
                case 1:
                    return PasswordStrength.VeryWeak;
                case 2:
                    return PasswordStrength.Weak;
                case 3:
                case 4:
                    return PasswordStrength.Medium;
                case 5:
                    return PasswordStrength.Strong;
                case 6:
                default:
                    return PasswordStrength.VeryStrong;
            }
        }
    }

    /// <summary>
    /// Énumération pour la force du mot de passe
    /// </summary>
    public enum PasswordStrength
    {
        VeryWeak,
        Weak,
        Medium,
        Strong,
        VeryStrong
    }
}
