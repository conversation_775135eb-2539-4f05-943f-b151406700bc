using System;
using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les produits
    /// </summary>
    public class Produit : BaseEntity
    {
        [Required(ErrorMessage = "Le code produit est obligatoire")]
        [StringLength(50, ErrorMessage = "Le code produit ne peut pas dépasser 50 caractères")]
        public string CodeProduit { get; set; }

        [StringLength(50)]
        public string CodeBarre { get; set; }

        [Required(ErrorMessage = "Le nom du produit est obligatoire")]
        [StringLength(200, ErrorMessage = "Le nom du produit ne peut pas dépasser 200 caractères")]
        public string Nom { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        [Required]
        public int CategorieId { get; set; }

        public int? FournisseurId { get; set; }

        [StringLength(50)]
        public string Unite { get; set; } = "Pièce";

        [Required]
        public decimal PrixAchat { get; set; }

        [Required]
        public decimal PrixVente { get; set; }

        public decimal PrixVenteGros { get; set; }

        public decimal TauxTVA { get; set; } = 19; // TVA par défaut en Algérie

        public decimal StockMinimum { get; set; } = 0;

        public decimal StockMaximum { get; set; } = 0;

        public decimal StockActuel { get; set; } = 0;

        public decimal StockReserve { get; set; } = 0;

        public bool EstActif { get; set; } = true;

        public bool EstPerissable { get; set; } = false;

        public int DureeConservation { get; set; } = 0; // en jours

        public string Image { get; set; }

        public decimal Poids { get; set; } = 0;

        public string Dimensions { get; set; }

        public string Emplacement { get; set; }

        public string Notes { get; set; }

        // Propriétés de navigation
        public virtual Categorie Categorie { get; set; }
        public virtual Fournisseur Fournisseur { get; set; }

        // Propriétés calculées
        public decimal MargeBeneficiaire => PrixVente - PrixAchat;
        public decimal PourcentageMarge => PrixAchat > 0 ? ((PrixVente - PrixAchat) / PrixAchat) * 100 : 0;
        public decimal StockDisponible => StockActuel - StockReserve;
        public bool EstEnRupture => StockDisponible <= StockMinimum;

        // Propriétés supplémentaires pour l'interface utilisateur
        public decimal ValeurStock { get; set; }
        public bool IsEnRupture { get; set; }
        public bool IsStockFaible { get; set; }
        public string NomCategorie { get; set; }
        public DateTime? DerniereMiseAJour { get; set; }
    }
}
