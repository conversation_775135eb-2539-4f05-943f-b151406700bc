using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;

namespace ZinStore.Core.Services.Interfaces
{
    public interface IAchatService
    {
        Task<IEnumerable<Achat>> GetAllAchatsAsync();
        Task<Achat> GetAchatByIdAsync(int id);
        Task<IEnumerable<Achat>> SearchAchatsAsync(string searchText, DateTime? dateDebut = null, DateTime? dateFin = null);
        Task<IEnumerable<Achat>> GetAchatsByFournisseurAsync(int fournisseurId);
        Task<IEnumerable<Achat>> GetAchatsByPeriodAsync(DateTime dateDebut, DateTime dateFin);
        Task<Achat> CreateAchatAsync(Achat achat);
        Task<Achat> UpdateAchatAsync(Achat achat);
        Task<bool> DeleteAchatAsync(int id);
        Task<string> GenerateNumeroAchatAsync();
        Task<decimal> GetTotalAchatsAsync(DateTime? dateDebut = null, DateTime? dateFin = null);
        Task<IEnumerable<Achat>> GetAchatsEnAttenteAsync();
        Task<bool> MarquerCommeRecuAsync(int achatId);
        Task<bool> AnnulerAchatAsync(int achatId);
    }
}
