using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.Views.Ventes
{
    /// <summary>
    /// Logique d'interaction pour VenteFormWindow.xaml
    /// </summary>
    public partial class VenteFormWindow : Window
    {
        private readonly VenteService _venteService;
        private readonly ClientService _clientService;
        private readonly ProduitService _produitService;
        private ObservableCollection<LigneVente> _lignesVente;
        private Vente _currentVente;

        public bool VenteSaved { get; private set; }

        public VenteFormWindow()
        {
            InitializeComponent();
            _venteService = new VenteService(new DatabaseContext());
            _clientService = new ClientService(new DatabaseContext());
            _produitService = new ProduitService(new DatabaseContext());
            
            _lignesVente = new ObservableCollection<LigneVente>();
            ArticlesDataGrid.ItemsSource = _lignesVente;
            
            _currentVente = new Vente();
            
            InitializeForm();
            LoadClients();
        }

        private void InitializeForm()
        {
            // Générer numéro de facture
            GenerateNumeroFacture();
            
            // Définir la date par défaut
            DateVenteDatePicker.SelectedDate = DateTime.Now;
            
            // Événements pour recalculer les totaux
            _lignesVente.CollectionChanged += (s, e) => CalculerTotaux();
        }

        private void GenerateNumeroFacture()
        {
            var today = DateTime.Now;
            var numero = $"FAC-{today:yyyyMMdd}-{today:HHmmss}";
            NumeroFactureTextBlock.Text = numero;
        }

        private async void LoadClients()
        {
            try
            {
                var clients = await _clientService.GetAllClientsAsync();
                ClientComboBox.ItemsSource = clients;
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des clients: {ex.Message}");
            }
        }

        private void NouveauClient_Click(object sender, RoutedEventArgs e)
        {
            var clientWindow = new Clients.ClientFormWindow();
            if (clientWindow.ShowDialog() == true && clientWindow.ClientSaved)
            {
                LoadClients();
            }
        }

        private async void RechercherProduit_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                await RechercherEtAjouterProduit();
            }
        }

        private async void RechercherProduit_Click(object sender, RoutedEventArgs e)
        {
            await RechercherEtAjouterProduit();
        }

        private async Task RechercherEtAjouterProduit()
        {
            try
            {
                string recherche = RechercherProduitTextBox.Text.Trim();
                if (string.IsNullOrEmpty(recherche))
                    return;

                var produits = await _produitService.SearchProduitsAsync(recherche);
                var produit = produits.FirstOrDefault();

                if (produit != null)
                {
                    AjouterProduitALaVente(produit);
                    RechercherProduitTextBox.Clear();
                }
                else
                {
                    MessageBoxHelper.ShowWarning("Aucun produit trouvé avec ce critère de recherche.");
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void AjouterArticle_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Ouvrir une fenêtre de sélection de produits
            MessageBoxHelper.ShowInfo("Fonctionnalité de sélection de produits en cours de développement.");
        }

        private void AjouterProduitALaVente(Produit produit)
        {
            // Vérifier si le produit existe déjà dans la liste
            var ligneExistante = _lignesVente.FirstOrDefault(l => l.ProduitId == produit.Id);
            
            if (ligneExistante != null)
            {
                // Augmenter la quantité
                ligneExistante.Quantite += 1;
                ligneExistante.CalculerTotal();
            }
            else
            {
                // Ajouter nouvelle ligne
                var nouvelleLigne = new LigneVente
                {
                    ProduitId = produit.Id,
                    CodeProduit = produit.CodeProduit,
                    NomProduit = produit.Nom,
                    PrixUnitaire = produit.PrixVente,
                    Quantite = 1,
                    PourcentageRemise = 0,
                    TauxTVA = produit.TauxTVA
                };
                nouvelleLigne.CalculerTotal();
                _lignesVente.Add(nouvelleLigne);
            }

            CalculerTotaux();
        }

        private void SupprimerArticle_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is LigneVente ligne)
            {
                _lignesVente.Remove(ligne);
                CalculerTotaux();
            }
        }

        private void CalculerTotaux()
        {
            decimal sousTotalHT = _lignesVente.Sum(l => l.TotalHT);
            decimal remiseTotale = _lignesVente.Sum(l => l.MontantRemise);
            decimal totalHT = sousTotalHT - remiseTotale;
            decimal tva = _lignesVente.Sum(l => l.MontantTVA);
            decimal totalTTC = totalHT + tva;

            SousTotalHTTextBlock.Text = $"{sousTotalHT:F2} DA";
            RemiseTotaleTextBlock.Text = $"{remiseTotale:F2} DA";
            TVATextBlock.Text = $"{tva:F2} DA";
            TotalTTCTextBlock.Text = $"{totalTTC:F2} DA";
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            await SaveVente(false);
        }

        private async void SaveAndPrint_Click(object sender, RoutedEventArgs e)
        {
            await SaveVente(true);
        }

        private async Task SaveVente(bool imprimer)
        {
            try
            {
                if (!ValidateForm())
                    return;

                ShowProgress();

                // Préparer la vente
                _currentVente.NumeroFacture = NumeroFactureTextBlock.Text;
                _currentVente.DateVente = DateVenteDatePicker.SelectedDate ?? DateTime.Now;
                _currentVente.ClientId = (int)ClientComboBox.SelectedValue;
                _currentVente.ModePaiement = ModePaiementComboBox.Text;
                _currentVente.Notes = NotesTextBox.Text.Trim();
                _currentVente.StatutVente = "Validée";

                // Calculer les totaux
                _currentVente.MontantHT = _lignesVente.Sum(l => l.TotalHT) - _lignesVente.Sum(l => l.MontantRemise);
                _currentVente.MontantTVA = _lignesVente.Sum(l => l.MontantTVA);
                _currentVente.MontantTotal = _currentVente.MontantHT + _currentVente.MontantTVA;
                _currentVente.MontantRemise = _lignesVente.Sum(l => l.MontantRemise);

                // Ajouter les lignes de vente
                _currentVente.LignesVente = _lignesVente.Select(l => new ZinStore.Core.Models.LigneVente
                {
                    ProduitId = l.ProduitId,
                    CodeProduit = l.CodeProduit,
                    NomProduit = l.NomProduit,
                    PrixUnitaire = l.PrixUnitaire,
                    Quantite = l.Quantite,
                    PourcentageRemise = l.PourcentageRemise,
                    MontantRemise = l.MontantRemise,
                    TauxTVA = l.TauxTVA,
                    MontantTVA = l.MontantTVA,
                    TotalHT = l.TotalHT
                }).ToList();

                // Sauvegarder
                var result = await _venteService.AddVenteAsync(_currentVente);

                HideProgress();

                if (result.Success)
                {
                    VenteSaved = true;
                    MessageBoxHelper.ShowSuccess(result.Message);

                    if (imprimer)
                    {
                        // TODO: Implémenter l'impression
                        MessageBoxHelper.ShowInfo("Fonctionnalité d'impression en cours de développement.");
                    }

                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBoxHelper.ShowError(result.Message);
                }
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBoxHelper.ShowError($"Erreur lors de l'enregistrement: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            if (ClientComboBox.SelectedValue == null)
            {
                MessageBoxHelper.ShowWarning("Veuillez sélectionner un client.");
                ClientComboBox.Focus();
                return false;
            }

            if (_lignesVente.Count == 0)
            {
                MessageBoxHelper.ShowWarning("Veuillez ajouter au moins un article à la vente.");
                RechercherProduitTextBox.Focus();
                return false;
            }

            if (DateVenteDatePicker.SelectedDate == null)
            {
                MessageBoxHelper.ShowWarning("Veuillez sélectionner une date de vente.");
                DateVenteDatePicker.Focus();
                return false;
            }

            return true;
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ShowProgress()
        {
            ProgressOverlay.Visibility = Visibility.Visible;
            SaveButton.IsEnabled = false;
        }

        private void HideProgress()
        {
            ProgressOverlay.Visibility = Visibility.Collapsed;
            SaveButton.IsEnabled = true;
        }
    }

    // Classe pour les lignes de vente dans le DataGrid
    public class LigneVente
    {
        public int ProduitId { get; set; }
        public string CodeProduit { get; set; }
        public string NomProduit { get; set; }
        public decimal PrixUnitaire { get; set; }
        public decimal Quantite { get; set; }
        public decimal PourcentageRemise { get; set; }
        public decimal TauxTVA { get; set; }
        public decimal TotalHT { get; set; }
        public decimal MontantRemise { get; set; }
        public decimal MontantTVA { get; set; }

        public void CalculerTotal()
        {
            decimal sousTotal = PrixUnitaire * Quantite;
            MontantRemise = sousTotal * (PourcentageRemise / 100);
            TotalHT = sousTotal - MontantRemise;
            MontantTVA = TotalHT * (TauxTVA / 100);
        }
    }
}
