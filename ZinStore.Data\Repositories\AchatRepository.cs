using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class AchatRepository : BaseRepository<Achat>
    {
        public AchatRepository(DatabaseContext context) : base(context, "Achats")
        {
        }

        public override async Task<IEnumerable<Achat>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT a.*, f.Nom as FournisseurNom, u.NomComplet as UtilisateurNom
                FROM Achats a
                LEFT JOIN Fournisseurs f ON a.FournisseurId = f.Id
                LEFT JOIN Utilisateurs u ON a.UtilisateurId = u.Id
                WHERE a.EstSupprime = 0 
                AND (a.NumeroFacture LIKE @SearchTerm OR f.Nom LIKE @SearchTerm)
                ORDER BY a.DateAchat DESC";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Achat>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<string> GenerateNextNumeroFactureAsync(string prefix = "FA")
        {
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTR(NumeroFacture, LENGTH(@Prefix) + 1) AS INTEGER)), 0) + 1 
                FROM Achats WHERE NumeroFacture LIKE @Pattern AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                var nextNumber = await connection.QuerySingleAsync<int>(sql, 
                    new { Prefix = prefix, Pattern = $"{prefix}%" });
                return $"{prefix}{nextNumber:D8}";
            }
        }
    }
}
