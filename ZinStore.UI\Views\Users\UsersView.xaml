<UserControl x:Class="ZinStore.UI.Views.Users.UsersView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.DataContext>
        <viewModels:UsersViewModel/>
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="AccountGroup" 
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="👤 Gestion des Utilisateurs"
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddUserCommand}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AccountPlus" VerticalAlignment="Center"/>
                            <TextBlock Text="Nouvel Utilisateur" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding ManageRolesCommand}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ShieldAccount" VerticalAlignment="Center"/>
                            <TextBlock Text="Gérer Rôles" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding RefreshCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" VerticalAlignment="Center"/>
                            <TextBlock Text="Actualiser" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Barre de recherche et filtres -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche -->
                <TextBox Grid.Column="0"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        materialDesign:HintAssist.Hint="Rechercher un utilisateur..."
                        Style="{StaticResource MaterialDesignOutlinedTextBox}">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Filtre rôle -->
                <ComboBox Grid.Column="2"
                         SelectedValue="{Binding SelectedRoleFilter}"
                         materialDesign:HintAssist.Hint="Filtrer par rôle"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}">
                    <ComboBoxItem Content="Tous les rôles" Tag="All"/>
                    <ComboBoxItem Content="Admin" Tag="Admin"/>
                    <ComboBoxItem Content="Manager" Tag="Manager"/>
                    <ComboBoxItem Content="Vendeur" Tag="Vendeur"/>
                    <ComboBoxItem Content="Caissier" Tag="Caissier"/>
                </ComboBox>

                <!-- Filtre statut -->
                <ComboBox Grid.Column="4"
                         SelectedValue="{Binding StatusFilter}"
                         materialDesign:HintAssist.Hint="Statut"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}">
                    <ComboBoxItem Content="Tous" Tag="All"/>
                    <ComboBoxItem Content="Actifs" Tag="Active"/>
                    <ComboBoxItem Content="Inactifs" Tag="Inactive"/>
                    <ComboBoxItem Content="Bloqués" Tag="Blocked"/>
                </ComboBox>

                <!-- Bouton recherche -->
                <Button Grid.Column="6"
                       Style="{StaticResource MaterialDesignIconButton}"
                       Command="{Binding SearchCommand}"
                       ToolTip="Rechercher">
                    <materialDesign:PackIcon Kind="Magnify"/>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des utilisateurs -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- En-tête du tableau -->
                <Border Grid.Row="0" 
                       Background="{DynamicResource PrimaryHueLightBrush}" 
                       Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="60"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="Photo" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="1" Text="Nom d'utilisateur" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="2" Text="Nom complet" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="3" Text="Email" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="4" Text="Rôle" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="5" Text="Statut" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="6" Text="Dernière connexion" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Grid.Column="7" Text="Actions" FontWeight="Bold" Foreground="White"/>
                    </Grid>
                </Border>

                <!-- Liste -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding Users}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border BorderBrush="{DynamicResource MaterialDesignDivider}" 
                                       BorderThickness="0,0,0,1"
                                       Padding="20,15">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="200"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="150"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <!-- Photo -->
                                        <Ellipse Grid.Column="0" 
                                                Width="40" Height="40"
                                                Fill="{DynamicResource PrimaryHueLightBrush}">
                                            <Ellipse.OpacityMask>
                                                <ImageBrush ImageSource="{Binding ProfilePicture, FallbackValue={x:Null}}"/>
                                            </Ellipse.OpacityMask>
                                        </Ellipse>
                                        <materialDesign:PackIcon Grid.Column="0"
                                                               Kind="Account"
                                                               Width="25" Height="25"
                                                               Foreground="White"
                                                               HorizontalAlignment="Center"
                                                               VerticalAlignment="Center"/>

                                        <!-- Nom d'utilisateur -->
                                        <TextBlock Grid.Column="1" 
                                                  Text="{Binding Username}" 
                                                  VerticalAlignment="Center"
                                                  FontWeight="Bold"/>

                                        <!-- Nom complet -->
                                        <TextBlock Grid.Column="2" 
                                                  Text="{Binding FullName}" 
                                                  VerticalAlignment="Center"/>

                                        <!-- Email -->
                                        <TextBlock Grid.Column="3" 
                                                  Text="{Binding Email}" 
                                                  VerticalAlignment="Center"/>

                                        <!-- Rôle -->
                                        <materialDesign:Chip Grid.Column="4"
                                                           Content="{Binding RoleName}"
                                                           Background="{DynamicResource SecondaryHueMidBrush}"
                                                           Foreground="White"
                                                           FontSize="10"
                                                           Height="25"
                                                           VerticalAlignment="Center"/>

                                        <!-- Statut -->
                                        <materialDesign:Chip Grid.Column="5"
                                                           Content="{Binding Status}"
                                                           Background="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                                           Foreground="White"
                                                           FontSize="10"
                                                           Height="25"
                                                           VerticalAlignment="Center"/>

                                        <!-- Dernière connexion -->
                                        <TextBlock Grid.Column="6" 
                                                  Text="{Binding LastLogin, StringFormat='dd/MM/yyyy HH:mm'}" 
                                                  VerticalAlignment="Center"
                                                  FontSize="12"/>

                                        <!-- Actions -->
                                        <StackPanel Grid.Column="7" 
                                                   Orientation="Horizontal" 
                                                   VerticalAlignment="Center">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.EditUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Modifier">
                                                <materialDesign:PackIcon Kind="Edit" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                            </Button>
                                            
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.ManagePermissionsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Gérer permissions">
                                                <materialDesign:PackIcon Kind="ShieldKey" Foreground="Orange"/>
                                            </Button>
                                            
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.ResetPasswordCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Réinitialiser mot de passe">
                                                <materialDesign:PackIcon Kind="KeyVariant" Foreground="Blue"/>
                                            </Button>
                                            
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.ToggleUserStatusCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Activer/Désactiver">
                                                <materialDesign:PackIcon Kind="{Binding IsActive, Converter={StaticResource BoolToLockIconConverter}}" 
                                                                       Foreground="{Binding IsActive, Converter={StaticResource BoolToColorConverter}}"/>
                                            </Button>
                                            
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.DeleteUserCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Supprimer">
                                                <materialDesign:PackIcon Kind="Delete" Foreground="Red"/>
                                            </Button>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- Indicateur de chargement -->
                <Grid Grid.Row="1" 
                     Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                   IsIndeterminate="True"
                                   Width="50" Height="50"
                                   Margin="0,0,0,20"/>
                        <TextBlock Text="Chargement des utilisateurs..."
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>

                <!-- Message vide -->
                <StackPanel Grid.Row="1"
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           Visibility="{Binding IsEmpty, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <materialDesign:PackIcon Kind="AccountOff" 
                                           Width="64" Height="64"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="Aucun utilisateur trouvé"
                              FontSize="16"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0"/>
                </StackPanel>

                <!-- Pied de page -->
                <Border Grid.Row="2" 
                       Background="{DynamicResource MaterialDesignCardBackground}" 
                       Padding="20,10"
                       BorderBrush="{DynamicResource MaterialDesignDivider}"
                       BorderThickness="0,1,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" 
                                   Orientation="Horizontal"
                                   VerticalAlignment="Center">
                            <TextBlock Text="Total: "/>
                            <TextBlock Text="{Binding Users.Count}" FontWeight="Bold"/>
                            <TextBlock Text=" utilisateur(s)"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="Dernière mise à jour: " VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding LastUpdateTime, StringFormat='dd/MM/yyyy HH:mm'}" 
                                      FontWeight="Bold" 
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
