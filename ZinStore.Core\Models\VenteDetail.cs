using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les détails des ventes
    /// </summary>
    public class VenteDetail : BaseEntity
    {
        [Required]
        public int VenteId { get; set; }

        [Required]
        public int ProduitId { get; set; }

        [Required]
        public decimal Quantite { get; set; }

        [Required]
        public decimal PrixUnitaire { get; set; }

        public decimal PrixAchat { get; set; }

        public decimal TauxTVA { get; set; }

        public decimal MontantTVA { get; set; }

        public decimal TauxRemise { get; set; } = 0;

        public decimal MontantRemise { get; set; } = 0;

        public decimal SousTotal { get; set; }

        public decimal Total { get; set; }

        public string Notes { get; set; }

        // Propriétés de navigation
        public virtual Vente Vente { get; set; }
        public virtual Produit Produit { get; set; }

        // Propriétés calculées
        public decimal MargeBeneficiaire => (PrixUnitaire - PrixAchat) * Quantite;
        public decimal PourcentageMarge => PrixAchat > 0 ? ((PrixUnitaire - PrixAchat) / PrixAchat) * 100 : 0;
    }
}
