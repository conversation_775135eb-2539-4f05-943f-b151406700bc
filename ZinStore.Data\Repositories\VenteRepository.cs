using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class VenteRepository : BaseRepository<Vente>
    {
        public VenteRepository(DatabaseContext context) : base(context, "Ventes")
        {
        }

        public override async Task<IEnumerable<Vente>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT v.*, c.Nom as ClientNom, u.NomComplet as UtilisateurNom
                FROM Ventes v
                LEFT JOIN Clients c ON v.ClientId = c.Id
                LEFT JOIN Utilisateurs u ON v.UtilisateurId = u.Id
                WHERE v.EstSupprime = 0 
                AND (v.NumeroFacture LIKE @SearchTerm OR c.Nom LIKE @SearchTerm)
                ORDER BY v.DateVente DESC";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Vente>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<string> GenerateNextNumeroFactureAsync(string prefix = "FV")
        {
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTR(NumeroFacture, LENGTH(@Prefix) + 1) AS INTEGER)), 0) + 1 
                FROM Ventes WHERE NumeroFacture LIKE @Pattern AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                var nextNumber = await connection.QuerySingleAsync<int>(sql, 
                    new { Prefix = prefix, Pattern = $"{prefix}%" });
                return $"{prefix}{nextNumber:D8}";
            }
        }
    }
}
