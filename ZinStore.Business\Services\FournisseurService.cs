using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;
using ZinStore.Business.Helpers;

namespace ZinStore.Business.Services
{
    public class FournisseurService
    {
        private readonly FournisseurRepository _fournisseurRepository;

        public FournisseurService(DatabaseContext context)
        {
            _fournisseurRepository = new FournisseurRepository(context);
        }

        public async Task<IEnumerable<Fournisseur>> GetAllFournisseursAsync()
        {
            return await _fournisseurRepository.GetAllAsync();
        }

        public async Task<Fournisseur> GetFournisseurByIdAsync(int id)
        {
            return await _fournisseurRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<Fournisseur>> SearchFournisseursAsync(string searchTerm)
        {
            return await _fournisseurRepository.SearchAsync(searchTerm);
        }

        public async Task<(bool Success, string Message, int FournisseurId)> AddFournisseurAsync(Fournisseur fournisseur)
        {
            try
            {
                var validationResult = ValidateFournisseur(fournisseur);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage, 0);
                }

                if (await _fournisseurRepository.CodeExistsAsync(fournisseur.CodeFournisseur))
                {
                    return (false, "Ce code fournisseur existe déjà.", 0);
                }

                CleanFournisseurData(fournisseur);
                int fournisseurId = await _fournisseurRepository.AddAsync(fournisseur);
                return (true, "Fournisseur ajouté avec succès.", fournisseurId);
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de l'ajout du fournisseur: {ex.Message}", 0);
            }
        }

        public async Task<(bool Success, string Message)> UpdateFournisseurAsync(Fournisseur fournisseur)
        {
            try
            {
                var validationResult = ValidateFournisseur(fournisseur);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage);
                }

                if (await _fournisseurRepository.CodeExistsAsync(fournisseur.CodeFournisseur, fournisseur.Id))
                {
                    return (false, "Ce code fournisseur existe déjà.");
                }

                CleanFournisseurData(fournisseur);
                bool success = await _fournisseurRepository.UpdateAsync(fournisseur);
                return success ? (true, "Fournisseur mis à jour avec succès.") : (false, "Erreur lors de la mise à jour du fournisseur.");
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de la mise à jour du fournisseur: {ex.Message}");
            }
        }

        public async Task<(bool Success, string Message)> DeleteFournisseurAsync(int fournisseurId)
        {
            try
            {
                // Vérifier si le fournisseur existe
                var fournisseur = await _fournisseurRepository.GetByIdAsync(fournisseurId);
                if (fournisseur == null)
                {
                    return (false, "Fournisseur introuvable.");
                }

                // TODO: Vérifier si le fournisseur a des achats en cours
                // if (await HasActiveTransactions(fournisseurId))
                // {
                //     return (false, "Impossible de supprimer ce fournisseur car il a des transactions en cours.");
                // }

                // Supprimer le fournisseur (suppression logique)
                bool success = await _fournisseurRepository.DeleteAsync(fournisseurId);
                return success ? (true, "Fournisseur supprimé avec succès.") : (false, "Erreur lors de la suppression du fournisseur.");
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de la suppression du fournisseur: {ex.Message}");
            }
        }

        public async Task<string> GenerateNextCodeAsync()
        {
            return await _fournisseurRepository.GenerateNextCodeAsync("FR");
        }

        private (bool IsValid, string ErrorMessage) ValidateFournisseur(Fournisseur fournisseur)
        {
            if (fournisseur == null)
                return (false, "Les données du fournisseur sont requises.");

            if (string.IsNullOrWhiteSpace(fournisseur.CodeFournisseur))
                return (false, "Le code fournisseur est obligatoire.");

            if (string.IsNullOrWhiteSpace(fournisseur.Nom))
                return (false, "Le nom du fournisseur est obligatoire.");

            if (!ValidationHelper.IsValidCode(fournisseur.CodeFournisseur))
                return (false, "Le code fournisseur contient des caractères non valides.");

            if (!string.IsNullOrWhiteSpace(fournisseur.Email) && !ValidationHelper.IsValidEmail(fournisseur.Email))
                return (false, "L'adresse email n'est pas valide.");

            return (true, string.Empty);
        }

        private void CleanFournisseurData(Fournisseur fournisseur)
        {
            fournisseur.CodeFournisseur = ValidationHelper.CleanCode(fournisseur.CodeFournisseur);
            fournisseur.Nom = fournisseur.Nom?.Trim();
            fournisseur.RaisonSociale = fournisseur.RaisonSociale?.Trim();
            fournisseur.Email = fournisseur.Email?.Trim().ToLower();
        }
    }
}
