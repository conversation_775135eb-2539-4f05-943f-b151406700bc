using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace ZinStore.UI.ViewModels
{
    /// <summary>
    /// Classe de base pour tous les ViewModels
    /// </summary>
    public abstract class BaseViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected virtual bool SetProperty<T>(ref T storage, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(storage, value))
                return false;

            storage = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        private bool _isBusy;
        public bool IsBusy
        {
            get => _isBusy;
            set
            {
                SetProperty(ref _isBusy, value);
                OnIsBusyChanged();
            }
        }

        protected virtual void OnIsBusyChanged()
        {
            // يمكن للـ ViewModels المشتقة تجاوز هذه الطريقة
        }

        private string _busyMessage;
        public string BusyMessage
        {
            get => _busyMessage;
            set => SetProperty(ref _busyMessage, value);
        }
    }
}
