-- =====================================================
-- Script de création des procédures stockées ZinStore
-- Procédures pour les opérations complexes
-- Version: MySQL 8.0+
-- =====================================================

USE zinstore;

-- Changer le délimiteur pour les procédures
DELIMITER $$

-- =====================================================
-- PROCÉDURE 1: Créer une nouvelle vente complète
-- =====================================================

CREATE PROCEDURE sp_creer_vente_complete(
    IN p_client_id INT,
    IN p_utilisateur_id INT,
    IN p_produits JSON,  -- Format: [{"produit_id": 1, "quantite": 2, "prix_unitaire": 100.00}]
    IN p_remise DECIMAL(15,2),
    IN p_mode_paiement VARCHAR(20),
    IN p_montant_paye DECIMAL(15,2),
    OUT p_vente_id INT,
    OUT p_numero_facture VARCHAR(50),
    OUT p_message VARCHAR(500)
)
BEGIN
    DECLARE v_count INT DEFAULT 0;
    DECLARE v_produit_id INT;
    DECLARE v_quantite INT;
    DECLARE v_prix_unitaire DECIMAL(15,2);
    DECLARE v_stock_disponible INT;
    DECLARE v_sous_total DECIMAL(15,2) DEFAULT 0;
    DECLARE v_numero_facture VARCHAR(50);
    DECLARE v_annee VARCHAR(4);
    DECLARE v_mois VARCHAR(2);
    DECLARE v_sequence INT;
    DECLARE done INT DEFAULT FALSE;
    
    -- Curseur pour parcourir les produits
    DECLARE produit_cursor CURSOR FOR 
        SELECT 
            JSON_UNQUOTE(JSON_EXTRACT(p_produits, CONCAT('$[', v_count, '].produit_id'))),
            JSON_UNQUOTE(JSON_EXTRACT(p_produits, CONCAT('$[', v_count, '].quantite'))),
            JSON_UNQUOTE(JSON_EXTRACT(p_produits, CONCAT('$[', v_count, '].prix_unitaire')))
        FROM (SELECT 0 AS n UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) numbers
        WHERE v_count < JSON_LENGTH(p_produits);
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1
            p_message = MESSAGE_TEXT;
        SET p_vente_id = 0;
        SET p_numero_facture = '';
    END;
    
    START TRANSACTION;
    
    -- Générer le numéro de facture
    SET v_annee = YEAR(NOW());
    SET v_mois = LPAD(MONTH(NOW()), 2, '0');
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(numero_facture, -4) AS UNSIGNED)), 0) + 1
    INTO v_sequence
    FROM ventes 
    WHERE numero_facture LIKE CONCAT('FV', v_annee, v_mois, '%');
    
    SET v_numero_facture = CONCAT('FV', v_annee, v_mois, LPAD(v_sequence, 4, '0'));
    
    -- Vérifier le stock pour tous les produits
    SET v_count = 0;
    WHILE v_count < JSON_LENGTH(p_produits) DO
        SET v_produit_id = JSON_UNQUOTE(JSON_EXTRACT(p_produits, CONCAT('$[', v_count, '].produit_id')));
        SET v_quantite = JSON_UNQUOTE(JSON_EXTRACT(p_produits, CONCAT('$[', v_count, '].quantite')));
        
        SELECT quantite_disponible INTO v_stock_disponible
        FROM stock 
        WHERE produit_id = v_produit_id;
        
        IF v_stock_disponible < v_quantite THEN
            SET p_message = CONCAT('Stock insuffisant pour le produit ID: ', v_produit_id);
            ROLLBACK;
            LEAVE;
        END IF;
        
        SET v_count = v_count + 1;
    END WHILE;
    
    -- Créer la vente
    INSERT INTO ventes (
        numero_facture, 
        client_id, 
        utilisateur_id, 
        remise, 
        mode_paiement, 
        montant_paye,
        statut_paiement
    ) VALUES (
        v_numero_facture, 
        p_client_id, 
        p_utilisateur_id, 
        COALESCE(p_remise, 0), 
        p_mode_paiement, 
        COALESCE(p_montant_paye, 0),
        CASE WHEN p_montant_paye >= (SELECT montant_total FROM ventes WHERE id = LAST_INSERT_ID()) THEN 'Payé' ELSE 'Partiel' END
    );
    
    SET p_vente_id = LAST_INSERT_ID();
    
    -- Ajouter les détails de vente
    SET v_count = 0;
    WHILE v_count < JSON_LENGTH(p_produits) DO
        SET v_produit_id = JSON_UNQUOTE(JSON_EXTRACT(p_produits, CONCAT('$[', v_count, '].produit_id')));
        SET v_quantite = JSON_UNQUOTE(JSON_EXTRACT(p_produits, CONCAT('$[', v_count, '].quantite')));
        SET v_prix_unitaire = JSON_UNQUOTE(JSON_EXTRACT(p_produits, CONCAT('$[', v_count, '].prix_unitaire')));
        
        INSERT INTO ventes_details (vente_id, produit_id, quantite, prix_unitaire)
        VALUES (p_vente_id, v_produit_id, v_quantite, v_prix_unitaire);
        
        SET v_count = v_count + 1;
    END WHILE;
    
    -- Mettre à jour le statut de paiement final
    UPDATE ventes 
    SET statut_paiement = CASE 
        WHEN montant_paye >= montant_total THEN 'Payé'
        WHEN montant_paye > 0 THEN 'Partiel'
        ELSE 'En attente'
    END
    WHERE id = p_vente_id;
    
    SET p_numero_facture = v_numero_facture;
    SET p_message = 'Vente créée avec succès';
    
    COMMIT;
END$$

-- =====================================================
-- PROCÉDURE 2: Ajustement de stock
-- =====================================================

CREATE PROCEDURE sp_ajuster_stock(
    IN p_produit_id INT,
    IN p_nouvelle_quantite INT,
    IN p_utilisateur_id INT,
    IN p_motif VARCHAR(200),
    OUT p_message VARCHAR(500)
)
BEGIN
    DECLARE v_quantite_actuelle INT;
    DECLARE v_difference INT;
    DECLARE v_type_mouvement VARCHAR(20);
    
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1
            p_message = MESSAGE_TEXT;
    END;
    
    START TRANSACTION;
    
    -- Récupérer la quantité actuelle
    SELECT quantite_actuelle INTO v_quantite_actuelle
    FROM stock 
    WHERE produit_id = p_produit_id;
    
    IF v_quantite_actuelle IS NULL THEN
        SET p_message = 'Produit non trouvé dans le stock';
        ROLLBACK;
        LEAVE;
    END IF;
    
    -- Calculer la différence
    SET v_difference = p_nouvelle_quantite - v_quantite_actuelle;
    
    IF v_difference = 0 THEN
        SET p_message = 'Aucun ajustement nécessaire';
        ROLLBACK;
        LEAVE;
    END IF;
    
    -- Déterminer le type de mouvement
    SET v_type_mouvement = IF(v_difference > 0, 'Entrée', 'Sortie');
    
    -- Mettre à jour le stock
    UPDATE stock 
    SET quantite_actuelle = p_nouvelle_quantite
    WHERE produit_id = p_produit_id;
    
    -- Créer le mouvement de stock
    INSERT INTO mouvements_stock (
        produit_id,
        type_mouvement,
        quantite,
        quantite_avant,
        reference_type,
        utilisateur_id,
        motif
    ) VALUES (
        p_produit_id,
        'Ajustement',
        v_difference,
        v_quantite_actuelle,
        'Ajustement',
        p_utilisateur_id,
        COALESCE(p_motif, 'Ajustement manuel')
    );
    
    SET p_message = CONCAT('Stock ajusté: ', v_difference, ' unités');
    
    COMMIT;
END$$

-- =====================================================
-- PROCÉDURE 3: Rapport de ventes par période
-- =====================================================

CREATE PROCEDURE sp_rapport_ventes_periode(
    IN p_date_debut DATE,
    IN p_date_fin DATE,
    IN p_client_id INT,
    IN p_utilisateur_id INT
)
BEGIN
    SELECT 
        v.id,
        v.numero_facture,
        v.date_vente,
        COALESCE(c.nom_complet, 'Client comptoir') AS client_nom,
        u.nom_complet AS vendeur_nom,
        v.sous_total,
        v.montant_tva,
        v.remise,
        v.montant_total,
        v.montant_paye,
        v.montant_restant,
        v.statut_paiement,
        v.mode_paiement,
        COUNT(vd.id) AS nombre_articles,
        SUM(vd.quantite) AS quantite_totale
    FROM ventes v
    LEFT JOIN clients c ON v.client_id = c.id
    LEFT JOIN utilisateurs u ON v.utilisateur_id = u.id
    LEFT JOIN ventes_details vd ON v.id = vd.vente_id
    WHERE v.date_vente BETWEEN p_date_debut AND DATE_ADD(p_date_fin, INTERVAL 1 DAY)
        AND (p_client_id IS NULL OR v.client_id = p_client_id)
        AND (p_utilisateur_id IS NULL OR v.utilisateur_id = p_utilisateur_id)
        AND v.statut_paiement != 'Annulé'
    GROUP BY v.id, v.numero_facture, v.date_vente, c.nom_complet, u.nom_complet,
             v.sous_total, v.montant_tva, v.remise, v.montant_total, 
             v.montant_paye, v.montant_restant, v.statut_paiement, v.mode_paiement
    ORDER BY v.date_vente DESC;
END$$

-- =====================================================
-- PROCÉDURE 4: Statistiques financières par période
-- =====================================================

CREATE PROCEDURE sp_statistiques_financieres_periode(
    IN p_date_debut DATE,
    IN p_date_fin DATE
)
BEGIN
    SELECT 
        'Résumé' AS type_ligne,
        NULL AS date_transaction,
        SUM(CASE WHEN type_transaction = 'Revenu' THEN montant ELSE 0 END) AS total_revenus,
        SUM(CASE WHEN type_transaction = 'Dépense' THEN montant ELSE 0 END) AS total_depenses,
        SUM(CASE WHEN type_transaction = 'Revenu' THEN montant ELSE -montant END) AS benefice_net,
        COUNT(CASE WHEN type_transaction = 'Revenu' THEN 1 END) AS nombre_revenus,
        COUNT(CASE WHEN type_transaction = 'Dépense' THEN 1 END) AS nombre_depenses
    FROM (
        SELECT date_revenu AS date_transaction, montant, 'Revenu' AS type_transaction 
        FROM revenus 
        WHERE date_revenu BETWEEN p_date_debut AND p_date_fin
        UNION ALL
        SELECT date_depense AS date_transaction, montant, 'Dépense' AS type_transaction 
        FROM depenses 
        WHERE date_depense BETWEEN p_date_debut AND p_date_fin
    ) AS transactions
    
    UNION ALL
    
    SELECT 
        'Détail' AS type_ligne,
        DATE(date_transaction) AS date_transaction,
        SUM(CASE WHEN type_transaction = 'Revenu' THEN montant ELSE 0 END) AS total_revenus,
        SUM(CASE WHEN type_transaction = 'Dépense' THEN montant ELSE 0 END) AS total_depenses,
        SUM(CASE WHEN type_transaction = 'Revenu' THEN montant ELSE -montant END) AS benefice_net,
        COUNT(CASE WHEN type_transaction = 'Revenu' THEN 1 END) AS nombre_revenus,
        COUNT(CASE WHEN type_transaction = 'Dépense' THEN 1 END) AS nombre_depenses
    FROM (
        SELECT date_revenu AS date_transaction, montant, 'Revenu' AS type_transaction 
        FROM revenus 
        WHERE date_revenu BETWEEN p_date_debut AND p_date_fin
        UNION ALL
        SELECT date_depense AS date_transaction, montant, 'Dépense' AS type_transaction 
        FROM depenses 
        WHERE date_depense BETWEEN p_date_debut AND p_date_fin
    ) AS transactions
    GROUP BY DATE(date_transaction)
    ORDER BY type_ligne, date_transaction DESC;
END$$

-- =====================================================
-- PROCÉDURE 5: Produits en rupture de stock
-- =====================================================

CREATE PROCEDURE sp_produits_rupture_stock()
BEGIN
    SELECT 
        p.id,
        p.nom,
        p.code_produit,
        p.prix_vente,
        c.nom AS categorie_nom,
        f.nom AS fournisseur_nom,
        s.quantite_actuelle,
        s.quantite_reservee,
        s.quantite_disponible,
        p.stock_minimum,
        p.stock_maximum,
        CASE 
            WHEN s.quantite_actuelle = 0 THEN 'Rupture totale'
            WHEN s.quantite_actuelle <= p.stock_minimum THEN 'Stock critique'
            WHEN s.quantite_disponible <= 0 THEN 'Stock réservé'
            ELSE 'Normal'
        END AS statut_stock,
        DATEDIFF(CURDATE(), s.date_derniere_entree) AS jours_sans_entree
    FROM produits p
    LEFT JOIN categories c ON p.categorie_id = c.id
    LEFT JOIN fournisseurs f ON p.fournisseur_id = f.id
    LEFT JOIN stock s ON p.id = s.produit_id
    WHERE p.est_actif = TRUE 
        AND (s.quantite_actuelle <= p.stock_minimum OR s.quantite_disponible <= 0)
    ORDER BY s.quantite_actuelle ASC, p.nom;
END$$

-- Remettre le délimiteur par défaut
DELIMITER ;
