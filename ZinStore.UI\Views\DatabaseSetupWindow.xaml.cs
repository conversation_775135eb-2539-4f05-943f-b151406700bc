using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using Microsoft.Win32;
using ZinStore.Data.Context;
using ZinStore.Data.Helpers;

namespace ZinStore.UI.Views
{
    /// <summary>
    /// Logique d'interaction pour DatabaseSetupWindow.xaml
    /// </summary>
    public partial class DatabaseSetupWindow : Window
    {
        public bool DatabaseConfigured { get; private set; }
        public string ConnectionString { get; private set; }

        public DatabaseSetupWindow()
        {
            InitializeComponent();
        }

        private async void CreateNew_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowProgress("Création de la base de données...");

                // Créer le répertoire Data s'il n'existe pas
                var dataDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataDir))
                {
                    Directory.CreateDirectory(dataDir);
                }

                // Chemin de la nouvelle base de données
                var dbPath = Path.Combine(dataDir, "ZinStore.db");

                // Supprimer l'ancienne base si elle existe
                if (File.Exists(dbPath))
                {
                    File.Delete(dbPath);
                }

                // Créer la nouvelle base de données
                await Task.Run(() =>
                {
                    var connectionString = $"Data Source={dbPath};Version=3;";
                    using (var context = new DatabaseContext(connectionString))
                    {
                        if (!context.TestConnection())
                        {
                            throw new Exception("Impossible de créer la connexion à la base de données");
                        }

                        var initializer = new DatabaseInitializer(context);
                        initializer.Initialize();
                    }
                });

                // Sauvegarder la chaîne de connexion
                ConnectionString = $"Data Source={dbPath};Version=3;";
                
                // Sauvegarder dans le fichier de connexion
                try
                {
                    ConnectionFileManager.WriteConnectionString(ConnectionString);
                }
                catch
                {
                    // Ignorer les erreurs de sauvegarde du fichier
                }

                DatabaseConfigured = true;
                HideProgress();

                MessageBox.Show("Base de données créée avec succès!\n\n" +
                              "Utilisateur par défaut:\n" +
                              "Nom d'utilisateur: admin\n" +
                              "Mot de passe: admin123",
                              "Succès",
                              MessageBoxButton.OK,
                              MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBox.Show($"Erreur lors de la création de la base de données:\n{ex.Message}",
                              "Erreur",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        private void SelectExisting_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new OpenFileDialog
                {
                    Title = "Sélectionner une base de données SQLite",
                    Filter = "Fichiers SQLite (*.db)|*.db|Tous les fichiers (*.*)|*.*",
                    DefaultExt = "db"
                };

                if (dialog.ShowDialog() == true)
                {
                    var selectedPath = dialog.FileName;
                    
                    // Tester la connexion
                    var testConnectionString = $"Data Source={selectedPath};Version=3;";
                    
                    ShowProgress("Test de la connexion...");
                    
                    Task.Run(() =>
                    {
                        try
                        {
                            using (var context = new DatabaseContext(testConnectionString))
                            {
                                if (!context.TestConnection())
                                {
                                    throw new Exception("Impossible de se connecter à la base de données");
                                }
                            }

                            Dispatcher.Invoke(() =>
                            {
                                ConnectionString = testConnectionString;
                                
                                // Sauvegarder dans le fichier de connexion
                                try
                                {
                                    ConnectionFileManager.WriteConnectionString(ConnectionString);
                                }
                                catch
                                {
                                    // Ignorer les erreurs de sauvegarde du fichier
                                }

                                DatabaseConfigured = true;
                                HideProgress();

                                MessageBox.Show("Base de données sélectionnée avec succès!",
                                              "Succès",
                                              MessageBoxButton.OK,
                                              MessageBoxImage.Information);

                                DialogResult = true;
                                Close();
                            });
                        }
                        catch (Exception ex)
                        {
                            Dispatcher.Invoke(() =>
                            {
                                HideProgress();
                                MessageBox.Show($"Erreur lors du test de la base de données:\n{ex.Message}",
                                              "Erreur",
                                              MessageBoxButton.OK,
                                              MessageBoxImage.Error);
                            });
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBox.Show($"Erreur lors de la sélection:\n{ex.Message}",
                              "Erreur",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        private void ManualConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var configWindow = new DatabaseConnectionWindow();
                configWindow.Owner = this;

                var result = configWindow.ShowDialog();

                if (result == true && configWindow.ConnectionSaved)
                {
                    // Utiliser la chaîne de connexion configurée
                    ConnectionString = configWindow.ConnectionString;
                    DatabaseConfigured = true;

                    MessageBox.Show("Configuration sauvegardée avec succès!",
                                  "Succès",
                                  MessageBoxButton.OK,
                                  MessageBoxImage.Information);

                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la configuration:\n{ex.Message}",
                              "Erreur",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        private void Exit_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void ShowProgress(string message)
        {
            ProgressText.Text = message;
            ProgressOverlay.Visibility = Visibility.Visible;
            
            // Désactiver les boutons
            CreateNewButton.IsEnabled = false;
            SelectExistingButton.IsEnabled = false;
            ManualConfigButton.IsEnabled = false;
        }

        private void HideProgress()
        {
            ProgressOverlay.Visibility = Visibility.Collapsed;
            
            // Réactiver les boutons
            CreateNewButton.IsEnabled = true;
            SelectExistingButton.IsEnabled = true;
            ManualConfigButton.IsEnabled = true;
        }
    }
}
