using System;
using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour la comptabilité générale
    /// </summary>
    public class CompteGeneral : BaseEntity
    {
        [Required(ErrorMessage = "Le numéro de compte est obligatoire")]
        [StringLength(20)]
        public string NumeroCompte { get; set; }

        [Required(ErrorMessage = "Le nom du compte est obligatoire")]
        [StringLength(100)]
        public string NomCompte { get; set; }

        [StringLength(10)]
        public string TypeCompte { get; set; } // Actif, Passif, Charge, Produit

        public int? CompteParentId { get; set; }

        public decimal SoldeDebiteur { get; set; } = 0;

        public decimal SoldeCrediteur { get; set; } = 0;

        public bool EstActif { get; set; } = true;

        public string Description { get; set; }

        // Propriétés de navigation
        public virtual CompteGeneral CompteParent { get; set; }

        // Propriétés calculées
        public decimal SoldeNet => SoldeDebiteur - SoldeCrediteur;
        public bool EstDebiteur => SoldeNet > 0;
        public bool EstCrediteur => SoldeNet < 0;
    }

    /// <summary>
    /// Modèle pour les écritures comptables
    /// </summary>
    public class EcritureComptable : BaseEntity
    {
        [Required(ErrorMessage = "Le numéro d'écriture est obligatoire")]
        [StringLength(50)]
        public string NumeroEcriture { get; set; }

        [Required]
        public DateTime DateEcriture { get; set; } = DateTime.Now;

        [Required]
        public int CompteId { get; set; }

        [StringLength(200)]
        public string Libelle { get; set; }

        public decimal MontantDebit { get; set; } = 0;

        public decimal MontantCredit { get; set; } = 0;

        [StringLength(50)]
        public string Reference { get; set; }

        [StringLength(50)]
        public string PieceJustificative { get; set; }

        public int UtilisateurId { get; set; }

        public bool EstValidee { get; set; } = false;

        public DateTime? DateValidation { get; set; }

        public string Notes { get; set; }

        // Propriétés de navigation
        public virtual CompteGeneral Compte { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
    }
}
