<Window x:Class="ZinStore.UI.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
        Title="ZinStore - Connexion"
        Height="600"
        Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.DataContext>
        <viewModels:LoginViewModel />
    </Window.DataContext>

    <Window.Resources>
        <!-- Styles personnalisés -->
        <Style x:Key="ModernTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox}">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#2196F3"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
            <Setter Property="materialDesign:TextFieldAssist.DecorationVisibility" Value="Hidden"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="ModernPasswordBox" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignPasswordBox}">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Padding" Value="16,12"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="materialDesign:TextFieldAssist.UnderlineBrush" Value="#2196F3"/>
            <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="8"/>
            <Setter Property="materialDesign:TextFieldAssist.DecorationVisibility" Value="Hidden"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Background" Value="White"/>
        </Style>

        <Style x:Key="LoginButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="50"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,16,0,8"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="25"/>
            <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
        </Style>

        <Style x:Key="CloseButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Padding" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="15"/>
        </Style>

        <!-- Animations -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.8"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                           From="30" To="0" Duration="0:0:0.6">
                <DoubleAnimation.EasingFunction>
                    <CubicEase EasingMode="EaseOut"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
        </Storyboard>

        <Storyboard x:Key="ShakeAnimation">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)">
                <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="0"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
    </Window.Resources>

    <Window.Triggers>
        <EventTrigger RoutedEvent="Window.Loaded">
            <BeginStoryboard Storyboard="{StaticResource FadeInAnimation}"/>
        </EventTrigger>
    </Window.Triggers>

    <Window.RenderTransform>
        <TranslateTransform/>
    </Window.RenderTransform>

    <!-- Arrière-plan avec dégradé -->
    <Grid>
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667eea" Offset="0"/>
                <GradientStop Color="#764ba2" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- Particules d'arrière-plan -->
        <Canvas>
            <Ellipse Width="100" Height="100" Fill="#FFFFFF" Opacity="0.1" Canvas.Left="50" Canvas.Top="100"/>
            <Ellipse Width="60" Height="60" Fill="#FFFFFF" Opacity="0.15" Canvas.Left="350" Canvas.Top="200"/>
            <Ellipse Width="80" Height="80" Fill="#FFFFFF" Opacity="0.08" Canvas.Left="200" Canvas.Top="500"/>
            <Ellipse Width="40" Height="40" Fill="#FFFFFF" Opacity="0.2" Canvas.Left="380" Canvas.Top="50"/>
        </Canvas>

        <!-- Contenu principal -->
        <materialDesign:Card Width="350" Height="500"
                           VerticalAlignment="Center"
                           HorizontalAlignment="Center"
                           materialDesign:ShadowAssist.ShadowDepth="Depth4"
                           materialDesign:Card.UniformCornerRadius="20"
                           Background="White">

            <Grid Margin="30,15">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Bouton de fermeture -->
                <Button Style="{StaticResource CloseButton}"
                       HorizontalAlignment="Right"
                       VerticalAlignment="Top"
                       Margin="-25,-5,0,0"
                       Click="CloseButton_Click"
                       Background="#FF5722"
                       Panel.ZIndex="1">
                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                </Button>

                <!-- En-tête moderne -->
                <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,10,0,20">
                    <!-- Logo avec animation -->
                    <Border Width="60" Height="60"
                           Background="#2196F3"
                           CornerRadius="30"
                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
                        <materialDesign:PackIcon Kind="Store"
                                               Width="30" Height="30"
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"/>
                    </Border>

                    <TextBlock Text="ZinStore"
                              FontSize="24"
                              FontWeight="Bold"
                              Foreground="#2C3E50"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,5"/>

                    <TextBlock Text="Système de Gestion de Supermarché"
                              FontSize="12"
                              Foreground="#7F8C8D"
                              HorizontalAlignment="Center"/>

                    <Rectangle Height="2" Width="50"
                              Fill="#2196F3"
                              Margin="0,10,0,0"
                              RadiusX="1" RadiusY="1"/>
                </StackPanel>

                <!-- Formulaire de connexion moderne -->
                <StackPanel Grid.Row="1" Margin="0,0,0,15">
                    <TextBlock Text="Connexion"
                              FontSize="18"
                              FontWeight="SemiBold"
                              Foreground="#2C3E50"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,20"/>

                    <!-- Nom d'utilisateur avec icône -->
                    <Grid Margin="0,0,0,15">
                        <TextBox x:Name="UsernameTextBox"
                                materialDesign:HintAssist.Hint="Nom d'utilisateur"
                                Style="{StaticResource ModernTextBox}"
                                Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                                Height="40"
                                Padding="35,8,12,8">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>
                        <materialDesign:PackIcon Kind="Account"
                                               Width="16" Height="16"
                                               Foreground="#7F8C8D"
                                               VerticalAlignment="Center"
                                               HorizontalAlignment="Left"
                                               Margin="12,0,0,0"/>
                    </Grid>

                    <!-- Mot de passe avec icône -->
                    <Grid Margin="0,0,0,15">
                        <PasswordBox x:Name="PasswordBox"
                                   materialDesign:HintAssist.Hint="Mot de passe"
                                   Style="{StaticResource ModernPasswordBox}"
                                   Height="40"
                                   Padding="35,8,12,8">
                            <PasswordBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding LoginCommand}"/>
                            </PasswordBox.InputBindings>
                        </PasswordBox>
                        <materialDesign:PackIcon Kind="Lock"
                                               Width="16" Height="16"
                                               Foreground="#7F8C8D"
                                               VerticalAlignment="Center"
                                               HorizontalAlignment="Left"
                                               Margin="12,0,0,0"/>
                    </Grid>

                    <!-- Message d'erreur moderne -->
                    <Border x:Name="ErrorBorder"
                           Background="#FFEBEE"
                           BorderBrush="#F44336"
                           BorderThickness="1"
                           CornerRadius="6"
                           Padding="10,8"
                           Margin="0,0,0,10"
                           Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                   Width="16" Height="16"
                                                   Foreground="#F44336"
                                                   VerticalAlignment="Center"
                                                   Margin="0,0,10,0"/>
                            <TextBlock Text="{Binding ErrorMessage}"
                                      Foreground="#F44336"
                                      FontSize="14"
                                      TextWrapping="Wrap"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        <Border.Style>
                            <Style TargetType="Border">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding HasError}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Border.Style>
                    </Border>

                    <!-- Indicateur de chargement moderne -->
                    <Border Background="#E3F2FD"
                           BorderBrush="#2196F3"
                           BorderThickness="1"
                           CornerRadius="6"
                           Padding="10,8"
                           Margin="0,0,0,10">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       Width="20" Height="20"
                                       IsIndeterminate="True"
                                       Margin="0,0,10,0"/>
                            <TextBlock Text="{Binding BusyMessage}"
                                      Foreground="#2196F3"
                                      FontSize="14"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                        <Border.Style>
                            <Style TargetType="Border">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsBusy}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Border.Style>
                    </Border>

                    <!-- Bouton de connexion moderne -->
                    <Button Style="{StaticResource LoginButton}"
                           Command="{Binding LoginCommand}"
                           Width="250"
                           Height="40"
                           Margin="0,5,0,10" >
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Login"
                                                   Width="16" Height="16"
                                                   Margin="0,0,8,0"/>
                            <TextBlock Text="Se connecter" FontSize="14" FontWeight="SemiBold"/>
                        </StackPanel>
                    </Button>

                    <!-- Liens utiles -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5,0,0">
                        <Button Content="Mot de passe oublié ?"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Foreground="#2196F3"
                               FontSize="10"
                               Padding="3"/>
                        <TextBlock Text="•" Foreground="#BDC3C7" Margin="3,0" VerticalAlignment="Center"/>
                        <Button Content="Aide"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Foreground="#2196F3"
                               FontSize="10"
                               Padding="3"/>
                    </StackPanel>
                </StackPanel>

                <!-- Pied de page moderne -->
                <StackPanel Grid.Row="2" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,5">
                        <materialDesign:PackIcon Kind="Copyright"
                                               Width="12" Height="12"
                                               Foreground="#BDC3C7"
                                               VerticalAlignment="Center"
                                               Margin="0,0,3,0"/>
                        <TextBlock Text="2024 ZinStore - Version 1.0"
                                  FontSize="10"
                                  Foreground="#BDC3C7"/>
                    </StackPanel>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Content="Conditions d'utilisation"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Foreground="#95A5A6"
                               FontSize="8"
                               Padding="3,1"/>
                        <TextBlock Text="•" Foreground="#BDC3C7" Margin="3,0" VerticalAlignment="Center"/>
                        <Button Content="Confidentialité"
                               Style="{StaticResource MaterialDesignFlatButton}"
                               Foreground="#95A5A6"
                               FontSize="8"
                               Padding="3,1"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
