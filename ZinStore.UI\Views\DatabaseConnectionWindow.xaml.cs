using System;
using System.Configuration;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using MaterialDesignThemes.Wpf;
using ZinStore.Data.Context;
using ZinStore.Data.Helpers;
using System.Data.SQLite;
using Microsoft.Data.SqlClient;

namespace ZinStore.UI.Views
{
    /// <summary>
    /// Logique d'interaction pour DatabaseConnectionWindow.xaml
    /// </summary>
    public partial class DatabaseConnectionWindow : Window
    {
        public string ConnectionString { get; private set; }
        public bool ConnectionSaved { get; private set; }

        public DatabaseConnectionWindow()
        {
            InitializeComponent();
            LoadCurrentConnectionString();
            SetupEventHandlers();
        }

        private void SetupEventHandlers()
        {
            DatabaseTypeComboBox.SelectionChanged += DatabaseType_SelectionChanged;
            UseWindowsAuthCheckBox.Checked += WindowsAuth_Changed;
            UseWindowsAuthCheckBox.Unchecked += WindowsAuth_Changed;

            // Auto-generate connection string when values change
            DatabasePathTextBox.TextChanged += (s, e) => GenerateConnectionString();
            ServerNameTextBox.TextChanged += (s, e) => GenerateConnectionString();
            DatabaseNameTextBox.TextChanged += (s, e) => GenerateConnectionString();
            UsernameTextBox.TextChanged += (s, e) => GenerateConnectionString();
        }

        private void LoadCurrentConnectionString()
        {
            try
            {
                // 1. Essayer d'abord le fichier de configuration
                var currentConnectionString = ConfigurationManager.ConnectionStrings["ZinStoreDB"]?.ConnectionString;

                // 2. Si pas trouvé, essayer le fichier de connexion
                if (string.IsNullOrEmpty(currentConnectionString))
                {
                    try
                    {
                        currentConnectionString = DatabaseContext.LoadConnectionStringFromFile();
                    }
                    catch
                    {
                        // Ignorer les erreurs du fichier de connexion
                    }
                }

                if (!string.IsNullOrEmpty(currentConnectionString))
                {
                    ConnectionStringTextBox.Text = currentConnectionString;
                    ParseConnectionString(currentConnectionString);
                }

                // Mettre à jour les informations du fichier
                UpdateFileInfo();
            }
            catch (Exception ex)
            {
                ShowStatus($"Erreur lors du chargement: {ex.Message}", PackIconKind.Error, "Red");
            }
        }

        private void ParseConnectionString(string connectionString)
        {
            try
            {
                if (connectionString.Contains("Data Source") && !connectionString.Contains("Server"))
                {
                    // SQLite
                    DatabaseTypeComboBox.SelectedIndex = 0;
                    var dataSourceStart = connectionString.IndexOf("Data Source=") + 12;
                    var dataSourceEnd = connectionString.IndexOf(";", dataSourceStart);
                    if (dataSourceEnd == -1) dataSourceEnd = connectionString.Length;

                    var dataSource = connectionString.Substring(dataSourceStart, dataSourceEnd - dataSourceStart);
                    DatabasePathTextBox.Text = dataSource;
                }
                else if (connectionString.Contains("Server") || connectionString.Contains("Data Source"))
                {
                    // SQL Server
                    DatabaseTypeComboBox.SelectedIndex = 1;
                    // Parse SQL Server connection string
                    // Implementation can be added here
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Erreur lors de l'analyse: {ex.Message}", PackIconKind.Error, "Red");
            }
        }

        private void DatabaseType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            switch (DatabaseTypeComboBox.SelectedIndex)
            {
                case 0: // SQLite
                    SQLitePanel.Visibility = Visibility.Visible;
                    SqlServerPanel.Visibility = Visibility.Collapsed;
                    break;
                case 1: // SQL Server
                    SQLitePanel.Visibility = Visibility.Collapsed;
                    SqlServerPanel.Visibility = Visibility.Visible;
                    break;
                default:
                    SQLitePanel.Visibility = Visibility.Collapsed;
                    SqlServerPanel.Visibility = Visibility.Collapsed;
                    ShowStatus("Type de base de données non supporté actuellement", PackIconKind.Warning, "Orange");
                    break;
            }
            GenerateConnectionString();
        }

        private void WindowsAuth_Changed(object sender, RoutedEventArgs e)
        {
            SqlAuthPanel.Visibility = UseWindowsAuthCheckBox.IsChecked == true ?
                Visibility.Collapsed : Visibility.Visible;
            GenerateConnectionString();
        }

        private void BrowseDatabase_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new SaveFileDialog
            {
                Title = "Sélectionner ou créer un fichier de base de données",
                Filter = "Fichiers SQLite (*.db)|*.db|Tous les fichiers (*.*)|*.*",
                DefaultExt = "db",
                FileName = "ZinStore.db"
            };

            if (dialog.ShowDialog() == true)
            {
                DatabasePathTextBox.Text = dialog.FileName;
                GenerateConnectionString();
            }
        }

        private void GenerateConnectionString_Click(object sender, RoutedEventArgs e)
        {
            GenerateConnectionString();
        }

        private void GenerateConnectionString()
        {
            try
            {
                string connectionString = "";

                switch (DatabaseTypeComboBox.SelectedIndex)
                {
                    case 0: // SQLite
                        var dbPath = DatabasePathTextBox.Text.Trim();
                        if (!string.IsNullOrEmpty(dbPath))
                        {
                            connectionString = $"Data Source={dbPath};Version=3;";

                            if (CreateIfNotExistsCheckBox.IsChecked == true)
                            {
                                connectionString += "New=False;";
                            }
                        }
                        break;

                    case 1: // SQL Server
                        var server = ServerNameTextBox.Text.Trim();
                        var database = DatabaseNameTextBox.Text.Trim();

                        if (!string.IsNullOrEmpty(server) && !string.IsNullOrEmpty(database))
                        {
                            connectionString = $"Server={server};Database={database};";

                            if (UseWindowsAuthCheckBox.IsChecked == true)
                            {
                                connectionString += "Integrated Security=true;";
                            }
                            else
                            {
                                var username = UsernameTextBox.Text.Trim();
                                var password = PasswordBox.Password;

                                if (!string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
                                {
                                    connectionString += $"User Id={username};Password={password};";
                                }
                            }

                            connectionString += "Connect Timeout=30;";
                        }
                        break;
                }

                ConnectionStringTextBox.Text = connectionString;
                HideStatus();
            }
            catch (Exception ex)
            {
                ShowStatus($"Erreur lors de la génération: {ex.Message}", PackIconKind.Error, "Red");
            }
        }

        private void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            var connectionString = ConnectionStringTextBox.Text.Trim();

            if (string.IsNullOrEmpty(connectionString))
            {
                ShowStatus("Veuillez générer une chaîne de connexion d'abord", PackIconKind.Warning, "Orange");
                return;
            }

            try
            {
                bool success = false;
                string message = "";

                switch (DatabaseTypeComboBox.SelectedIndex)
                {
                    case 0: // SQLite
                        success = TestSQLiteConnection(connectionString, out message);
                        break;
                    case 1: // SQL Server
                        success = TestSqlServerConnection(connectionString, out message);
                        break;
                    default:
                        message = "Type de base de données non supporté";
                        break;
                }

                if (success)
                {
                    ShowStatus("Connexion réussie!", PackIconKind.CheckCircle, "Green");
                }
                else
                {
                    ShowStatus($"Échec de la connexion: {message}", PackIconKind.Error, "Red");
                }
            }
            catch (Exception ex)
            {
                ShowStatus($"Erreur lors du test: {ex.Message}", PackIconKind.Error, "Red");
            }
        }

        private bool TestSQLiteConnection(string connectionString, out string message)
        {
            try
            {
                // Extract database path
                var dataSourceStart = connectionString.IndexOf("Data Source=") + 12;
                var dataSourceEnd = connectionString.IndexOf(";", dataSourceStart);
                if (dataSourceEnd == -1) dataSourceEnd = connectionString.Length;

                var dbPath = connectionString.Substring(dataSourceStart, dataSourceEnd - dataSourceStart);

                // Create directory if it doesn't exist
                var directory = Path.GetDirectoryName(Path.GetFullPath(dbPath));
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Test connection
                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();
                    message = "Connexion SQLite établie avec succès";
                    return true;
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
                return false;
            }
        }

        private bool TestSqlServerConnection(string connectionString, out string message)
        {
            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    message = "Connexion SQL Server établie avec succès";
                    return true;
                }
            }
            catch (Exception ex)
            {
                message = ex.Message;
                return false;
            }
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            var connectionString = ConnectionStringTextBox.Text.Trim();

            if (string.IsNullOrEmpty(connectionString))
            {
                ShowStatus("Veuillez générer une chaîne de connexion d'abord", PackIconKind.Warning, "Orange");
                return;
            }

            try
            {
                SaveConnectionString(connectionString);
                ConnectionString = connectionString;
                ConnectionSaved = true;

                MessageBox.Show("Configuration sauvegardée avec succès!\nRedémarrez l'application pour appliquer les changements.",
                    "Succès", MessageBoxButton.OK, MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                ShowStatus($"Erreur lors de la sauvegarde: {ex.Message}", PackIconKind.Error, "Red");
            }
        }

        private void SaveConnectionString(string connectionString)
        {
            try
            {
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

                // Remove existing connection string
                if (config.ConnectionStrings.ConnectionStrings["ZinStoreDB"] != null)
                {
                    config.ConnectionStrings.ConnectionStrings.Remove("ZinStoreDB");
                }

                // Add new connection string
                config.ConnectionStrings.ConnectionStrings.Add(new ConnectionStringSettings("ZinStoreDB", connectionString));

                // Save configuration
                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("connectionStrings");
            }
            catch (Exception ex)
            {
                throw new Exception($"Impossible de sauvegarder la configuration: {ex.Message}");
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        #region Gestion du fichier de connexion

        private void LoadFromFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!DatabaseContext.ConnectionFileExists())
                {
                    ShowFileStatus("Aucun fichier de connexion trouvé", PackIconKind.FileAlert, "#FF9800");
                    return;
                }

                string connectionString = DatabaseContext.LoadConnectionStringFromFile();
                if (!string.IsNullOrEmpty(connectionString))
                {
                    ConnectionStringTextBox.Text = connectionString;
                    ShowFileStatus("Chaîne de connexion chargée depuis le fichier", PackIconKind.FileCheck, "#4CAF50");

                    // Essayer de parser la chaîne de connexion pour remplir les champs
                    ParseConnectionStringFromFile(connectionString);
                }
                else
                {
                    ShowFileStatus("Le fichier de connexion est vide", PackIconKind.FileAlert, "#FF9800");
                }
            }
            catch (Exception ex)
            {
                ShowFileStatus($"Erreur: {ex.Message}", PackIconKind.FileRemove, "#F44336");
            }
        }

        private void SaveToFile_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string connectionString = ConnectionStringTextBox.Text.Trim();
                if (string.IsNullOrEmpty(connectionString))
                {
                    ShowFileStatus("Veuillez générer une chaîne de connexion d'abord", PackIconKind.FileAlert, "#FF9800");
                    return;
                }

                ConnectionFileManager.WriteConnectionString(connectionString);
                ShowFileStatus("Chaîne de connexion sauvegardée dans le fichier", PackIconKind.FileCheck, "#4CAF50");

                // Afficher les détails du fichier
                UpdateFileInfo();
            }
            catch (Exception ex)
            {
                ShowFileStatus($"Erreur lors de la sauvegarde: {ex.Message}", PackIconKind.FileRemove, "#F44336");
            }
        }

        private void FileInfo_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var fileInfo = DatabaseContext.GetConnectionFileInfo();

                if (!fileInfo.HasAnyFile)
                {
                    ShowFileStatus("Aucun fichier de connexion trouvé", PackIconKind.FileAlert, "#FF9800");
                    return;
                }

                string details = $"Fichier principal: {(fileInfo.MainFileExists ? "✓" : "✗")}\n";
                details += $"Fichier de sauvegarde: {(fileInfo.BackupFileExists ? "✓" : "✗")}\n";

                if (fileInfo.MainFileExists)
                {
                    details += $"Taille: {fileInfo.MainFileSize} octets\n";
                    details += $"Modifié: {fileInfo.MainFileLastModified:dd/MM/yyyy HH:mm:ss}\n";
                }

                details += $"Chaîne valide: {(fileInfo.IsValid ? "✓" : "✗")}";

                if (!string.IsNullOrEmpty(fileInfo.ErrorMessage))
                {
                    details += $"\nErreur: {fileInfo.ErrorMessage}";
                }

                MessageBox.Show(details, "Informations sur le fichier de connexion",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                ShowFileStatus("Informations affichées", PackIconKind.FileCheck, "#4CAF50");
                UpdateFileInfo();
            }
            catch (Exception ex)
            {
                ShowFileStatus($"Erreur: {ex.Message}", PackIconKind.FileRemove, "#F44336");
            }
        }

        #endregion

        private void ShowStatus(string message, PackIconKind icon, string color)
        {
            StatusText.Text = message;
            StatusIcon.Kind = icon;
            StatusIcon.Foreground = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(color));
            StatusBorder.Background = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString($"Light{color}"));
            StatusBorder.Visibility = Visibility.Visible;
        }

        private void HideStatus()
        {
            StatusBorder.Visibility = Visibility.Collapsed;
        }

        private void ShowFileStatus(string message, PackIconKind icon, string color)
        {
            FileStatusBorder.Visibility = Visibility.Visible;
            FileStatusIcon.Kind = icon;
            FileStatusIcon.Foreground = new System.Windows.Media.SolidColorBrush(
                (System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString(color));
            FileStatusText.Text = message;

            // Masquer après 5 secondes
            var timer = new System.Windows.Threading.DispatcherTimer();
            timer.Interval = TimeSpan.FromSeconds(5);
            timer.Tick += (s, e) =>
            {
                FileStatusBorder.Visibility = Visibility.Collapsed;
                timer.Stop();
            };
            timer.Start();
        }

        private void UpdateFileInfo()
        {
            try
            {
                var fileInfo = DatabaseContext.GetConnectionFileInfo();

                if (fileInfo.HasAnyFile)
                {
                    string details = $"Fichiers: {(fileInfo.MainFileExists ? "Principal" : "")} {(fileInfo.BackupFileExists ? "Sauvegarde" : "")}";
                    if (fileInfo.MainFileExists)
                    {
                        details += $" | {fileInfo.MainFileSize} octets";
                    }
                    FileDetailsText.Text = details;
                }
                else
                {
                    FileDetailsText.Text = "Aucun fichier de connexion";
                }
            }
            catch
            {
                FileDetailsText.Text = "Erreur lors de la lecture des informations";
            }
        }

        private void ParseConnectionStringFromFile(string connectionString)
        {
            try
            {
                // Essayer de déterminer le type de base de données et remplir les champs appropriés
                if (connectionString.Contains("Data Source", StringComparison.OrdinalIgnoreCase) &&
                    connectionString.Contains(".db", StringComparison.OrdinalIgnoreCase))
                {
                    // SQLite
                    DatabaseTypeComboBox.SelectedIndex = 0;

                    // Extraire le chemin de la base de données
                    var parts = connectionString.Split(';');
                    foreach (var part in parts)
                    {
                        if (part.Trim().StartsWith("Data Source", StringComparison.OrdinalIgnoreCase))
                        {
                            var path = part.Split('=')[1].Trim();
                            DatabasePathTextBox.Text = path;
                            break;
                        }
                    }
                }
                // Ajouter d'autres types de bases de données si nécessaire
            }
            catch
            {
                // Ignorer les erreurs de parsing
            }
        }
    }
}
