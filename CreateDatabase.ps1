# Script PowerShell pour créer la base de données ZinStore
Write-Host "=== Création de la base de données ZinStore ===" -ForegroundColor Green

# Vérifier si SQLite est disponible
try {
    # Charger l'assembly System.Data.SQLite
    Add-Type -Path "ZinStore.UI\bin\Debug\net6.0-windows\System.Data.SQLite.dll"
    Write-Host "✓ SQLite chargé avec succès" -ForegroundColor Green
}
catch {
    Write-Host "✗ Erreur lors du chargement de SQLite: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Chemin de la base de données
$dbPath = "ZinStore.UI\Data\ZinStore.db"
$dataDir = "ZinStore.UI\Data"

# Créer le dossier Data s'il n'existe pas
if (!(Test-Path $dataDir)) {
    New-Item -ItemType Directory -Path $dataDir -Force
    Write-Host "✓ Dossier Data créé" -ForegroundColor Green
}

# Vérifier si la base de données existe déjà
if (Test-Path $dbPath) {
    Write-Host "⚠ La base de données existe déjà à: $dbPath" -ForegroundColor Yellow
    $response = Read-Host "Voulez-vous la recréer? (y/N)"
    if ($response -ne "y" -and $response -ne "Y") {
        Write-Host "Opération annulée." -ForegroundColor Yellow
        exit 0
    }
    Remove-Item $dbPath -Force
    Write-Host "✓ Ancienne base de données supprimée" -ForegroundColor Green
}

# Créer la connexion SQLite
$connectionString = "Data Source=$dbPath;Version=3;"
$connection = New-Object System.Data.SQLite.SQLiteConnection($connectionString)

try {
    $connection.Open()
    Write-Host "✓ Connexion à la base de données établie" -ForegroundColor Green
    
    # Créer les tables
    Write-Host "Création des tables..." -ForegroundColor Cyan
    
    # Table Utilisateurs
    $sql = @"
CREATE TABLE IF NOT EXISTS Utilisateurs (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    NomUtilisateur TEXT NOT NULL UNIQUE,
    MotDePasse TEXT NOT NULL,
    NomComplet TEXT NOT NULL,
    Email TEXT,
    Telephone TEXT,
    TypeUtilisateur INTEGER NOT NULL,
    EstActif INTEGER NOT NULL DEFAULT 1,
    Photo TEXT,
    Adresse TEXT,
    PeutGererUtilisateurs INTEGER NOT NULL DEFAULT 0,
    PeutGererClients INTEGER NOT NULL DEFAULT 0,
    PeutGererFournisseurs INTEGER NOT NULL DEFAULT 0,
    PeutGererProduits INTEGER NOT NULL DEFAULT 0,
    PeutGererVentes INTEGER NOT NULL DEFAULT 0,
    PeutGererAchats INTEGER NOT NULL DEFAULT 0,
    PeutGererStock INTEGER NOT NULL DEFAULT 0,
    PeutGererComptabilite INTEGER NOT NULL DEFAULT 0,
    PeutVoirRapports INTEGER NOT NULL DEFAULT 0,
    PeutGererParametres INTEGER NOT NULL DEFAULT 0,
    DateCreation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    DateModification DATETIME,
    EstSupprime INTEGER NOT NULL DEFAULT 0,
    UtilisateurCreation TEXT,
    UtilisateurModification TEXT
);
"@
    
    $command = $connection.CreateCommand()
    $command.CommandText = $sql
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✓ Table Utilisateurs créée" -ForegroundColor Green
    
    # Insérer l'utilisateur administrateur par défaut
    $adminPassword = [Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes("admin123"))
    $sql = @"
INSERT INTO Utilisateurs (
    NomUtilisateur, MotDePasse, NomComplet, Email, TypeUtilisateur, EstActif,
    PeutGererUtilisateurs, PeutGererClients, PeutGererFournisseurs, PeutGererProduits,
    PeutGererVentes, PeutGererAchats, PeutGererStock, PeutGererComptabilite,
    PeutVoirRapports, PeutGererParametres, UtilisateurCreation
) VALUES (
    'admin', '$adminPassword', 'Administrateur', '<EMAIL>', 0, 1,
    1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 'System'
);
"@
    
    $command.CommandText = $sql
    $command.ExecuteNonQuery() | Out-Null
    Write-Host "✓ Utilisateur administrateur créé (admin/admin123)" -ForegroundColor Green
    
    Write-Host "=== Base de données créée avec succès! ===" -ForegroundColor Green
    Write-Host "Chemin: $dbPath" -ForegroundColor Cyan
    Write-Host "Utilisateur par défaut: admin" -ForegroundColor Cyan
    Write-Host "Mot de passe par défaut: admin123" -ForegroundColor Cyan
}
catch {
    Write-Host "✗ Erreur lors de la création de la base de données: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    if ($connection.State -eq "Open") {
        $connection.Close()
    }
    $connection.Dispose()
}
