<UserControl x:Class="ZinStore.UI.Views.StockView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">

    <UserControl.DataContext>
        <viewModels:StockViewModel />
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0"
                  Text="📦 Gestion du Stock"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Margin="0,0,0,20"
                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                  FontWeight="Bold"/>

        <!-- Statistiques -->
        <UniformGrid Grid.Row="1"
                    Rows="1"
                    Columns="4"
                    Margin="0,0,0,20">

            <!-- Total produits -->
            <materialDesign:Card Style="{StaticResource CardStyle}"
                               Background="{DynamicResource PrimaryHueMidBrush}"
                               Margin="0,0,10,0">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0"
                                               Kind="Package"
                                               Width="30"
                                               Height="30"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1"
                                  Text="{Binding TotalProduits}"
                                  FontSize="20"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  VerticalAlignment="Center"
                                  HorizontalAlignment="Right"/>
                    </Grid>
                    <TextBlock Text="Total Produits"
                              FontSize="12"
                              Foreground="White"
                              Opacity="0.8"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Produits en rupture -->
            <materialDesign:Card Style="{StaticResource CardStyle}"
                               Background="#FF5722"
                               Margin="0,0,10,0">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0"
                                               Kind="AlertCircle"
                                               Width="30"
                                               Height="30"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1"
                                  Text="{Binding ProduitsEnRupture}"
                                  FontSize="20"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  VerticalAlignment="Center"
                                  HorizontalAlignment="Right"/>
                    </Grid>
                    <TextBlock Text="En Rupture"
                              FontSize="12"
                              Foreground="White"
                              Opacity="0.8"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Stock faible -->
            <materialDesign:Card Style="{StaticResource CardStyle}"
                               Background="#FF9800"
                               Margin="0,0,10,0">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0"
                                               Kind="TrendingDown"
                                               Width="30"
                                               Height="30"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1"
                                  Text="{Binding StockFaible}"
                                  FontSize="20"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  VerticalAlignment="Center"
                                  HorizontalAlignment="Right"/>
                    </Grid>
                    <TextBlock Text="Stock Faible"
                              FontSize="12"
                              Foreground="White"
                              Opacity="0.8"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Valeur totale -->
            <materialDesign:Card Style="{StaticResource CardStyle}"
                               Background="#4CAF50">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0"
                                               Kind="CurrencyEur"
                                               Width="30"
                                               Height="30"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1"
                                  Text="{Binding ValeurTotale, StringFormat='{}{0:C}'}"
                                  FontSize="20"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  VerticalAlignment="Center"
                                  HorizontalAlignment="Right"/>
                    </Grid>
                    <TextBlock Text="Valeur Totale"
                              FontSize="12"
                              Foreground="White"
                              Opacity="0.8"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </UniformGrid>

        <!-- Barre d'outils -->
        <materialDesign:Card Grid.Row="2"
                           Margin="0,0,0,20"
                           Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche et filtres -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox materialDesign:HintAssist.Hint="Rechercher un produit..."
                            Style="{StaticResource CustomTextBox}"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Width="250"
                            Margin="0,0,10,0"/>

                    <ComboBox materialDesign:HintAssist.Hint="Filtrer par statut"
                             SelectedItem="{Binding SelectedFilter}"
                             Width="150">
                        <ComboBoxItem Content="Tous"/>
                        <ComboBoxItem Content="En stock"/>
                        <ComboBoxItem Content="Stock faible"/>
                        <ComboBoxItem Content="En rupture"/>
                    </ComboBox>
                </StackPanel>

                <!-- Boutons -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal">
                    <Button Content="Ajustement Stock"
                           Style="{StaticResource PrimaryButton}"
                           Command="{Binding AjustementStockCommand}"/>
                    <Button Content="Inventaire"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding InventaireCommand}"/>
                    <Button Content="Exporter"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding ExportCommand}"/>
                    <Button Content="Actualiser"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding RefreshCommand}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des produits -->
        <materialDesign:Card Grid.Row="3"
                           Padding="15">
            <DataGrid ItemsSource="{Binding Produits}"
                     SelectedItem="{Binding SelectedProduit}"
                     Style="{StaticResource CustomDataGrid}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Code"
                                      Binding="{Binding CodeProduit}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Nom"
                                      Binding="{Binding Nom}"
                                      Width="200"/>
                    <DataGridTextColumn Header="Catégorie"
                                      Binding="{Binding NomCategorie}"
                                      Width="120"/>
                    <DataGridTextColumn Header="Stock Actuel"
                                      Binding="{Binding StockActuel}"
                                      Width="100">
                        <DataGridTextColumn.CellStyle>
                            <Style TargetType="DataGridCell">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsStockFaible}" Value="True">
                                        <Setter Property="Background" Value="#FFEB3B"/>
                                        <Setter Property="Foreground" Value="Black"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding IsEnRupture}" Value="True">
                                        <Setter Property="Background" Value="#F44336"/>
                                        <Setter Property="Foreground" Value="White"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.CellStyle>
                    </DataGridTextColumn>
                    <DataGridTextColumn Header="Stock Min"
                                      Binding="{Binding StockMinimum}"
                                      Width="80"/>
                    <DataGridTextColumn Header="Stock Max"
                                      Binding="{Binding StockMaximum}"
                                      Width="80"/>
                    <DataGridTextColumn Header="Prix Achat"
                                      Binding="{Binding PrixAchat, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Valeur Stock"
                                      Binding="{Binding ValeurStock, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Dernière MAJ"
                                      Binding="{Binding DerniereMiseAJour, StringFormat='dd/MM/yyyy'}"
                                      Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
