using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using ZinStore.Core.Models;
using ZinStore.Core.Services.Interfaces;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class AchatFormViewModel : BaseViewModel
    {
        private readonly IAchatService _achatService;
        private readonly IFournisseurService _fournisseurService;
        private readonly IProduitService _produitService;
        
        private string _numeroBon;
        private DateTime _dateAchat;
        private Fournisseur _selectedFournisseur;
        private string _modePaiement;
        private string _notes;
        private bool _imprimerBon;
        private string _searchProduitText;
        private decimal _montantHT;
        private decimal _montantTVA;
        private decimal _montantTotal;

        public AchatFormViewModel()
        {
            // Pour le design-time
            _achatService = null;
            _fournisseurService = null;
            _produitService = null;
            
            // Initialisation
            DateAchat = DateTime.Now;
            NumeroBon = GenerateNumeroBon();
            
            Fournisseurs = new ObservableCollection<Fournisseur>();
            ProduitsAchetes = new ObservableCollection<ProduitAchat>();
            
            // Commandes
            AddProduitCommand = new RelayCommand(AddProduit);
            RemoveProduitCommand = new RelayCommand<ProduitAchat>(RemoveProduit);
            SearchProduitCommand = new RelayCommand(SearchProduit);
            SaveCommand = new RelayCommand(Save, CanSave);
            CancelCommand = new RelayCommand(Cancel);
            
            // Charger les données
            LoadFournisseurs();
            LoadTestData();
        }

        public string NumeroBon
        {
            get => _numeroBon;
            set => SetProperty(ref _numeroBon, value);
        }

        public DateTime DateAchat
        {
            get => _dateAchat;
            set => SetProperty(ref _dateAchat, value);
        }

        public Fournisseur SelectedFournisseur
        {
            get => _selectedFournisseur;
            set => SetProperty(ref _selectedFournisseur, value);
        }

        public string ModePaiement
        {
            get => _modePaiement;
            set => SetProperty(ref _modePaiement, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public bool ImprimerBon
        {
            get => _imprimerBon;
            set => SetProperty(ref _imprimerBon, value);
        }

        public string SearchProduitText
        {
            get => _searchProduitText;
            set => SetProperty(ref _searchProduitText, value);
        }

        public decimal MontantHT
        {
            get => _montantHT;
            set => SetProperty(ref _montantHT, value);
        }

        public decimal MontantTVA
        {
            get => _montantTVA;
            set => SetProperty(ref _montantTVA, value);
        }

        public decimal MontantTotal
        {
            get => _montantTotal;
            set => SetProperty(ref _montantTotal, value);
        }

        public ObservableCollection<Fournisseur> Fournisseurs { get; }
        public ObservableCollection<ProduitAchat> ProduitsAchetes { get; }

        // Commandes
        public ICommand AddProduitCommand { get; }
        public ICommand RemoveProduitCommand { get; }
        public ICommand SearchProduitCommand { get; }
        public ICommand SaveCommand { get; }
        public ICommand CancelCommand { get; }

        public void LoadAchat(Achat achat)
        {
            if (achat == null) return;

            NumeroBon = achat.NumeroBon;
            DateAchat = achat.DateAchat;
            ModePaiement = achat.ModePaiement;
            Notes = achat.Notes;
            
            // Charger les détails de l'achat
            // LoadAchatDetails(achat.Id);
        }

        private void LoadFournisseurs()
        {
            if (_fournisseurService == null)
            {
                // Données de test
                Fournisseurs.Add(new Fournisseur { Id = 1, Nom = "Fournisseur ABC", Email = "<EMAIL>" });
                Fournisseurs.Add(new Fournisseur { Id = 2, Nom = "Fournisseur XYZ", Email = "<EMAIL>" });
                Fournisseurs.Add(new Fournisseur { Id = 3, Nom = "Fournisseur DEF", Email = "<EMAIL>" });
                return;
            }

            // Charger depuis le service
        }

        private void AddProduit()
        {
            try
            {
                // Ouvrir une fenêtre de sélection de produit
                MessageBoxHelper.ShowInfo("Fonctionnalité d'ajout de produit en cours de développement.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ajout du produit: {ex.Message}");
            }
        }

        private void RemoveProduit(ProduitAchat produit)
        {
            if (produit == null) return;

            try
            {
                ProduitsAchetes.Remove(produit);
                CalculerTotaux();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la suppression: {ex.Message}");
            }
        }

        private void SearchProduit()
        {
            if (string.IsNullOrWhiteSpace(SearchProduitText)) return;

            try
            {
                // Rechercher et ajouter le produit
                MessageBoxHelper.ShowInfo($"Recherche de: {SearchProduitText}");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void Save()
        {
            try
            {
                if (!ValidateForm()) return;

                // Sauvegarder l'achat
                MessageBoxHelper.ShowSuccess("Achat enregistré avec succès!");
                
                // Fermer la fenêtre
                // CloseWindow();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'enregistrement: {ex.Message}");
            }
        }

        private void Cancel()
        {
            // Fermer la fenêtre sans sauvegarder
            // CloseWindow();
        }

        private bool CanSave()
        {
            return SelectedFournisseur != null && ProduitsAchetes.Any();
        }

        private bool ValidateForm()
        {
            if (SelectedFournisseur == null)
            {
                MessageBoxHelper.ShowWarning("Veuillez sélectionner un fournisseur.");
                return false;
            }

            if (!ProduitsAchetes.Any())
            {
                MessageBoxHelper.ShowWarning("Veuillez ajouter au moins un produit.");
                return false;
            }

            return true;
        }

        private void CalculerTotaux()
        {
            MontantHT = ProduitsAchetes.Sum(p => p.Total);
            MontantTVA = MontantHT * 0.19m; // TVA 19%
            MontantTotal = MontantHT + MontantTVA;
        }

        private string GenerateNumeroBon()
        {
            return $"BA{DateTime.Now:yyyyMMdd}{DateTime.Now:HHmmss}";
        }

        private void LoadTestData()
        {
            // Données de test
            ProduitsAchetes.Add(new ProduitAchat
            {
                NomProduit = "Produit Test 1",
                PrixUnitaire = 25.50m,
                Quantite = 10,
                Total = 255.00m
            });
            
            ProduitsAchetes.Add(new ProduitAchat
            {
                NomProduit = "Produit Test 2",
                PrixUnitaire = 15.75m,
                Quantite = 5,
                Total = 78.75m
            });
            
            CalculerTotaux();
        }
    }

    public class ProduitAchat
    {
        public string NomProduit { get; set; }
        public decimal PrixUnitaire { get; set; }
        public int Quantite { get; set; }
        public decimal Total { get; set; }
    }
}
