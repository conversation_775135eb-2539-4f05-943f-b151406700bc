using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    /// <summary>
    /// Repository pour les clients
    /// </summary>
    public class ClientRepository : BaseRepository<Client>
    {
        public ClientRepository(DatabaseContext context) : base(context, "Clients")
        {
        }

        /// <summary>
        /// Recherche des clients par nom, prénom ou code
        /// </summary>
        public override async Task<IEnumerable<Client>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT * FROM Clients 
                WHERE EstSupprime = 0 
                AND (CodeClient LIKE @SearchTerm 
                     OR Nom LIKE @SearchTerm 
                     OR Prenom LIKE @SearchTerm 
                     OR RaisonSociale LIKE @SearchTerm
                     OR Telephone LIKE @SearchTerm
                     OR Mobile LIKE @SearchTerm)
                ORDER BY Nom, Prenom";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Client>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        /// <summary>
        /// Vérifie si un code client existe déjà
        /// </summary>
        public async Task<bool> CodeExistsAsync(string codeClient, int? excludeId = null)
        {
            var sql = "SELECT COUNT(*) FROM Clients WHERE CodeClient = @CodeClient AND EstSupprime = 0";
            
            if (excludeId.HasValue)
            {
                sql += " AND Id != @ExcludeId";
            }

            using (var connection = _context.GetConnection())
            {
                var count = await connection.QuerySingleAsync<int>(sql, 
                    new { CodeClient = codeClient, ExcludeId = excludeId });
                return count > 0;
            }
        }

        /// <summary>
        /// Obtient les clients actifs
        /// </summary>
        public async Task<IEnumerable<Client>> GetActiveClientsAsync()
        {
            var sql = @"
                SELECT * FROM Clients 
                WHERE EstActif = 1 AND EstSupprime = 0 
                ORDER BY Nom, Prenom";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Client>(sql);
            }
        }

        /// <summary>
        /// Obtient un client par son code
        /// </summary>
        public async Task<Client> GetByCodeAsync(string codeClient)
        {
            var sql = "SELECT * FROM Clients WHERE CodeClient = @CodeClient AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<Client>(sql, new { CodeClient = codeClient });
            }
        }

        /// <summary>
        /// Met à jour le solde du compte client
        /// </summary>
        public async Task<bool> UpdateSoldeCompteAsync(int clientId, decimal nouveauSolde)
        {
            var sql = "UPDATE Clients SET SoldeCompte = @SoldeCompte WHERE Id = @Id";

            using (var connection = _context.GetConnection())
            {
                var affectedRows = await connection.ExecuteAsync(sql, 
                    new { Id = clientId, SoldeCompte = nouveauSolde });
                return affectedRows > 0;
            }
        }

        /// <summary>
        /// Obtient les clients avec un solde débiteur
        /// </summary>
        public async Task<IEnumerable<Client>> GetClientsWithDebitAsync()
        {
            var sql = @"
                SELECT * FROM Clients 
                WHERE SoldeCompte > 0 AND EstActif = 1 AND EstSupprime = 0 
                ORDER BY SoldeCompte DESC";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Client>(sql);
            }
        }

        /// <summary>
        /// Obtient les clients qui ont dépassé leur limite de crédit
        /// </summary>
        public async Task<IEnumerable<Client>> GetClientsOverCreditLimitAsync()
        {
            var sql = @"
                SELECT * FROM Clients 
                WHERE SoldeCompte > LimiteCredit 
                AND LimiteCredit > 0 
                AND EstActif = 1 
                AND EstSupprime = 0 
                ORDER BY (SoldeCompte - LimiteCredit) DESC";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Client>(sql);
            }
        }

        /// <summary>
        /// Génère le prochain code client
        /// </summary>
        public async Task<string> GenerateNextCodeAsync(string prefix = "CL")
        {
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTR(CodeClient, LENGTH(@Prefix) + 1) AS INTEGER)), 0) + 1 
                FROM Clients 
                WHERE CodeClient LIKE @Pattern AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                var nextNumber = await connection.QuerySingleAsync<int>(sql, 
                    new { Prefix = prefix, Pattern = $"{prefix}%" });
                return $"{prefix}{nextNumber:D6}";
            }
        }
    }
}
