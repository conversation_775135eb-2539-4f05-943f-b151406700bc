<UserControl x:Class="ZinStore.UI.Views.Finance.FinanceView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.DataContext>
        <viewModels:FinanceViewModel/>
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="CashMultiple" 
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="💰 Gestion Financière"
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddIncomeCommand}"
                           Background="Green"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" VerticalAlignment="Center"/>
                            <TextBlock Text="Nouveau Revenu" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddExpenseCommand}"
                           Background="Red"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Minus" VerticalAlignment="Center"/>
                            <TextBlock Text="Nouvelle Dépense" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding RefreshCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" VerticalAlignment="Center"/>
                            <TextBlock Text="Actualiser" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Résumé financier -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Revenus -->
            <materialDesign:Card Grid.Column="0" Padding="20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Total Revenus" 
                                      FontSize="14" 
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="{Binding TotalIncome, StringFormat='{}{0:F2} DA'}" 
                                      FontSize="24" 
                                      FontWeight="Bold"
                                      Foreground="Green"/>
                        </StackPanel>
                        
                        <materialDesign:PackIcon Grid.Column="1"
                                               Kind="TrendingUp"
                                               Width="40" Height="40"
                                               Foreground="Green"
                                               VerticalAlignment="Center"/>
                    </Grid>
                    
                    <TextBlock Text="{Binding IncomeGrowth, StringFormat='↗ +{0:F1}% ce mois'}" 
                              FontSize="12"
                              Foreground="Green"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Total Dépenses -->
            <materialDesign:Card Grid.Column="2" Padding="20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Total Dépenses" 
                                      FontSize="14" 
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="{Binding TotalExpenses, StringFormat='{}{0:F2} DA'}" 
                                      FontSize="24" 
                                      FontWeight="Bold"
                                      Foreground="Red"/>
                        </StackPanel>
                        
                        <materialDesign:PackIcon Grid.Column="1"
                                               Kind="TrendingDown"
                                               Width="40" Height="40"
                                               Foreground="Red"
                                               VerticalAlignment="Center"/>
                    </Grid>
                    
                    <TextBlock Text="{Binding ExpenseGrowth, StringFormat='↗ +{0:F1}% ce mois'}" 
                              FontSize="12"
                              Foreground="Red"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Bénéfice Net -->
            <materialDesign:Card Grid.Column="4" Padding="20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Bénéfice Net" 
                                      FontSize="14" 
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="{Binding NetProfit, StringFormat='{}{0:F2} DA'}" 
                                      FontSize="24" 
                                      FontWeight="Bold"
                                      Foreground="{Binding NetProfit, Converter={StaticResource ProfitToColorConverter}}"/>
                        </StackPanel>
                        
                        <materialDesign:PackIcon Grid.Column="1"
                                               Kind="Calculator"
                                               Width="40" Height="40"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"/>
                    </Grid>
                    
                    <TextBlock Text="{Binding ProfitMargin, StringFormat='Marge: {0:F1}%'}" 
                              FontSize="12"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Solde Caisse -->
            <materialDesign:Card Grid.Column="6" Padding="20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Solde Caisse" 
                                      FontSize="14" 
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="{Binding CashBalance, StringFormat='{}{0:F2} DA'}" 
                                      FontSize="24" 
                                      FontWeight="Bold"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </StackPanel>
                        
                        <materialDesign:PackIcon Grid.Column="1"
                                               Kind="CashRegister"
                                               Width="40" Height="40"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"/>
                    </Grid>
                    
                    <TextBlock Text="Toutes caisses confondues" 
                              FontSize="12"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Contenu principal -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Liste des transactions -->
            <materialDesign:Card Grid.Column="0" Padding="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- En-tête avec filtres -->
                    <Border Grid.Row="0" 
                           Background="{DynamicResource PrimaryHueLightBrush}" 
                           Padding="20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="150"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" 
                                      Text="Historique des Transactions" 
                                      FontWeight="Bold" 
                                      FontSize="16"
                                      Foreground="White"
                                      VerticalAlignment="Center"/>

                            <ComboBox Grid.Column="1"
                                     SelectedValue="{Binding TypeFilter}"
                                     materialDesign:HintAssist.Hint="Type"
                                     Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                     Margin="10,0">
                                <ComboBoxItem Content="Tous" Tag="All"/>
                                <ComboBoxItem Content="Revenus" Tag="Income"/>
                                <ComboBoxItem Content="Dépenses" Tag="Expense"/>
                            </ComboBox>

                            <DatePicker Grid.Column="2"
                                       SelectedDate="{Binding DateFrom}"
                                       materialDesign:HintAssist.Hint="Du"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       Margin="5,0"/>

                            <DatePicker Grid.Column="3"
                                       SelectedDate="{Binding DateTo}"
                                       materialDesign:HintAssist.Hint="Au"
                                       Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                       Margin="5,0"/>
                        </Grid>
                    </Border>

                    <!-- En-têtes colonnes -->
                    <Border Grid.Row="1" 
                           Background="{DynamicResource MaterialDesignCardBackground}" 
                           Padding="20,10"
                           BorderBrush="{DynamicResource MaterialDesignDivider}"
                           BorderThickness="0,0,0,1">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="120"/>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="Type" FontWeight="Bold"/>
                            <TextBlock Grid.Column="1" Text="Date" FontWeight="Bold"/>
                            <TextBlock Grid.Column="2" Text="Description" FontWeight="Bold"/>
                            <TextBlock Grid.Column="3" Text="Catégorie" FontWeight="Bold"/>
                            <TextBlock Grid.Column="4" Text="Montant" FontWeight="Bold"/>
                            <TextBlock Grid.Column="5" Text="Mode" FontWeight="Bold"/>
                            <TextBlock Grid.Column="6" Text="Actions" FontWeight="Bold"/>
                        </Grid>
                    </Border>

                    <!-- Liste -->
                    <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding Transactions}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border BorderBrush="{DynamicResource MaterialDesignDivider}" 
                                           BorderThickness="0,0,0,1"
                                           Padding="20,15">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="120"/>
                                                <ColumnDefinition Width="150"/>
                                                <ColumnDefinition Width="100"/>
                                                <ColumnDefinition Width="100"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Type -->
                                            <materialDesign:Chip Grid.Column="0"
                                                               Content="{Binding Type}"
                                                               Background="{Binding Type, Converter={StaticResource TransactionTypeToColorConverter}}"
                                                               Foreground="White"
                                                               FontSize="10"
                                                               Height="25"/>

                                            <!-- Date -->
                                            <TextBlock Grid.Column="1" 
                                                      Text="{Binding Date, StringFormat='dd/MM/yyyy'}" 
                                                      VerticalAlignment="Center"/>

                                            <!-- Description -->
                                            <TextBlock Grid.Column="2" 
                                                      Text="{Binding Description}" 
                                                      VerticalAlignment="Center"
                                                      TextTrimming="CharacterEllipsis"/>

                                            <!-- Catégorie -->
                                            <TextBlock Grid.Column="3" 
                                                      Text="{Binding Category}" 
                                                      VerticalAlignment="Center"/>

                                            <!-- Montant -->
                                            <TextBlock Grid.Column="4" 
                                                      Text="{Binding Amount, StringFormat='{}{0:F2} DA'}" 
                                                      VerticalAlignment="Center"
                                                      FontWeight="Bold"
                                                      Foreground="{Binding Type, Converter={StaticResource TransactionTypeToColorConverter}}"/>

                                            <!-- Mode de paiement -->
                                            <TextBlock Grid.Column="5" 
                                                      Text="{Binding PaymentMethod}" 
                                                      VerticalAlignment="Center"/>

                                            <!-- Actions -->
                                            <StackPanel Grid.Column="6" 
                                                       Orientation="Horizontal" 
                                                       VerticalAlignment="Center">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                       Command="{Binding DataContext.EditTransactionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       ToolTip="Modifier">
                                                    <materialDesign:PackIcon Kind="Edit" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                                </Button>
                                                
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                       Command="{Binding DataContext.DeleteTransactionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       ToolTip="Supprimer">
                                                    <materialDesign:PackIcon Kind="Delete" Foreground="Red"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>

            <!-- Panneau latéral - Graphiques et statistiques -->
            <StackPanel Grid.Column="2">
                <!-- Répartition par catégorie -->
                <materialDesign:Card Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Dépenses par Catégorie" 
                                  FontWeight="Bold" 
                                  FontSize="14"
                                  Margin="0,0,0,15"/>
                        
                        <ItemsControl ItemsSource="{Binding ExpenseCategories}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0,0,0,10">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <StackPanel Grid.Column="0">
                                            <TextBlock Text="{Binding Name}" FontSize="12"/>
                                            <ProgressBar Value="{Binding Percentage}" 
                                                        Maximum="100"
                                                        Height="8"
                                                        Background="{DynamicResource MaterialDesignDivider}"
                                                        Foreground="{Binding Color}"/>
                                        </StackPanel>
                                        
                                        <TextBlock Grid.Column="1" 
                                                  Text="{Binding Amount, StringFormat='{}{0:F0} DA'}" 
                                                  FontSize="12"
                                                  FontWeight="Bold"
                                                  VerticalAlignment="Center"
                                                  Margin="10,0,0,0"/>
                                    </Grid>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Actions rapides -->
                <materialDesign:Card Padding="15">
                    <StackPanel>
                        <TextBlock Text="Actions Rapides" 
                                  FontWeight="Bold" 
                                  FontSize="14"
                                  Margin="0,0,0,15"/>
                        
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding ExportCommand}"
                               Margin="0,0,0,10">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileExport" VerticalAlignment="Center"/>
                                <TextBlock Text="Exporter" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding GenerateReportCommand}"
                               Margin="0,0,0,10">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileChart" VerticalAlignment="Center"/>
                                <TextBlock Text="Rapport" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding ManageCategoriesCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderEdit" VerticalAlignment="Center"/>
                                <TextBlock Text="Catégories" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
