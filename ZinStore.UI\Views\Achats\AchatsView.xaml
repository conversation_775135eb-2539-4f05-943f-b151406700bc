<UserControl x:Class="ZinStore.UI.Views.AchatsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                  Text="🛒 Gestion des Achats"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Margin="0,0,0,20"
                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                  FontWeight="Bold"/>

        <materialDesign:Card Grid.Row="1"
                           Padding="40"
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <materialDesign:PackIcon Kind="ShoppingCart"
                                       Width="80"
                                       Height="80"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                       HorizontalAlignment="Center"
                                       Margin="0,0,0,20"/>
                <TextBlock Text="🚧 Module des Achats"
                          Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource PrimaryHueMidBrush}"
                          FontWeight="Bold"
                          Margin="0,0,0,10"/>
                <TextBlock Text="En cours de développement..."
                          Style="{StaticResource MaterialDesignBody1TextBlock}"
                          HorizontalAlignment="Center"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
