<UserControl x:Class="ZinStore.UI.Views.AchatsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0" 
                  Text="Gestion des Achats" 
                  Style="{StaticResource TitleText}"/>

        <materialDesign:Card Grid.Row="1" 
                           Padding="20">
            <TextBlock Text="Module des achats en cours de développement..." 
                      FontSize="16" 
                      HorizontalAlignment="Center" 
                      VerticalAlignment="Center"/>
        </materialDesign:Card>
    </Grid>
</UserControl>
