<UserControl x:Class="ZinStore.UI.Views.AchatsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                  Text="🛒 Gestion des Achats"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Margin="0,0,0,20"
                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                  FontWeight="Bold"/>

        <!-- Barre d'outils -->
        <materialDesign:Card Grid.Row="1"
                           Margin="0,0,0,20"
                           Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche et filtres -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox materialDesign:HintAssist.Hint="Rechercher un achat (N° bon, fournisseur)..."
                            materialDesign:HintAssist.IsFloating="True"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Width="350"
                            Height="56"
                            FontSize="14"
                            VerticalContentAlignment="Center"
                            Margin="0,0,10,0">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <DatePicker materialDesign:HintAssist.Hint="Date début"
                               materialDesign:HintAssist.IsFloating="True"
                               Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                               SelectedDate="{Binding DateDebut}"
                               Width="140"
                               Height="56"
                               Margin="0,0,10,0"/>

                    <DatePicker materialDesign:HintAssist.Hint="Date fin"
                               materialDesign:HintAssist.IsFloating="True"
                               Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                               SelectedDate="{Binding DateFin}"
                               Width="140"
                               Height="56"/>
                </StackPanel>

                <!-- Boutons -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal"
                           VerticalAlignment="Center">
                    <Button Content="➕ Nouvel Achat"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddAchatCommand}"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Foreground="White"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="👁️ Voir Détails"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding ViewDetailsCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="🖨️ Imprimer"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding PrintCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="🔄 Actualiser"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding RefreshCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des achats -->
        <materialDesign:Card Grid.Row="2"
                           Padding="0"
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- En-tête -->
                <Border Grid.Row="0"
                       Background="{DynamicResource MaterialDesignToolBarBackground}"
                       Padding="15,10">
                    <TextBlock Text="Liste des Achats"
                              Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                              FontWeight="Bold"/>
                </Border>

                <!-- DataGrid -->
                <DataGrid Grid.Row="1"
                         ItemsSource="{Binding Achats}"
                         SelectedItem="{Binding SelectedAchat}"
                         Style="{StaticResource MaterialDesignDataGrid}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False"
                         CanUserDeleteRows="False"
                         IsReadOnly="True"
                         SelectionMode="Single"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         AlternatingRowBackground="{DynamicResource MaterialDesignDivider}"
                         Margin="15">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="N° Bon d'Achat"
                                          Binding="{Binding NumeroBon}"
                                          Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Date d'Achat"
                                          Binding="{Binding DateAchat, StringFormat='dd/MM/yyyy'}"
                                          Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Fournisseur"
                                          Binding="{Binding NomFournisseur}"
                                          Width="200">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Montant HT"
                                          Binding="{Binding MontantHT, StringFormat='{}{0:N2} DA'}"
                                          Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="#FF2196F3"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="TVA"
                                          Binding="{Binding MontantTVA, StringFormat='{}{0:N2} DA'}"
                                          Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Montant TTC"
                                          Binding="{Binding MontantTotal, StringFormat='{}{0:N2} DA'}"
                                          Width="130">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="#FF4CAF50"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Statut"
                                          Binding="{Binding StatutAchat}"
                                          Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Mode Paiement"
                                          Binding="{Binding ModePaiement}"
                                          Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
