<Application x:Class="ZinStore.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:ZinStore.UI.Helpers.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />

                <!-- Styles personnalisés -->
                <ResourceDictionary>
                    <!-- Value Converters -->
                    <converters:ProfitToColorConverter x:Key="ProfitToColorConverter"/>
                    <converters:PercentageToColorConverter x:Key="PercentageToColorConverter"/>
                    <converters:StatusToColorConverter x:Key="StatusToColorConverter"/>
                    <converters:PerformanceToColorConverter x:Key="PerformanceToColorConverter"/>
                    <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
                    <converters:InverseNullToVisibilityConverter x:Key="InverseNullToVisibilityConverter"/>
                    <converters:TransactionTypeToColorConverter x:Key="TransactionTypeToColorConverter"/>
                    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                    <!-- Style pour les boutons principaux -->
                    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>

                    <!-- Style pour les boutons secondaires -->
                    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>

                    <!-- Style pour les TextBox -->
                    <Style x:Key="CustomTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>

                    <!-- Style pour les ComboBox -->
                    <Style x:Key="CustomComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                        <Setter Property="Height" Value="40"/>
                        <Setter Property="Margin" Value="5"/>
                        <Setter Property="FontSize" Value="14"/>
                    </Style>

                    <!-- Style pour les DataGrid -->
                    <Style x:Key="CustomDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                        <Setter Property="AutoGenerateColumns" Value="False"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="IsReadOnly" Value="True"/>
                        <Setter Property="SelectionMode" Value="Single"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="FontSize" Value="13"/>
                    </Style>

                    <!-- Style pour les titres -->
                    <Style x:Key="TitleText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                        <Setter Property="Margin" Value="0,0,0,20"/>
                    </Style>

                    <!-- Style pour les sous-titres -->
                    <Style x:Key="SubtitleText" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="18"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                        <Setter Property="Margin" Value="0,0,0,10"/>
                    </Style>

                    <!-- Style pour les cartes -->
                    <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="10"/>
                        <Setter Property="Padding" Value="20"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
