using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;

namespace ZinStore.Business.Services
{
    public class AchatService
    {
        private readonly AchatRepository _achatRepository;

        public AchatService(DatabaseContext context)
        {
            _achatRepository = new AchatRepository(context);
        }

        public async Task<IEnumerable<Achat>> GetAllAchatsAsync()
        {
            return await _achatRepository.GetAllAsync();
        }

        public async Task<Achat> GetAchatByIdAsync(int id)
        {
            return await _achatRepository.GetByIdAsync(id);
        }

        public async Task<string> GenerateNextNumeroFactureAsync()
        {
            return await _achatRepository.GenerateNextNumeroFactureAsync("FA");
        }
    }
}
