using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Core.Services.Interfaces;
using ZinStore.Core.Enums;
using ZinStore.Data.Context;

namespace ZinStore.Business.Services
{
    public class AchatService : IAchatService
    {
        private readonly DatabaseContext _context;

        public AchatService(DatabaseContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Achat>> GetAllAchatsAsync()
        {
            using var connection = _context.GetConnection();

            const string sql = @"
                SELECT a.*, f.Nom as NomFournisseur, u.NomComplet as NomUtilisateur
                FROM Achats a
                LEFT JOIN Fournisseurs f ON a.FournisseurId = f.Id
                LEFT JOIN Utilisateurs u ON a.UtilisateurId = u.Id
                ORDER BY a.DateAchat DESC";

            return await connection.QueryAsync<Achat>(sql);
        }

        public async Task<Achat> GetAchatByIdAsync(int id)
        {
            using var connection = _context.GetConnection();

            const string sql = @"
                SELECT a.*, f.Nom as NomFournisseur, u.NomComplet as NomUtilisateur
                FROM Achats a
                LEFT JOIN Fournisseurs f ON a.FournisseurId = f.Id
                LEFT JOIN Utilisateurs u ON a.UtilisateurId = u.Id
                WHERE a.Id = @Id";

            return await connection.QueryFirstOrDefaultAsync<Achat>(sql, new { Id = id });
        }

        public async Task<IEnumerable<Achat>> SearchAchatsAsync(string searchText, DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            using var connection = _context.GetConnection();

            var whereConditions = new List<string>();
            var parameters = new DynamicParameters();

            if (!string.IsNullOrWhiteSpace(searchText))
            {
                whereConditions.Add("(a.NumeroFacture LIKE @SearchText OR f.Nom LIKE @SearchText OR a.NumeroFactureFournisseur LIKE @SearchText)");
                parameters.Add("SearchText", $"%{searchText}%");
            }

            if (dateDebut.HasValue)
            {
                whereConditions.Add("a.DateAchat >= @DateDebut");
                parameters.Add("DateDebut", dateDebut.Value);
            }

            if (dateFin.HasValue)
            {
                whereConditions.Add("a.DateAchat <= @DateFin");
                parameters.Add("DateFin", dateFin.Value);
            }

            var whereClause = whereConditions.Any() ? "WHERE " + string.Join(" AND ", whereConditions) : "";

            var sql = $@"
                SELECT a.*, f.Nom as NomFournisseur, u.NomComplet as NomUtilisateur
                FROM Achats a
                LEFT JOIN Fournisseurs f ON a.FournisseurId = f.Id
                LEFT JOIN Utilisateurs u ON a.UtilisateurId = u.Id
                {whereClause}
                ORDER BY a.DateAchat DESC";

            return await connection.QueryAsync<Achat>(sql, parameters);
        }

        public async Task<IEnumerable<Achat>> GetAchatsByFournisseurAsync(int fournisseurId)
        {
            using var connection = _context.GetConnection();

            const string sql = @"
                SELECT a.*, f.Nom as NomFournisseur, u.NomComplet as NomUtilisateur
                FROM Achats a
                LEFT JOIN Fournisseurs f ON a.FournisseurId = f.Id
                LEFT JOIN Utilisateurs u ON a.UtilisateurId = u.Id
                WHERE a.FournisseurId = @FournisseurId
                ORDER BY a.DateAchat DESC";

            return await connection.QueryAsync<Achat>(sql, new { FournisseurId = fournisseurId });
        }

        public async Task<IEnumerable<Achat>> GetAchatsByPeriodAsync(DateTime dateDebut, DateTime dateFin)
        {
            using var connection = _context.GetConnection();

            const string sql = @"
                SELECT a.*, f.Nom as NomFournisseur, u.NomComplet as NomUtilisateur
                FROM Achats a
                LEFT JOIN Fournisseurs f ON a.FournisseurId = f.Id
                LEFT JOIN Utilisateurs u ON a.UtilisateurId = u.Id
                WHERE a.DateAchat >= @DateDebut AND a.DateAchat <= @DateFin
                ORDER BY a.DateAchat DESC";

            return await connection.QueryAsync<Achat>(sql, new { DateDebut = dateDebut, DateFin = dateFin });
        }

        // الوظائف الأساسية - سيتم تطويرها لاحقاً
        public Task<Achat> CreateAchatAsync(Achat achat)
        {
            throw new NotImplementedException("سيتم تطويرها لاحقاً");
        }

        public Task<Achat> UpdateAchatAsync(Achat achat)
        {
            throw new NotImplementedException("سيتم تطويرها لاحقاً");
        }

        public Task<bool> DeleteAchatAsync(int id)
        {
            throw new NotImplementedException("سيتم تطويرها لاحقاً");
        }

        public async Task<string> GenerateNumeroAchatAsync()
        {
            using var connection = _context.GetConnection();

            var today = DateTime.Now;
            var prefix = $"ACH{today:yyyyMM}";

            const string sql = @"
                SELECT NumeroFacture
                FROM Achats
                WHERE NumeroFacture LIKE @Prefix || '%'
                ORDER BY NumeroFacture DESC
                LIMIT 1";

            var lastNumber = await connection.QueryFirstOrDefaultAsync<string>(sql, new { Prefix = prefix });

            int nextNumber = 1;
            if (!string.IsNullOrEmpty(lastNumber))
            {
                var numberPart = lastNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int currentNumber))
                {
                    nextNumber = currentNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        public Task<decimal> GetTotalAchatsAsync(DateTime? dateDebut = null, DateTime? dateFin = null)
        {
            throw new NotImplementedException("سيتم تطويرها لاحقاً");
        }

        public Task<IEnumerable<Achat>> GetAchatsEnAttenteAsync()
        {
            throw new NotImplementedException("سيتم تطويرها لاحقاً");
        }

        public Task<bool> MarquerCommeRecuAsync(int achatId)
        {
            throw new NotImplementedException("سيتم تطويرها لاحقاً");
        }

        public Task<bool> AnnulerAchatAsync(int achatId)
        {
            throw new NotImplementedException("سيتم تطويرها لاحقاً");
        }
    }
}
