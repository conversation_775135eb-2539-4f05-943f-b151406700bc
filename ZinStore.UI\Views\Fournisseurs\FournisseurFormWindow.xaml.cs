using System;
using System.Threading.Tasks;
using System.Windows;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.Views.Fournisseurs
{
    /// <summary>
    /// Logique d'interaction pour FournisseurFormWindow.xaml
    /// </summary>
    public partial class FournisseurFormWindow : Window
    {
        private readonly FournisseurService _fournisseurService;
        private Fournisseur _currentFournisseur;
        private bool _isEditMode;

        public bool FournisseurSaved { get; private set; }

        public FournisseurFormWindow(Fournisseur fournisseur = null)
        {
            InitializeComponent();
            _fournisseurService = new FournisseurService(new DatabaseContext());
            
            if (fournisseur != null)
            {
                _currentFournisseur = fournisseur;
                _isEditMode = true;
                TitleText.Text = "Modifier Fournisseur";
                LoadFournisseurData();
            }
            else
            {
                _currentFournisseur = new Fournisseur();
                _isEditMode = false;
                TitleText.Text = "Nouveau Fournisseur";
                GenerateFournisseurCode();
            }
        }

        private void LoadFournisseurData()
        {
            try
            {
                CodeFournisseurTextBox.Text = _currentFournisseur.CodeFournisseur;
                RaisonSocialeTextBox.Text = _currentFournisseur.RaisonSociale;
                NomCommercialTextBox.Text = _currentFournisseur.NomCommercial;
                PersonneContactTextBox.Text = _currentFournisseur.PersonneContact;
                TelephoneTextBox.Text = _currentFournisseur.Telephone;
                EmailTextBox.Text = _currentFournisseur.Email;
                FaxTextBox.Text = _currentFournisseur.Fax;
                AdresseTextBox.Text = _currentFournisseur.Adresse;
                VilleTextBox.Text = _currentFournisseur.Ville;
                CodePostalTextBox.Text = _currentFournisseur.CodePostal;
                PaysTextBox.Text = _currentFournisseur.Pays;
                ConditionsPaiementComboBox.Text = _currentFournisseur.ConditionsPaiement;
                DelaiLivraisonTextBox.Text = _currentFournisseur.DelaiLivraison.ToString();
                SoldeCompteTextBox.Text = _currentFournisseur.SoldeCompte.ToString("F2");
                LimiteCreditTextBox.Text = _currentFournisseur.LimiteCredit.ToString("F2");
                NumeroTVATextBox.Text = _currentFournisseur.NumeroTVA;
                RegistreCommerceTextBox.Text = _currentFournisseur.RegistreCommerce;
                EstActifCheckBox.IsChecked = _currentFournisseur.EstActif;
                NotesTextBox.Text = _currentFournisseur.Notes;
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
        }

        private void GenerateFournisseurCode()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                CodeFournisseurTextBox.Text = $"FR{timestamp.Substring(8)}";
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la génération du code: {ex.Message}");
            }
        }

        private async void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                ShowProgress();

                // Remplir l'objet fournisseur avec les données du formulaire
                _currentFournisseur.CodeFournisseur = CodeFournisseurTextBox.Text.Trim();
                _currentFournisseur.RaisonSociale = RaisonSocialeTextBox.Text.Trim();
                _currentFournisseur.NomCommercial = NomCommercialTextBox.Text.Trim();
                _currentFournisseur.PersonneContact = PersonneContactTextBox.Text.Trim();
                _currentFournisseur.Telephone = TelephoneTextBox.Text.Trim();
                _currentFournisseur.Email = EmailTextBox.Text.Trim();
                _currentFournisseur.Fax = FaxTextBox.Text.Trim();
                _currentFournisseur.Adresse = AdresseTextBox.Text.Trim();
                _currentFournisseur.Ville = VilleTextBox.Text.Trim();
                _currentFournisseur.CodePostal = CodePostalTextBox.Text.Trim();
                _currentFournisseur.Pays = PaysTextBox.Text.Trim();
                _currentFournisseur.ConditionsPaiement = ConditionsPaiementComboBox.Text;
                _currentFournisseur.NumeroTVA = NumeroTVATextBox.Text.Trim();
                _currentFournisseur.RegistreCommerce = RegistreCommerceTextBox.Text.Trim();
                _currentFournisseur.EstActif = EstActifCheckBox.IsChecked ?? true;
                _currentFournisseur.Notes = NotesTextBox.Text.Trim();

                // Convertir les valeurs numériques
                if (int.TryParse(DelaiLivraisonTextBox.Text, out int delaiLivraison))
                    _currentFournisseur.DelaiLivraison = delaiLivraison;

                if (decimal.TryParse(SoldeCompteTextBox.Text, out decimal solde))
                    _currentFournisseur.SoldeCompte = solde;

                if (decimal.TryParse(LimiteCreditTextBox.Text, out decimal limite))
                    _currentFournisseur.LimiteCredit = limite;

                // Sauvegarder
                bool success;
                string message;
                if (_isEditMode)
                {
                    var result = await _fournisseurService.UpdateFournisseurAsync(_currentFournisseur);
                    success = result.Success;
                    message = result.Message;
                }
                else
                {
                    _currentFournisseur.DateCreation = DateTime.Now;
                    var result = await _fournisseurService.AddFournisseurAsync(_currentFournisseur);
                    success = result.Success;
                    message = result.Message;
                }

                HideProgress();

                if (success)
                {
                    FournisseurSaved = true;
                    MessageBoxHelper.ShowSuccess(message);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBoxHelper.ShowError(message);
                }
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBoxHelper.ShowError($"Erreur lors de l'enregistrement: {ex.Message}");
            }
        }

        private bool ValidateForm()
        {
            // Vérifier les champs obligatoires
            if (string.IsNullOrWhiteSpace(CodeFournisseurTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("Le code fournisseur est obligatoire.");
                CodeFournisseurTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(RaisonSocialeTextBox.Text))
            {
                MessageBoxHelper.ShowWarning("La raison sociale est obligatoire.");
                RaisonSocialeTextBox.Focus();
                return false;
            }

            // Valider l'email si fourni
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                if (!IsValidEmail(EmailTextBox.Text))
                {
                    MessageBoxHelper.ShowWarning("L'adresse email n'est pas valide.");
                    EmailTextBox.Focus();
                    return false;
                }
            }

            // Valider les montants
            if (!string.IsNullOrWhiteSpace(SoldeCompteTextBox.Text))
            {
                if (!decimal.TryParse(SoldeCompteTextBox.Text, out _))
                {
                    MessageBoxHelper.ShowWarning("Le solde du compte doit être un nombre valide.");
                    SoldeCompteTextBox.Focus();
                    return false;
                }
            }

            if (!string.IsNullOrWhiteSpace(LimiteCreditTextBox.Text))
            {
                if (!decimal.TryParse(LimiteCreditTextBox.Text, out _))
                {
                    MessageBoxHelper.ShowWarning("La limite de crédit doit être un nombre valide.");
                    LimiteCreditTextBox.Focus();
                    return false;
                }
            }

            if (!string.IsNullOrWhiteSpace(DelaiLivraisonTextBox.Text))
            {
                if (!int.TryParse(DelaiLivraisonTextBox.Text, out _))
                {
                    MessageBoxHelper.ShowWarning("Le délai de livraison doit être un nombre entier.");
                    DelaiLivraisonTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void ShowProgress()
        {
            ProgressOverlay.Visibility = Visibility.Visible;
            SaveButton.IsEnabled = false;
        }

        private void HideProgress()
        {
            ProgressOverlay.Visibility = Visibility.Collapsed;
            SaveButton.IsEnabled = true;
        }
    }
}
