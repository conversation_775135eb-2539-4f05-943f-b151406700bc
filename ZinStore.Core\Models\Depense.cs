using System;
using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les dépenses
    /// </summary>
    public class Depense : BaseEntity
    {
        [Required(ErrorMessage = "Le numéro de référence est obligatoire")]
        [StringLength(50)]
        public string NumeroReference { get; set; }

        [Required]
        public DateTime DateDepense { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "La catégorie de dépense est obligatoire")]
        [StringLength(100)]
        public string CategorieDepense { get; set; }

        [Required(ErrorMessage = "La description est obligatoire")]
        [StringLength(200)]
        public string Description { get; set; }

        [Required]
        public decimal Montant { get; set; }

        public decimal MontantTVA { get; set; } = 0;

        public decimal MontantTotal { get; set; }

        [StringLength(50)]
        public string ModePaiement { get; set; }

        [StringLength(100)]
        public string Beneficiaire { get; set; }

        [StringLength(50)]
        public string NumeroFacture { get; set; }

        public int? FournisseurId { get; set; }

        public int UtilisateurId { get; set; }

        public bool EstValidee { get; set; } = false;

        public DateTime? DateValidation { get; set; }

        public int? UtilisateurValidation { get; set; }

        public string Notes { get; set; }

        [StringLength(200)]
        public string PieceJointe { get; set; }

        // Propriétés de navigation
        public virtual Fournisseur Fournisseur { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
    }
}
