using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class FournisseurRepository : BaseRepository<Fournisseur>
    {
        public FournisseurRepository(DatabaseContext context) : base(context, "Fournisseurs")
        {
        }

        public override async Task<IEnumerable<Fournisseur>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT * FROM Fournisseurs 
                WHERE EstSupprime = 0 
                AND (CodeFournisseur LIKE @SearchTerm OR Nom LIKE @SearchTerm OR RaisonSociale LIKE @SearchTerm)
                ORDER BY Nom";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Fournisseur>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<bool> CodeExistsAsync(string codeFournisseur, int? excludeId = null)
        {
            var sql = "SELECT COUNT(*) FROM Fournisseurs WHERE CodeFournisseur = @CodeFournisseur AND EstSupprime = 0";
            if (excludeId.HasValue) sql += " AND Id != @ExcludeId";

            using (var connection = _context.GetConnection())
            {
                var count = await connection.QuerySingleAsync<int>(sql, 
                    new { CodeFournisseur = codeFournisseur, ExcludeId = excludeId });
                return count > 0;
            }
        }

        public async Task<IEnumerable<Fournisseur>> GetActiveFournisseursAsync()
        {
            var sql = "SELECT * FROM Fournisseurs WHERE EstActif = 1 AND EstSupprime = 0 ORDER BY Nom";
            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Fournisseur>(sql);
            }
        }

        public async Task<string> GenerateNextCodeAsync(string prefix = "FR")
        {
            var sql = @"
                SELECT COALESCE(MAX(CAST(SUBSTR(CodeFournisseur, LENGTH(@Prefix) + 1) AS INTEGER)), 0) + 1 
                FROM Fournisseurs WHERE CodeFournisseur LIKE @Pattern AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                var nextNumber = await connection.QuerySingleAsync<int>(sql, 
                    new { Prefix = prefix, Pattern = $"{prefix}%" });
                return $"{prefix}{nextNumber:D6}";
            }
        }
    }
}
