using System;
using System.Windows;
using ZinStore.Data.Context;
using ZinStore.UI.Views;

namespace ZinStore.UI
{
    /// <summary>
    /// Logique d'interaction pour App.xaml
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);

            try
            {
                // Vérifier et initialiser la base de données
                if (!CheckAndInitializeDatabase())
                {
                    // L'utilisateur a annulé la configuration ou une erreur s'est produite
                    Shutdown();
                    return;
                }

                // Démarrer l'application normalement
                StartMainApplication();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'initialisation de l'application: {ex.Message}",
                    "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        private bool CheckAndInitializeDatabase()
        {
            try
            {
                // Essayer de créer une connexion à la base de données
                using (var context = new DatabaseContext())
                {
                    // Tester la connexion
                    if (context.TestConnection())
                    {
                        // La base de données existe et fonctionne
                        var initializer = new DatabaseInitializer(context);
                        initializer.Initialize();
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur de connexion à la base de données: {ex.Message}");

                // Afficher la fenêtre de configuration de la base de données
                return ShowDatabaseSetup();
            }

            // Si on arrive ici, la connexion a échoué
            return ShowDatabaseSetup();
        }

        private bool ShowDatabaseSetup()
        {
            try
            {
                var setupWindow = new DatabaseSetupWindow();
                var result = setupWindow.ShowDialog();

                if (result == true && setupWindow.DatabaseConfigured)
                {
                    // Tester la nouvelle configuration
                    try
                    {
                        using (var context = new DatabaseContext(setupWindow.ConnectionString))
                        {
                            if (context.TestConnection())
                            {
                                var initializer = new DatabaseInitializer(context);
                                initializer.Initialize();
                                return true;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la validation de la nouvelle configuration: {ex.Message}",
                                      "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de la configuration de la base de données: {ex.Message}",
                              "Erreur", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private void StartMainApplication()
        {
            // Créer et afficher la fenêtre de connexion
            var loginWindow = new LoginWindow();
            loginWindow.Show();
        }
    }
}
