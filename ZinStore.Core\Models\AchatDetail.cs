using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les détails des achats
    /// </summary>
    public class AchatDetail : BaseEntity
    {
        [Required]
        public int AchatId { get; set; }

        [Required]
        public int ProduitId { get; set; }

        [Required]
        public decimal Quantite { get; set; }

        [Required]
        public decimal PrixUnitaire { get; set; }

        public decimal TauxTVA { get; set; }

        public decimal MontantTVA { get; set; }

        public decimal TauxRemise { get; set; } = 0;

        public decimal MontantRemise { get; set; } = 0;

        public decimal SousTotal { get; set; }

        public decimal Total { get; set; }

        public decimal QuantiteRecue { get; set; } = 0;

        public string Notes { get; set; }

        // Propriétés de navigation
        public virtual Achat Achat { get; set; }
        public virtual Produit Produit { get; set; }

        // Propriétés calculées
        public decimal QuantiteRestante => Quantite - QuantiteRecue;
        public bool EstCompletementRecue => QuantiteRecue >= Quantite;
    }
}
