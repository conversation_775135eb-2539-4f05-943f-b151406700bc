using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;

namespace ZinStore.Business.Services
{
    public class ComptabiliteService
    {
        private readonly DepenseRepository _depenseRepository;
        private readonly RevenuRepository _revenuRepository;
        private readonly CompteGeneralRepository _compteRepository;

        public ComptabiliteService(DatabaseContext context)
        {
            _depenseRepository = new DepenseRepository(context);
            _revenuRepository = new RevenuRepository(context);
            _compteRepository = new CompteGeneralRepository(context);
        }

        public async Task<IEnumerable<Depense>> GetAllDepensesAsync()
        {
            return await _depenseRepository.GetAllAsync();
        }

        public async Task<IEnumerable<Revenu>> GetAllRevenusAsync()
        {
            return await _revenuRepository.GetAllAsync();
        }

        public async Task<IEnumerable<CompteGeneral>> GetAllComptesAsync()
        {
            return await _compteRepository.GetAllAsync();
        }
    }
}
