<Window x:Class="ZinStore.UI.Views.Ventes.VenteFormWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Nouvelle Vente - ZinStore"
        Height="750"
        Width="900"
        WindowStartupLocation="CenterOwner"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ShoppingCart" 
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="Nouvelle Vente"
                              FontSize="20"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="N° Facture:"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Margin="0,0,10,0"/>
                    <TextBlock x:Name="NumeroFactureTextBlock"
                              Text="FAC-20240101-001"
                              FontSize="16"
                              FontWeight="Bold"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Informations client et vente -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Client -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="Client" FontWeight="Bold" Margin="0,0,0,10"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <ComboBox x:Name="ClientComboBox"
                                 Grid.Column="0"
                                 materialDesign:HintAssist.Hint="Sélectionner un client"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 DisplayMemberPath="NomComplet"
                                 SelectedValuePath="Id"/>
                        <Button Grid.Column="1"
                               Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="Nouveau client"
                               Click="NouveauClient_Click"
                               Margin="5,0,0,0">
                            <materialDesign:PackIcon Kind="AccountPlus"/>
                        </Button>
                    </Grid>
                </StackPanel>

                <!-- Date -->
                <StackPanel Grid.Column="2">
                    <TextBlock Text="Date de Vente" FontWeight="Bold" Margin="0,0,0,10"/>
                    <DatePicker x:Name="DateVenteDatePicker"
                               Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>
                </StackPanel>

                <!-- Mode de paiement -->
                <StackPanel Grid.Column="4">
                    <TextBlock Text="Mode de Paiement" FontWeight="Bold" Margin="0,0,0,10"/>
                    <ComboBox x:Name="ModePaiementComboBox"
                             Style="{StaticResource MaterialDesignOutlinedComboBox}">
                        <ComboBoxItem Content="Espèces" IsSelected="True"/>
                        <ComboBoxItem Content="Carte bancaire"/>
                        <ComboBoxItem Content="Chèque"/>
                        <ComboBoxItem Content="Virement"/>
                        <ComboBoxItem Content="Crédit"/>
                    </ComboBox>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des produits -->
        <materialDesign:Card Grid.Row="2" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Titre et bouton d'ajout -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0"
                              Text="Articles"
                              FontWeight="Bold"
                              FontSize="16"
                              VerticalAlignment="Center"/>
                    
                    <Button Grid.Column="1"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Click="AjouterArticle_Click">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" VerticalAlignment="Center"/>
                            <TextBlock Text="Ajouter Article" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </Grid>

                <!-- Barre de recherche produit -->
                <Grid Grid.Row="1" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox x:Name="RechercherProduitTextBox"
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="Rechercher un produit (nom, code, code-barres)..."
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            KeyDown="RechercherProduit_KeyDown"/>
                    
                    <Button Grid.Column="1"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Click="RechercherProduit_Click"
                           Margin="5,0,0,0">
                        <materialDesign:PackIcon Kind="Magnify"/>
                    </Button>
                </Grid>

                <!-- DataGrid des articles -->
                <DataGrid x:Name="ArticlesDataGrid"
                         Grid.Row="2"
                         Style="{StaticResource CustomDataGrid}"
                         AutoGenerateColumns="False"
                         CanUserAddRows="False">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Code" 
                                          Binding="{Binding CodeProduit}" 
                                          Width="100"
                                          IsReadOnly="True"/>
                        <DataGridTextColumn Header="Désignation" 
                                          Binding="{Binding NomProduit}" 
                                          Width="250"
                                          IsReadOnly="True"/>
                        <DataGridTextColumn Header="Prix Unit." 
                                          Binding="{Binding PrixUnitaire, StringFormat='{}{0:F2}'}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="Quantité" 
                                          Binding="{Binding Quantite, StringFormat='{}{0:F2}'}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="Remise %" 
                                          Binding="{Binding PourcentageRemise, StringFormat='{}{0:F2}'}" 
                                          Width="100"/>
                        <DataGridTextColumn Header="Total HT" 
                                          Binding="{Binding TotalHT, StringFormat='{}{0:F2}'}" 
                                          Width="120"
                                          IsReadOnly="True"/>
                        <DataGridTemplateColumn Header="Actions" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Style="{StaticResource MaterialDesignIconButton}"
                                           Click="SupprimerArticle_Click"
                                           ToolTip="Supprimer">
                                        <materialDesign:PackIcon Kind="Delete" Foreground="Red"/>
                                    </Button>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- Totaux -->
        <materialDesign:Card Grid.Row="3" Padding="20" Margin="0,20,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>

                <!-- Notes -->
                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                    <TextBlock Text="Notes" FontWeight="Bold" Margin="0,0,0,10"/>
                    <TextBox x:Name="NotesTextBox"
                            materialDesign:HintAssist.Hint="Notes sur la vente..."
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Height="80"
                            TextWrapping="Wrap"
                            AcceptsReturn="True"
                            VerticalScrollBarVisibility="Auto"/>
                </StackPanel>

                <!-- Calculs -->
                <StackPanel Grid.Column="1">
                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Sous-total HT:" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" x:Name="SousTotalHTTextBlock" Text="0.00 DA" FontWeight="Bold"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Remise totale:"/>
                        <TextBlock Grid.Column="1" x:Name="RemiseTotaleTextBlock" Text="0.00 DA"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="TVA (19%):"/>
                        <TextBlock Grid.Column="1" x:Name="TVATextBlock" Text="0.00 DA"/>
                    </Grid>
                    
                    <Separator Margin="0,5"/>
                    
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Total TTC:" FontWeight="Bold" FontSize="16"/>
                        <TextBlock Grid.Column="1" x:Name="TotalTTCTextBlock" Text="0.00 DA" FontWeight="Bold" FontSize="16" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </Grid>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Boutons d'action -->
        <StackPanel Grid.Row="4" 
                   Orientation="Horizontal" 
                   HorizontalAlignment="Right" 
                   Margin="0,20,0,0">
            <Button Content="Annuler"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="Cancel_Click"
                   Width="100"
                   Margin="0,0,10,0"/>
            <Button Content="Enregistrer et Imprimer"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   Click="SaveAndPrint_Click"
                   Width="180"
                   Margin="0,0,10,0"/>
            <Button x:Name="SaveButton"
                   Content="Enregistrer"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="Save_Click"
                   Width="120"/>
        </StackPanel>

        <!-- Indicateur de progression -->
        <Grid x:Name="ProgressOverlay" 
              Grid.RowSpan="5"
              Background="#80000000"
              Visibility="Collapsed">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           IsIndeterminate="True"
                           Width="50" Height="50"
                           Margin="0,0,0,20"/>
                <TextBlock Text="Enregistrement en cours..."
                          Foreground="White"
                          FontSize="14"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
