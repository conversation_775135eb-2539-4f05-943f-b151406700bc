<Window x:Class="ZinStore.UI.Views.Stock.InventaireWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
        Title="Inventaire du Stock"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}">

    <Window.DataContext>
        <viewModels:InventaireViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ClipboardList" 
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="📋 Inventaire du Stock"
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="Date:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding DateInventaire, StringFormat='dd/MM/yyyy'}" 
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Statistiques -->
        <UniformGrid Grid.Row="1" Rows="1" Columns="4" Margin="10,0,10,10">
            <!-- Total produits -->
            <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                               Background="{DynamicResource PrimaryHueMidBrush}"
                               Margin="5" Padding="15">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0" Kind="Package"
                                               Width="24" Height="24"
                                               Foreground="White" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="{Binding TotalProduits}"
                                  FontSize="18" FontWeight="Bold" Foreground="White"
                                  VerticalAlignment="Center" HorizontalAlignment="Right"/>
                    </Grid>
                    <TextBlock Text="Total Produits" FontSize="12" Foreground="White" Opacity="0.8"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Produits vérifiés -->
            <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                               Background="#4CAF50" Margin="5" Padding="15">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0" Kind="CheckCircle"
                                               Width="24" Height="24"
                                               Foreground="White" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="{Binding ProduitsVerifies}"
                                  FontSize="18" FontWeight="Bold" Foreground="White"
                                  VerticalAlignment="Center" HorizontalAlignment="Right"/>
                    </Grid>
                    <TextBlock Text="Vérifiés" FontSize="12" Foreground="White" Opacity="0.8"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Écarts détectés -->
            <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                               Background="#FF9800" Margin="5" Padding="15">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0" Kind="AlertTriangle"
                                               Width="24" Height="24"
                                               Foreground="White" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="{Binding EcartsDetectes}"
                                  FontSize="18" FontWeight="Bold" Foreground="White"
                                  VerticalAlignment="Center" HorizontalAlignment="Right"/>
                    </Grid>
                    <TextBlock Text="Écarts" FontSize="12" Foreground="White" Opacity="0.8"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Progression -->
            <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                               Background="#2196F3" Margin="5" Padding="15">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <materialDesign:PackIcon Grid.Column="0" Kind="ProgressCheck"
                                               Width="24" Height="24"
                                               Foreground="White" VerticalAlignment="Center"/>
                        <TextBlock Grid.Column="1" Text="{Binding ProgressionPourcentage, StringFormat='{}{0:F0}%'}"
                                  FontSize="18" FontWeight="Bold" Foreground="White"
                                  VerticalAlignment="Center" HorizontalAlignment="Right"/>
                    </Grid>
                    <TextBlock Text="Progression" FontSize="12" Foreground="White" Opacity="0.8"/>
                </StackPanel>
            </materialDesign:Card>
        </UniformGrid>

        <!-- Contenu principal -->
        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>

            <!-- Liste des produits -->
            <materialDesign:Card Grid.Column="0" Padding="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- En-tête -->
                    <Border Grid.Row="0" Background="{DynamicResource MaterialDesignToolBarBackground}" Padding="15,10">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="Inventaire des Produits"
                                      Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                      FontWeight="Bold"/>
                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                       Command="{Binding ScanBarcodeCommand}"
                                       ToolTip="Scanner code-barres">
                                    <materialDesign:PackIcon Kind="Barcode"/>
                                </Button>
                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                       Command="{Binding ImportCommand}"
                                       ToolTip="Importer données">
                                    <materialDesign:PackIcon Kind="Import"/>
                                </Button>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- Recherche -->
                    <Border Grid.Row="1" Padding="15">
                        <TextBox materialDesign:HintAssist.Hint="Rechercher un produit ou scanner code-barres..."
                                materialDesign:HintAssist.IsFloating="True"
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>
                    </Border>

                    <!-- DataGrid -->
                    <DataGrid Grid.Row="2" ItemsSource="{Binding ProduitsInventaire}"
                             Style="{StaticResource MaterialDesignDataGrid}"
                             AutoGenerateColumns="False"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             Margin="15">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Code" Binding="{Binding CodeProduit}" Width="100"/>
                            <DataGridTextColumn Header="Nom" Binding="{Binding NomProduit}" Width="200"/>
                            <DataGridTextColumn Header="Stock Système" Binding="{Binding StockSysteme}" Width="100"/>
                            <DataGridTemplateColumn Header="Stock Physique" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBox Text="{Binding StockPhysique, UpdateSourceTrigger=PropertyChanged}"
                                                Style="{StaticResource MaterialDesignTextBox}"
                                                HorizontalAlignment="Stretch"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="Écart" Binding="{Binding Ecart}" Width="80">
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding AEcart}" Value="True">
                                                <Setter Property="Foreground" Value="Red"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                            <DataGridCheckBoxColumn Header="Vérifié" Binding="{Binding EstVerifie}" Width="80"/>
                            <DataGridTemplateColumn Header="Actions" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.ValiderCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Valider">
                                                <materialDesign:PackIcon Kind="Check" Foreground="Green"/>
                                            </Button>
                                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                                   Command="{Binding DataContext.AjusterCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                   CommandParameter="{Binding}"
                                                   ToolTip="Ajuster">
                                                <materialDesign:PackIcon Kind="Tune" Foreground="Orange"/>
                                            </Button>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>

            <!-- Panneau latéral -->
            <StackPanel Grid.Column="2">
                <!-- Actions rapides -->
                <materialDesign:Card Padding="15" Margin="0,0,0,10">
                    <StackPanel>
                        <TextBlock Text="Actions Rapides"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Button Content="📊 Générer Rapport"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Command="{Binding GenererRapportCommand}"
                               Margin="0,0,0,10"/>
                        
                        <Button Content="💾 Sauvegarder"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding SauvegarderCommand}"
                               Margin="0,0,0,10"/>
                        
                        <Button Content="🔄 Tout Valider"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding ValiderToutCommand}"
                               Margin="0,0,0,10"/>
                        
                        <Button Content="📤 Exporter"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding ExporterCommand}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Résumé des écarts -->
                <materialDesign:Card Padding="15">
                    <StackPanel>
                        <TextBlock Text="Résumé des Écarts"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="Écarts positifs:" FontSize="12"/>
                            <TextBlock Grid.Column="1" Text="{Binding EcartsPositifs}" 
                                      FontWeight="Bold" Foreground="Green"/>
                        </Grid>
                        
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="Écarts négatifs:" FontSize="12"/>
                            <TextBlock Grid.Column="1" Text="{Binding EcartsNegatifs}" 
                                      FontWeight="Bold" Foreground="Red"/>
                        </Grid>
                        
                        <Separator Margin="0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Text="Valeur des écarts:" FontWeight="Bold"/>
                            <TextBlock Grid.Column="1" Text="{Binding ValeurEcarts, StringFormat='{}{0:N2} DA'}" 
                                      FontWeight="Bold" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>

        <!-- Boutons d'action -->
        <materialDesign:Card Grid.Row="3" Padding="20" Margin="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <CheckBox Content="Appliquer automatiquement les ajustements"
                             IsChecked="{Binding AppliquerAjustements}"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="✅ Finaliser Inventaire"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding FinaliserCommand}"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Foreground="White"
                           Margin="0,0,10,0"
                           Height="40" Padding="20,0"/>
                    <Button Content="❌ Fermer"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding FermerCommand}"
                           Height="40" Padding="20,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</Window>
