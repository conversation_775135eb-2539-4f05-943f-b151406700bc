-- =====================================================
-- Script de finalisation et configuration ZinStore
-- Configuration finale et vérifications
-- Version: MySQL 8.0+
-- =====================================================

USE zinstore;

-- =====================================================
-- CONFIGURATION DES PRIVILÈGES ET SÉCURITÉ
-- =====================================================

-- Créer un utilisateur dédié pour l'application (optionnel)
-- CREATE USER 'zinstore_app'@'localhost' IDENTIFIED BY 'mot_de_passe_securise';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON zinstore.* TO 'zinstore_app'@'localhost';
-- GRANT EXECUTE ON zinstore.* TO 'zinstore_app'@'localhost';

-- Créer un utilisateur en lecture seule pour les rapports (optionnel)
-- CREATE USER 'zinstore_readonly'@'localhost' IDENTIFIED BY 'mot_de_passe_lecture';
-- GRANT SELECT ON zinstore.* TO 'zinstore_readonly'@'localhost';

-- =====================================================
-- INSERTION DES DONNÉES INITIALES STOCK
-- =====================================================

-- Initialiser le stock pour tous les produits existants
INSERT INTO stock (produit_id, quantite_actuelle, quantite_reservee)
SELECT id, 0, 0 
FROM produits 
WHERE id NOT IN (SELECT produit_id FROM stock);

-- Ajouter du stock initial pour les produits d'exemple
UPDATE stock s
JOIN produits p ON s.produit_id = p.id
SET s.quantite_actuelle = CASE 
    WHEN p.code_produit LIKE 'SAMS%' THEN 25
    WHEN p.code_produit LIKE 'HP%' THEN 10
    WHEN p.code_produit LIKE 'JBL%' THEN 50
    WHEN p.code_produit LIKE 'LG%' THEN 5
    WHEN p.code_produit LIKE 'PANA%' THEN 15
    WHEN p.code_produit LIKE 'TSHIRT%' THEN 100
    WHEN p.code_produit LIKE 'JEAN%' THEN 75
    WHEN p.code_produit LIKE 'NIKE%' THEN 40
    WHEN p.code_produit LIKE 'HUILE%' THEN 200
    WHEN p.code_produit LIKE 'RIZ%' THEN 150
    WHEN p.code_produit LIKE 'THE%' THEN 180
    WHEN p.code_produit LIKE 'FILTRE%' THEN 80
    ELSE 20
END;

-- =====================================================
-- CRÉATION DE VENTES D'EXEMPLE
-- =====================================================

-- Vente 1: Client particulier
INSERT INTO ventes (numero_facture, client_id, utilisateur_id, date_vente, remise, mode_paiement, montant_paye, statut_paiement) 
VALUES ('FV202412010001', 1, 3, '2024-12-01 10:30:00', 0.00, 'Espèces', 0.00, 'En attente');

SET @vente1_id = LAST_INSERT_ID();

INSERT INTO ventes_details (vente_id, produit_id, quantite, prix_unitaire) VALUES
(@vente1_id, 1, 1, 45000.00),  -- Smartphone Samsung
(@vente1_id, 3, 2, 12000.00);  -- Écouteurs JBL

-- Vente 2: Client entreprise
INSERT INTO ventes (numero_facture, client_id, utilisateur_id, date_vente, remise, mode_paiement, montant_paye, statut_paiement) 
VALUES ('FV202412010002', 4, 3, '2024-12-01 14:15:00', 5000.00, 'Virement', 0.00, 'En attente');

SET @vente2_id = LAST_INSERT_ID();

INSERT INTO ventes_details (vente_id, produit_id, quantite, prix_unitaire) VALUES
(@vente2_id, 2, 3, 110000.00),  -- Laptop HP
(@vente2_id, 4, 2, 38000.00);   -- Tablette Samsung

-- Vente 3: Vente comptoir (sans client)
INSERT INTO ventes (numero_facture, client_id, utilisateur_id, date_vente, remise, mode_paiement, montant_paye, statut_paiement) 
VALUES ('FV202412010003', NULL, 4, '2024-12-01 16:45:00', 0.00, 'Carte', 0.00, 'En attente');

SET @vente3_id = LAST_INSERT_ID();

INSERT INTO ventes_details (vente_id, produit_id, quantite, prix_unitaire) VALUES
(@vente3_id, 8, 3, 1500.00),   -- T-shirt
(@vente3_id, 9, 1, 4200.00),   -- Jean
(@vente3_id, 11, 5, 650.00);   -- Huile d'olive

-- Marquer les ventes comme payées
UPDATE ventes SET 
    montant_paye = montant_total,
    statut_paiement = 'Payé'
WHERE id IN (@vente1_id, @vente2_id, @vente3_id);

-- =====================================================
-- CRÉATION D'ACHATS D'EXEMPLE
-- =====================================================

-- Achat 1: Réapprovisionnement électronique
INSERT INTO achats (numero_commande, fournisseur_id, utilisateur_id, date_commande, date_livraison_prevue, statut_commande, statut_paiement, mode_paiement) 
VALUES ('CMD202412010001', 1, 2, '2024-12-01 09:00:00', '2024-12-08', 'Confirmé', 'En attente', 'Virement');

SET @achat1_id = LAST_INSERT_ID();

INSERT INTO achats_details (achat_id, produit_id, quantite_commandee, quantite_recue, prix_unitaire) VALUES
(@achat1_id, 1, 20, 20, 35000.00),  -- Smartphone Samsung
(@achat1_id, 3, 50, 50, 8500.00),   -- Écouteurs JBL
(@achat1_id, 4, 15, 15, 28000.00);  -- Tablette Samsung

-- Achat 2: Réapprovisionnement mode
INSERT INTO achats (numero_commande, fournisseur_id, utilisateur_id, date_commande, date_livraison_prevue, statut_commande, statut_paiement, mode_paiement) 
VALUES ('CMD202412010002', 3, 2, '2024-12-01 11:30:00', '2024-12-15', 'Expédié', 'En attente', 'Chèque');

SET @achat2_id = LAST_INSERT_ID();

INSERT INTO achats_details (achat_id, produit_id, quantite_commandee, quantite_recue, prix_unitaire) VALUES
(@achat2_id, 8, 100, 0, 800.00),   -- T-shirt (pas encore reçu)
(@achat2_id, 9, 50, 0, 2500.00),   -- Jean (pas encore reçu)
(@achat2_id, 10, 30, 0, 8500.00);  -- Chaussures (pas encore reçu)

-- =====================================================
-- CRÉATION DE MOUVEMENTS FINANCIERS
-- =====================================================

-- Revenus divers
INSERT INTO revenus (description, montant, categorie, utilisateur_id, date_revenu) VALUES
('Service de livraison', 2500.00, 'Service', 2, '2024-12-01'),
('Intérêts bancaires', 1200.00, 'Intérêt', 2, '2024-12-01'),
('Vente d\'ancien matériel', 15000.00, 'Autre', 2, '2024-11-30');

-- Dépenses diverses
INSERT INTO depenses (description, montant, categorie, utilisateur_id, date_depense, mode_paiement) VALUES
('Loyer magasin décembre', 45000.00, 'Loyer', 2, '2024-12-01', 'Virement'),
('Facture électricité', 8500.00, 'Électricité', 2, '2024-12-01', 'Prélèvement'),
('Frais de transport', 3200.00, 'Transport', 2, '2024-12-01', 'Espèces'),
('Publicité Facebook', 5000.00, 'Marketing', 2, '2024-11-30', 'Carte');

-- =====================================================
-- VÉRIFICATIONS ET TESTS
-- =====================================================

-- Vérifier l'intégrité des données
SELECT 'Vérification des totaux de vente' AS verification;
SELECT 
    v.numero_facture,
    v.sous_total AS sous_total_calcule,
    (SELECT SUM(sous_total) FROM ventes_details WHERE vente_id = v.id) AS sous_total_details,
    v.montant_total,
    CASE 
        WHEN ABS(v.sous_total - (SELECT COALESCE(SUM(sous_total), 0) FROM ventes_details WHERE vente_id = v.id)) < 0.01 
        THEN 'OK' 
        ELSE 'ERREUR' 
    END AS statut_verification
FROM ventes v;

-- Vérifier les stocks
SELECT 'Vérification des stocks' AS verification;
SELECT 
    p.nom,
    p.code_produit,
    s.quantite_actuelle,
    s.quantite_reservee,
    s.quantite_disponible,
    CASE 
        WHEN s.quantite_disponible < 0 THEN 'ATTENTION: Stock négatif'
        WHEN s.quantite_actuelle <= p.stock_minimum THEN 'ALERTE: Stock faible'
        ELSE 'OK'
    END AS statut_stock
FROM produits p
LEFT JOIN stock s ON p.id = s.produit_id
WHERE p.est_actif = TRUE
ORDER BY s.quantite_disponible;

-- Statistiques générales
SELECT 'Statistiques générales' AS information;
SELECT 
    (SELECT COUNT(*) FROM utilisateurs WHERE est_actif = TRUE) AS utilisateurs_actifs,
    (SELECT COUNT(*) FROM clients WHERE est_actif = TRUE) AS clients_actifs,
    (SELECT COUNT(*) FROM fournisseurs WHERE est_actif = TRUE) AS fournisseurs_actifs,
    (SELECT COUNT(*) FROM produits WHERE est_actif = TRUE) AS produits_actifs,
    (SELECT COUNT(*) FROM categories WHERE est_active = TRUE) AS categories_actives,
    (SELECT COUNT(*) FROM ventes) AS total_ventes,
    (SELECT COUNT(*) FROM achats) AS total_achats,
    (SELECT COALESCE(SUM(montant), 0) FROM revenus) AS total_revenus,
    (SELECT COALESCE(SUM(montant), 0) FROM depenses) AS total_depenses;

-- =====================================================
-- MESSAGES DE FINALISATION
-- =====================================================

SELECT 'Base de données ZinStore configurée avec succès!' AS message;
SELECT 'Données d\'exemple insérées' AS message;
SELECT 'Prêt pour utilisation' AS message;

-- =====================================================
-- INFORMATIONS DE CONNEXION PAR DÉFAUT
-- =====================================================

SELECT 'INFORMATIONS DE CONNEXION PAR DÉFAUT:' AS info;
SELECT 'Utilisateur: admin' AS info;
SELECT 'Mot de passe: password' AS info;
SELECT 'Role: Admin' AS info;
SELECT '' AS info;
SELECT 'AUTRES COMPTES DE TEST:' AS info;
SELECT 'manager / password (Manager)' AS info;
SELECT 'vendeur1 / password (Vendeur)' AS info;
SELECT 'vendeur2 / password (Vendeur)' AS info;
SELECT 'caissier1 / password (Caissier)' AS info;

-- =====================================================
-- RECOMMANDATIONS POST-INSTALLATION
-- =====================================================

/*
RECOMMANDATIONS POST-INSTALLATION:

1. SÉCURITÉ:
   - Changer les mots de passe par défaut
   - Configurer les privilèges utilisateurs
   - Activer les logs d'audit si nécessaire
   - Configurer le SSL pour les connexions

2. SAUVEGARDE:
   - Configurer des sauvegardes automatiques
   - Tester la restauration
   - Documenter la procédure de sauvegarde

3. MONITORING:
   - Surveiller les performances des requêtes
   - Monitorer l'espace disque
   - Configurer des alertes système

4. MAINTENANCE:
   - Planifier l'optimisation des tables
   - Surveiller la croissance des logs
   - Archiver les données anciennes

5. DÉVELOPPEMENT:
   - Configurer un environnement de test
   - Documenter le schéma de base
   - Mettre en place la gestion des versions
*/
