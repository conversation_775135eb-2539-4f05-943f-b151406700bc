using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class ParametresRepository : BaseRepository<Parametres>
    {
        public ParametresRepository(DatabaseContext context) : base(context, "Parametres")
        {
        }

        public override async Task<IEnumerable<Parametres>> SearchAsync(string searchTerm)
        {
            return await GetAllAsync();
        }

        public async Task<Parametres> GetParametresAsync()
        {
            var sql = "SELECT * FROM Parametres WHERE EstSupprime = 0 LIMIT 1";
            using (var connection = _context.GetConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<Parametres>(sql);
            }
        }
    }
}
