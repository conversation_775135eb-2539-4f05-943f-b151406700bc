<UserControl x:Class="ZinStore.UI.Views.POS.POSView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.DataContext>
        <viewModels:POSViewModel/>
    </UserControl.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="2*"/>
            <ColumnDefinition Width="10"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- <PERSON>ie gauche - Produits et recherche -->
        <Grid Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- En-tête -->
            <materialDesign:Card Grid.Row="0" Padding="20" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CashRegister" 
                                               Width="32" Height="32"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="🛒 Point de Vente"
                                  Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                  FontWeight="Bold"
                                  VerticalAlignment="Center"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                                  Margin="10,0,0,0"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <TextBlock Text="Caisse: " VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding CurrentCashRegister}" 
                                  FontWeight="Bold"
                                  VerticalAlignment="Center"
                                  Margin="5,0,20,0"/>
                        <TextBlock Text="Vendeur: " VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding CurrentUser}" 
                                  FontWeight="Bold"
                                  VerticalAlignment="Center"
                                  Margin="5,0,0,0"/>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Barre de recherche -->
            <materialDesign:Card Grid.Row="1" Padding="15" Margin="10,0,10,10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBox Grid.Column="0"
                            x:Name="SearchTextBox"
                            materialDesign:HintAssist.Hint="Rechercher un produit (nom, code, code-barres)..."
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            FontSize="16"
                            KeyDown="SearchTextBox_KeyDown"/>

                    <Button Grid.Column="1"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Command="{Binding SearchCommand}"
                           Margin="10,0,0,0">
                        <materialDesign:PackIcon Kind="Magnify" Width="24" Height="24"/>
                    </Button>

                    <Button Grid.Column="2"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Command="{Binding ScanBarcodeCommand}"
                           ToolTip="Scanner code-barres"
                           Margin="5,0,0,0">
                        <materialDesign:PackIcon Kind="Barcode" Width="24" Height="24"/>
                    </Button>
                </Grid>
            </materialDesign:Card>

            <!-- Grille des produits -->
            <materialDesign:Card Grid.Row="2" Margin="10,0,10,10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Catégories -->
                    <ScrollViewer Grid.Row="0" 
                                 HorizontalScrollBarVisibility="Auto" 
                                 VerticalScrollBarVisibility="Disabled"
                                 Padding="10">
                        <StackPanel Orientation="Horizontal">
                            <Button Content="Tous"
                                   Style="{StaticResource MaterialDesignRaisedButton}"
                                   Command="{Binding SelectCategoryCommand}"
                                   CommandParameter=""
                                   Margin="0,0,10,0"/>
                            
                            <ItemsControl ItemsSource="{Binding Categories}">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Orientation="Horizontal"/>
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Button Content="{Binding Nom}"
                                               Style="{StaticResource MaterialDesignOutlinedButton}"
                                               Command="{Binding DataContext.SelectCategoryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                               CommandParameter="{Binding Id}"
                                               Margin="0,0,10,0"/>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Produits -->
                    <ScrollViewer Grid.Row="1" Padding="10">
                        <ItemsControl ItemsSource="{Binding Products}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <UniformGrid Columns="4"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Margin="5" 
                                                        Cursor="Hand"
                                                        MouseLeftButtonDown="ProductCard_Click">
                                        <materialDesign:Card.Tag>
                                            <Binding Path="."/>
                                        </materialDesign:Card.Tag>
                                        <Grid Margin="10">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="60"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- Image ou icône -->
                                            <materialDesign:PackIcon Grid.Row="0"
                                                                   Kind="PackageVariantClosed"
                                                                   Width="40" Height="40"
                                                                   HorizontalAlignment="Center"
                                                                   VerticalAlignment="Center"
                                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"/>

                                            <!-- Nom du produit -->
                                            <TextBlock Grid.Row="1"
                                                      Text="{Binding Nom}"
                                                      FontWeight="Bold"
                                                      TextAlignment="Center"
                                                      TextTrimming="CharacterEllipsis"
                                                      Margin="0,5,0,0"/>

                                            <!-- Prix -->
                                            <TextBlock Grid.Row="2"
                                                      Text="{Binding PrixVente, StringFormat='{}{0:F2} DA'}"
                                                      FontSize="14"
                                                      FontWeight="Bold"
                                                      TextAlignment="Center"
                                                      Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                      Margin="0,2,0,0"/>

                                            <!-- Stock -->
                                            <TextBlock Grid.Row="3"
                                                      Text="{Binding StockActuel, StringFormat='Stock: {0}'}"
                                                      FontSize="12"
                                                      TextAlignment="Center"
                                                      Foreground="Gray"
                                                      Margin="0,2,0,0"/>
                                        </Grid>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>
        </Grid>

        <!-- Partie droite - Panier et paiement -->
        <Grid Grid.Column="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Client -->
            <materialDesign:Card Grid.Row="0" Padding="15" Margin="0,10,10,10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" 
                              Text="Client" 
                              FontWeight="Bold" 
                              FontSize="14"
                              Margin="0,0,0,10"/>

                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <ComboBox Grid.Column="0"
                                 materialDesign:HintAssist.Hint="Sélectionner un client"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 ItemsSource="{Binding Clients}"
                                 SelectedItem="{Binding SelectedClient}"
                                 DisplayMemberPath="NomComplet"/>

                        <Button Grid.Column="1"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Command="{Binding AddClientCommand}"
                               ToolTip="Nouveau client"
                               Margin="5,0,0,0">
                            <materialDesign:PackIcon Kind="AccountPlus"/>
                        </Button>
                    </Grid>
                </Grid>
            </materialDesign:Card>

            <!-- Panier -->
            <materialDesign:Card Grid.Row="1" Padding="15" Margin="0,0,10,10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" 
                                  Text="Panier" 
                                  FontWeight="Bold" 
                                  FontSize="14"
                                  VerticalAlignment="Center"/>

                        <Button Grid.Column="1"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Command="{Binding ClearCartCommand}"
                               ToolTip="Vider le panier">
                            <materialDesign:PackIcon Kind="DeleteSweep" Foreground="Red"/>
                        </Button>
                    </Grid>

                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding CartItems}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Margin="0,0,0,5" Padding="10">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- Nom et bouton supprimer -->
                                            <Grid Grid.Row="0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0"
                                                          Text="{Binding ProductName}"
                                                          FontWeight="Bold"
                                                          TextTrimming="CharacterEllipsis"/>

                                                <Button Grid.Column="1"
                                                       Style="{StaticResource MaterialDesignIconButton}"
                                                       Command="{Binding DataContext.RemoveFromCartCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Width="20" Height="20">
                                                    <materialDesign:PackIcon Kind="Close" Width="12" Height="12" Foreground="Red"/>
                                                </Button>
                                            </Grid>

                                            <!-- Quantité et prix -->
                                            <Grid Grid.Row="1" Margin="0,5,0,0">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <Button Grid.Column="0"
                                                       Style="{StaticResource MaterialDesignIconButton}"
                                                       Command="{Binding DataContext.DecreaseQuantityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Width="25" Height="25">
                                                    <materialDesign:PackIcon Kind="Minus" Width="12" Height="12"/>
                                                </Button>

                                                <TextBlock Grid.Column="1"
                                                          Text="{Binding Quantity}"
                                                          FontWeight="Bold"
                                                          TextAlignment="Center"
                                                          VerticalAlignment="Center"/>

                                                <Button Grid.Column="2"
                                                       Style="{StaticResource MaterialDesignIconButton}"
                                                       Command="{Binding DataContext.IncreaseQuantityCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       Width="25" Height="25">
                                                    <materialDesign:PackIcon Kind="Plus" Width="12" Height="12"/>
                                                </Button>
                                            </Grid>

                                            <!-- Total -->
                                            <TextBlock Grid.Row="2"
                                                      Text="{Binding Total, StringFormat='{}{0:F2} DA'}"
                                                      FontWeight="Bold"
                                                      TextAlignment="Right"
                                                      Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                      Margin="0,5,0,0"/>
                                        </Grid>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </materialDesign:Card>

            <!-- Totaux -->
            <materialDesign:Card Grid.Row="2" Padding="15" Margin="0,0,10,10">
                <StackPanel>
                    <Grid Margin="0,0,0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Sous-total:" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" Text="{Binding SubTotal, StringFormat='{}{0:F2} DA'}" FontWeight="Bold"/>
                    </Grid>

                    <Grid Margin="0,0,0,5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="TVA (19%):" />
                        <TextBlock Grid.Column="1" Text="{Binding TaxAmount, StringFormat='{}{0:F2} DA'}"/>
                    </Grid>

                    <Separator Margin="0,5"/>

                    <Grid Margin="0,5,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Total:" FontWeight="Bold" FontSize="16"/>
                        <TextBlock Grid.Column="1" 
                                  Text="{Binding Total, StringFormat='{}{0:F2} DA'}" 
                                  FontWeight="Bold" 
                                  FontSize="16"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Boutons de paiement -->
            <materialDesign:Card Grid.Row="3" Padding="15" Margin="0,0,10,10">
                <StackPanel>
                    <Button Content="PAYER EN ESPÈCES"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding PayCashCommand}"
                           Background="Green"
                           FontWeight="Bold"
                           Height="50"
                           Margin="0,0,0,10"/>

                    <Button Content="PAYER PAR CARTE"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding PayCardCommand}"
                           Height="40"
                           Margin="0,0,0,10"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0"
                               Content="SUSPENDRE"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding SuspendSaleCommand}"
                               Margin="0,0,5,0"/>

                        <Button Grid.Column="1"
                               Content="ANNULER"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding CancelSaleCommand}"
                               Margin="5,0,0,0"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </Grid>
    </Grid>
</UserControl>
