-- =====================================================
-- Script de création des tables ZinStore
-- Version: MySQL 8.0+
-- =====================================================

USE zinstore;

-- =====================================================
-- 1. TABLE DES UTILISATEURS
-- =====================================================

CREATE TABLE utilisateurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_utilisateur VARCHAR(50) NOT NULL UNIQUE,
    mot_de_passe VARCHAR(255) NOT NULL,
    nom_complet VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    telephone VARCHAR(20),
    role ENUM('Admin', 'Manager', 'Vendeur', 'Caissier') NOT NULL DEFAULT 'Vendeur',
    est_actif BOOLEAN NOT NULL DEFAULT TRUE,
    derniere_connexion DATETIME,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_nom_utilisateur (nom_utilisateur),
    INDEX idx_role (role),
    INDEX idx_est_actif (est_actif)
) ENGINE=InnoDB COMMENT='Table des utilisateurs du système';

-- =====================================================
-- 2. TABLE DES CLIENTS
-- =====================================================

CREATE TABLE clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100),
    nom_complet VARCHAR(200) GENERATED ALWAYS AS (CONCAT(IFNULL(prenom, ''), ' ', nom)) STORED,
    telephone VARCHAR(20),
    email VARCHAR(100),
    adresse TEXT,
    ville VARCHAR(50),
    code_postal VARCHAR(10),
    date_naissance DATE,
    type_client ENUM('Particulier', 'Entreprise') NOT NULL DEFAULT 'Particulier',
    numero_client VARCHAR(20) UNIQUE,
    credit_limite DECIMAL(15,2) DEFAULT 0.00,
    solde_compte DECIMAL(15,2) DEFAULT 0.00,
    est_actif BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_nom (nom),
    INDEX idx_telephone (telephone),
    INDEX idx_numero_client (numero_client),
    INDEX idx_type_client (type_client),
    INDEX idx_est_actif (est_actif)
) ENGINE=InnoDB COMMENT='Table des clients';

-- =====================================================
-- 3. TABLE DES FOURNISSEURS
-- =====================================================

CREATE TABLE fournisseurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    contact_nom VARCHAR(100),
    telephone VARCHAR(20),
    email VARCHAR(100),
    adresse TEXT,
    ville VARCHAR(50),
    code_postal VARCHAR(10),
    numero_fournisseur VARCHAR(20) UNIQUE,
    conditions_paiement VARCHAR(100),
    delai_livraison_jours INT DEFAULT 7,
    est_actif BOOLEAN NOT NULL DEFAULT TRUE,
    notes TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_nom (nom),
    INDEX idx_telephone (telephone),
    INDEX idx_numero_fournisseur (numero_fournisseur),
    INDEX idx_est_actif (est_actif)
) ENGINE=InnoDB COMMENT='Table des fournisseurs';

-- =====================================================
-- 4. TABLE DES CATÉGORIES
-- =====================================================

CREATE TABLE categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    description TEXT,
    code_categorie VARCHAR(20) UNIQUE,
    categorie_parent_id INT,
    est_active BOOLEAN NOT NULL DEFAULT TRUE,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (categorie_parent_id) REFERENCES categories(id) ON DELETE SET NULL,
    INDEX idx_nom (nom),
    INDEX idx_code_categorie (code_categorie),
    INDEX idx_categorie_parent (categorie_parent_id),
    INDEX idx_est_active (est_active)
) ENGINE=InnoDB COMMENT='Table des catégories de produits';

-- =====================================================
-- 5. TABLE DES PRODUITS
-- =====================================================

CREATE TABLE produits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(200) NOT NULL,
    description TEXT,
    code_produit VARCHAR(50) UNIQUE NOT NULL,
    code_barre VARCHAR(50),
    categorie_id INT,
    prix_achat DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    prix_vente DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    marge_benefice DECIMAL(5,2) GENERATED ALWAYS AS (
        CASE 
            WHEN prix_achat > 0 THEN ((prix_vente - prix_achat) / prix_achat * 100)
            ELSE 0 
        END
    ) STORED,
    unite_mesure ENUM('Pièce', 'Carton', 'Kg', 'Litre', 'Mètre') NOT NULL DEFAULT 'Pièce',
    quantite_par_unite INT DEFAULT 1,
    quantite_carton INT DEFAULT 1,
    stock_minimum INT DEFAULT 0,
    stock_maximum INT DEFAULT 1000,
    date_expiration DATE,
    a_expiration BOOLEAN GENERATED ALWAYS AS (date_expiration IS NOT NULL) STORED,
    fournisseur_id INT,
    est_actif BOOLEAN NOT NULL DEFAULT TRUE,
    image_url VARCHAR(500),
    notes TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (categorie_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id) ON DELETE SET NULL,
    INDEX idx_nom (nom),
    INDEX idx_code_produit (code_produit),
    INDEX idx_code_barre (code_barre),
    INDEX idx_categorie (categorie_id),
    INDEX idx_fournisseur (fournisseur_id),
    INDEX idx_prix_vente (prix_vente),
    INDEX idx_est_actif (est_actif),
    INDEX idx_date_expiration (date_expiration)
) ENGINE=InnoDB COMMENT='Table des produits';

-- =====================================================
-- 6. TABLE DU STOCK
-- =====================================================

CREATE TABLE stock (
    id INT AUTO_INCREMENT PRIMARY KEY,
    produit_id INT NOT NULL,
    quantite_actuelle INT NOT NULL DEFAULT 0,
    quantite_reservee INT NOT NULL DEFAULT 0,
    quantite_disponible INT GENERATED ALWAYS AS (quantite_actuelle - quantite_reservee) STORED,
    valeur_stock DECIMAL(15,2) GENERATED ALWAYS AS (quantite_actuelle * (SELECT prix_achat FROM produits WHERE id = produit_id)) STORED,
    emplacement VARCHAR(100),
    date_derniere_entree DATETIME,
    date_derniere_sortie DATETIME,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE CASCADE,
    UNIQUE KEY unique_produit_stock (produit_id),
    INDEX idx_quantite_actuelle (quantite_actuelle),
    INDEX idx_quantite_disponible (quantite_disponible),
    INDEX idx_emplacement (emplacement)
) ENGINE=InnoDB COMMENT='Table de gestion du stock';

-- =====================================================
-- 7. TABLE DES VENTES
-- =====================================================

CREATE TABLE ventes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_facture VARCHAR(50) UNIQUE NOT NULL,
    client_id INT,
    utilisateur_id INT NOT NULL,
    date_vente DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    sous_total DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    taux_tva DECIMAL(5,2) DEFAULT 19.00,
    montant_tva DECIMAL(15,2) GENERATED ALWAYS AS (sous_total * taux_tva / 100) STORED,
    remise DECIMAL(15,2) DEFAULT 0.00,
    montant_total DECIMAL(15,2) GENERATED ALWAYS AS (sous_total + (sous_total * taux_tva / 100) - remise) STORED,
    montant_paye DECIMAL(15,2) DEFAULT 0.00,
    montant_restant DECIMAL(15,2) GENERATED ALWAYS AS (sous_total + (sous_total * taux_tva / 100) - remise - montant_paye) STORED,
    statut_paiement ENUM('En attente', 'Partiel', 'Payé', 'Annulé') NOT NULL DEFAULT 'En attente',
    mode_paiement ENUM('Espèces', 'Carte', 'Chèque', 'Virement', 'Crédit') DEFAULT 'Espèces',
    notes TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE RESTRICT,
    INDEX idx_numero_facture (numero_facture),
    INDEX idx_client (client_id),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_date_vente (date_vente),
    INDEX idx_statut_paiement (statut_paiement),
    INDEX idx_montant_total (montant_total)
) ENGINE=InnoDB COMMENT='Table des ventes (en-têtes)';

-- =====================================================
-- 8. TABLE DES DÉTAILS DE VENTE
-- =====================================================

CREATE TABLE ventes_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    vente_id INT NOT NULL,
    produit_id INT NOT NULL,
    quantite INT NOT NULL,
    prix_unitaire DECIMAL(15,2) NOT NULL,
    sous_total DECIMAL(15,2) GENERATED ALWAYS AS (quantite * prix_unitaire) STORED,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (vente_id) REFERENCES ventes(id) ON DELETE CASCADE,
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE RESTRICT,
    INDEX idx_vente (vente_id),
    INDEX idx_produit (produit_id),
    INDEX idx_quantite (quantite),
    INDEX idx_prix_unitaire (prix_unitaire)
) ENGINE=InnoDB COMMENT='Table des détails de vente (lignes)';
