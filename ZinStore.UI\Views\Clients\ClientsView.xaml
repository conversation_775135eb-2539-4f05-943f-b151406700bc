<UserControl x:Class="ZinStore.UI.Views.ClientsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">
    
    <UserControl.DataContext>
        <viewModels:ClientsViewModel />
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0"
                  Text="👥 Gestion des Clients"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Margin="0,0,0,20"
                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                  FontWeight="Bold"/>

        <!-- Barre d'outils -->
        <materialDesign:Card Grid.Row="1" 
                           Margin="0,0,0,20" 
                           Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche -->
                <TextBox Grid.Column="0"
                        materialDesign:HintAssist.Hint="Rechercher un client (nom, téléphone, email)..."
                        materialDesign:HintAssist.IsFloating="True"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        Width="400"
                        Height="56"
                        FontSize="14"
                        VerticalContentAlignment="Center"
                        HorizontalAlignment="Left">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Boutons -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal"
                           VerticalAlignment="Center">
                    <Button Content="➕ Nouveau Client"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddClientCommand}"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Foreground="White"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="✏️ Modifier"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding EditClientCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="🗑️ Supprimer"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding DeleteClientCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="🔄 Actualiser"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding RefreshCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des clients -->
        <materialDesign:Card Grid.Row="2" 
                           Padding="15">
            <DataGrid ItemsSource="{Binding Clients}"
                     SelectedItem="{Binding SelectedClient}"
                     Style="{StaticResource CustomDataGrid}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Code" 
                                      Binding="{Binding CodeClient}" 
                                      Width="100"/>
                    <DataGridTextColumn Header="Nom" 
                                      Binding="{Binding Nom}" 
                                      Width="150"/>
                    <DataGridTextColumn Header="Prénom" 
                                      Binding="{Binding Prenom}" 
                                      Width="150"/>
                    <DataGridTextColumn Header="Téléphone" 
                                      Binding="{Binding Telephone}" 
                                      Width="120"/>
                    <DataGridTextColumn Header="Email" 
                                      Binding="{Binding Email}" 
                                      Width="200"/>
                    <DataGridTextColumn Header="Ville" 
                                      Binding="{Binding Ville}" 
                                      Width="120"/>
                    <DataGridTextColumn Header="Solde" 
                                      Binding="{Binding SoldeCompte, StringFormat='{}{0:C}'}" 
                                      Width="100"/>
                    <DataGridCheckBoxColumn Header="Actif" 
                                          Binding="{Binding EstActif}" 
                                          Width="60"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
