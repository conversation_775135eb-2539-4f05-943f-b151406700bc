<UserControl x:Class="ZinStore.UI.Views.Branches.BranchesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.DataContext>
        <viewModels:BranchesViewModel/>
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="StorefrontOutline" 
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="Gestion des Filiales"
                              FontSize="20"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddBranchCommand}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Plus" VerticalAlignment="Center"/>
                            <TextBlock Text="Nouvelle Filiale" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding SyncBranchesCommand}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Sync" VerticalAlignment="Center"/>
                            <TextBlock Text="Synchroniser" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding RefreshCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Refresh" VerticalAlignment="Center"/>
                            <TextBlock Text="Actualiser" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Statistiques globales -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="10"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Filiales -->
            <materialDesign:Card Grid.Column="0" Padding="20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Total Filiales" 
                                      FontSize="14" 
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="{Binding TotalBranches}" 
                                      FontSize="24" 
                                      FontWeight="Bold"
                                      Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                        </StackPanel>
                        
                        <materialDesign:PackIcon Grid.Column="1"
                                               Kind="StorefrontOutline"
                                               Width="40" Height="40"
                                               Foreground="{DynamicResource PrimaryHueMidBrush}"
                                               VerticalAlignment="Center"/>
                    </Grid>
                    
                    <TextBlock Text="{Binding ActiveBranches, StringFormat='{}{0} actives'}" 
                              FontSize="12"
                              Foreground="Green"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Chiffre d'affaires total -->
            <materialDesign:Card Grid.Column="2" Padding="20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="CA Total" 
                                      FontSize="14" 
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="{Binding TotalRevenue, StringFormat='{}{0:F0} DA'}" 
                                      FontSize="24" 
                                      FontWeight="Bold"
                                      Foreground="Green"/>
                        </StackPanel>
                        
                        <materialDesign:PackIcon Grid.Column="1"
                                               Kind="CurrencyUsd"
                                               Width="40" Height="40"
                                               Foreground="Green"
                                               VerticalAlignment="Center"/>
                    </Grid>
                    
                    <TextBlock Text="Ce mois" 
                              FontSize="12"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Employés total -->
            <materialDesign:Card Grid.Column="4" Padding="20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Total Employés" 
                                      FontSize="14" 
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="{Binding TotalEmployees}" 
                                      FontSize="24" 
                                      FontWeight="Bold"
                                      Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                        </StackPanel>
                        
                        <materialDesign:PackIcon Grid.Column="1"
                                               Kind="AccountGroup"
                                               Width="40" Height="40"
                                               Foreground="{DynamicResource SecondaryHueMidBrush}"
                                               VerticalAlignment="Center"/>
                    </Grid>
                    
                    <TextBlock Text="Toutes filiales" 
                              FontSize="12"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Performance moyenne -->
            <materialDesign:Card Grid.Column="6" Padding="20">
                <StackPanel>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="Performance Moy." 
                                      FontSize="14" 
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="{Binding AveragePerformance, StringFormat='{}{0:F1}%'}" 
                                      FontSize="24" 
                                      FontWeight="Bold"
                                      Foreground="Orange"/>
                        </StackPanel>
                        
                        <materialDesign:PackIcon Grid.Column="1"
                                               Kind="ChartLine"
                                               Width="40" Height="40"
                                               Foreground="Orange"
                                               VerticalAlignment="Center"/>
                    </Grid>
                    
                    <TextBlock Text="vs objectifs" 
                              FontSize="12"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              Margin="0,5,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Liste des filiales -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- En-tête avec filtres -->
                <Border Grid.Row="0" 
                       Background="{DynamicResource PrimaryHueLightBrush}" 
                       Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0"
                                Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                materialDesign:HintAssist.Hint="Rechercher une filiale..."
                                Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                Margin="0,0,20,0"/>

                        <ComboBox Grid.Column="1"
                                 SelectedValue="{Binding RegionFilter}"
                                 materialDesign:HintAssist.Hint="Région"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 Margin="0,0,10,0">
                            <ComboBoxItem Content="Toutes régions" Tag="All"/>
                            <ComboBoxItem Content="Nord" Tag="North"/>
                            <ComboBoxItem Content="Sud" Tag="South"/>
                            <ComboBoxItem Content="Est" Tag="East"/>
                            <ComboBoxItem Content="Ouest" Tag="West"/>
                            <ComboBoxItem Content="Centre" Tag="Center"/>
                        </ComboBox>

                        <ComboBox Grid.Column="2"
                                 SelectedValue="{Binding StatusFilter}"
                                 materialDesign:HintAssist.Hint="Statut"
                                 Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                 Margin="0,0,10,0">
                            <ComboBoxItem Content="Tous" Tag="All"/>
                            <ComboBoxItem Content="Actives" Tag="Active"/>
                            <ComboBoxItem Content="Inactives" Tag="Inactive"/>
                            <ComboBoxItem Content="En construction" Tag="Construction"/>
                        </ComboBox>

                        <Button Grid.Column="3"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Command="{Binding SearchCommand}"
                               ToolTip="Rechercher">
                            <materialDesign:PackIcon Kind="Magnify" Foreground="White"/>
                        </Button>
                    </Grid>
                </Border>

                <!-- Grille des filiales -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <ItemsControl ItemsSource="{Binding Branches}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <UniformGrid Columns="3" Margin="20"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <materialDesign:Card Margin="10" 
                                                    Cursor="Hand"
                                                    MouseLeftButtonDown="BranchCard_Click">
                                    <materialDesign:Card.Tag>
                                        <Binding Path="."/>
                                    </materialDesign:Card.Tag>
                                    
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="150"/>
                                            <RowDefinition Height="*"/>
                                        </Grid.RowDefinitions>

                                        <!-- Image de la filiale -->
                                        <Border Grid.Row="0" 
                                               Background="{DynamicResource PrimaryHueLightBrush}"
                                               CornerRadius="4,4,0,0">
                                            <Grid>
                                                <Image Source="{Binding ImagePath}" 
                                                      Stretch="UniformToFill"
                                                      Visibility="{Binding ImagePath, Converter={StaticResource NullToVisibilityConverter}}"/>
                                                
                                                <materialDesign:PackIcon Kind="StorefrontOutline"
                                                                       Width="60" Height="60"
                                                                       Foreground="White"
                                                                       HorizontalAlignment="Center"
                                                                       VerticalAlignment="Center"
                                                                       Visibility="{Binding ImagePath, Converter={StaticResource InverseNullToVisibilityConverter}}"/>
                                                
                                                <!-- Badge de statut -->
                                                <materialDesign:Chip Content="{Binding Status}"
                                                                   Background="{Binding Status, Converter={StaticResource StatusToColorConverter}}"
                                                                   Foreground="White"
                                                                   FontSize="10"
                                                                   Height="20"
                                                                   HorizontalAlignment="Right"
                                                                   VerticalAlignment="Top"
                                                                   Margin="10"/>
                                            </Grid>
                                        </Border>

                                        <!-- Informations de la filiale -->
                                        <StackPanel Grid.Row="1" Margin="15">
                                            <!-- Nom et code -->
                                            <TextBlock Text="{Binding Name}" 
                                                      FontWeight="Bold"
                                                      FontSize="16"
                                                      TextTrimming="CharacterEllipsis"
                                                      Margin="0,0,0,5"/>
                                            
                                            <TextBlock Text="{Binding Code}" 
                                                      FontSize="12"
                                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                      Margin="0,0,0,10"/>

                                            <!-- Adresse -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                                <materialDesign:PackIcon Kind="MapMarker" 
                                                                       Width="16" Height="16"
                                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="{Binding Address}" 
                                                          FontSize="12"
                                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                          TextTrimming="CharacterEllipsis"
                                                          Margin="5,0,0,0"/>
                                            </StackPanel>

                                            <!-- Manager -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                                <materialDesign:PackIcon Kind="Account" 
                                                                       Width="16" Height="16"
                                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="{Binding ManagerName}" 
                                                          FontSize="12"
                                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                          Margin="5,0,0,0"/>
                                            </StackPanel>

                                            <!-- Téléphone -->
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                                <materialDesign:PackIcon Kind="Phone" 
                                                                       Width="16" Height="16"
                                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                                       VerticalAlignment="Center"/>
                                                <TextBlock Text="{Binding Phone}" 
                                                          FontSize="12"
                                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                          Margin="5,0,0,0"/>
                                            </StackPanel>

                                            <!-- Statistiques -->
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <StackPanel Grid.Column="0">
                                                    <TextBlock Text="CA Mensuel" 
                                                              FontSize="10"
                                                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                    <TextBlock Text="{Binding MonthlyRevenue, StringFormat='{}{0:F0} DA'}" 
                                                              FontWeight="Bold"
                                                              FontSize="12"
                                                              Foreground="Green"/>
                                                </StackPanel>
                                                
                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="Employés" 
                                                              FontSize="10"
                                                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                    <TextBlock Text="{Binding EmployeeCount}" 
                                                              FontWeight="Bold"
                                                              FontSize="12"
                                                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                                </StackPanel>
                                            </Grid>

                                            <!-- Barre de performance -->
                                            <StackPanel Margin="0,10,0,0">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    
                                                    <TextBlock Grid.Column="0" 
                                                              Text="Performance" 
                                                              FontSize="10"
                                                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                                    
                                                    <TextBlock Grid.Column="1" 
                                                              Text="{Binding Performance, StringFormat='{}{0:F0}%'}" 
                                                              FontSize="10"
                                                              FontWeight="Bold"
                                                              Foreground="{Binding Performance, Converter={StaticResource PerformanceToColorConverter}}"/>
                                                </Grid>
                                                
                                                <ProgressBar Value="{Binding Performance}" 
                                                            Maximum="100"
                                                            Height="6"
                                                            Background="{DynamicResource MaterialDesignDivider}"
                                                            Foreground="{Binding Performance, Converter={StaticResource PerformanceToColorConverter}}"
                                                            Margin="0,2,0,0"/>
                                            </StackPanel>

                                            <!-- Actions -->
                                            <StackPanel Orientation="Horizontal" 
                                                       HorizontalAlignment="Right"
                                                       Margin="0,10,0,0">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                       Command="{Binding DataContext.ViewBranchDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       ToolTip="Voir détails">
                                                    <materialDesign:PackIcon Kind="Eye" Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                                </Button>
                                                
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                       Command="{Binding DataContext.EditBranchCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                       CommandParameter="{Binding}"
                                                       ToolTip="Modifier">
                                                    <materialDesign:PackIcon Kind="Edit" Foreground="Orange"/>
                                                </Button>
                                            </StackPanel>
                                        </StackPanel>
                                    </Grid>
                                </materialDesign:Card>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                <!-- Message vide -->
                <StackPanel Grid.Row="1"
                           HorizontalAlignment="Center" 
                           VerticalAlignment="Center"
                           Visibility="{Binding IsEmpty, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <materialDesign:PackIcon Kind="StorefrontOutline" 
                                           Width="64" Height="64"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="Aucune filiale trouvée"
                              FontSize="16"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Overlay de chargement -->
        <Grid Background="#80000000"
              Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="50"
                           Height="50"
                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="{Binding BusyMessage}"
                          FontSize="14"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
