using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class CompteGeneralRepository : BaseRepository<CompteGeneral>
    {
        public CompteGeneralRepository(DatabaseContext context) : base(context, "ComptesGeneraux")
        {
        }

        public override async Task<IEnumerable<CompteGeneral>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT * FROM ComptesGeneraux 
                WHERE EstSupprime = 0 
                AND (NumeroCompte LIKE @SearchTerm OR NomCompte LIKE @SearchTerm)
                ORDER BY NumeroCompte";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<CompteGeneral>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }
    }
}
