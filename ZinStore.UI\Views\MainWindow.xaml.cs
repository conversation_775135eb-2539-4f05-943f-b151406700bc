using System.Windows;
using System.Windows.Controls;

namespace ZinStore.UI.Views
{
    /// <summary>
    /// Logique d'interaction pour MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // Afficher le menu contextuel
            var button = sender as <PERSON><PERSON>;
            if (button?.ContextMenu != null)
            {
                button.ContextMenu.PlacementTarget = button;
                button.ContextMenu.IsOpen = true;
            }
        }

        private void DatabaseConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var configWindow = new DatabaseConnectionWindow();
                configWindow.Owner = this;

                var result = configWindow.ShowDialog();

                if (result == true && configWindow.ConnectionSaved)
                {
                    // Optionnel: Redémarrer l'application ou recharger la configuration
                    MessageBox.Show("Configuration sauvegardée avec succès!\n" +
                                  "Les changements seront appliqués au prochain démarrage de l'application.",
                                  "Configuration Base de Données",
                                  MessageBoxButton.OK,
                                  MessageBoxImage.Information);
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ouverture de la configuration: {ex.Message}",
                              "Erreur",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        private void GeneralSettings_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Paramètres généraux - À implémenter",
                          "Information",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = "ZinStore - Système de Gestion de Supermarché\n\n" +
                             "Version: 1.0\n" +
                             "Développé avec: WPF, .NET 6, SQLite, Material Design\n" +
                             "Base de données: SQLite avec Dapper ORM\n\n" +
                             "© 2024 ZinStore. Tous droits réservés.";

            MessageBox.Show(aboutMessage,
                          "À propos de ZinStore",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }
    }
}
