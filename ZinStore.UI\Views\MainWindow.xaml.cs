using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;

namespace ZinStore.UI.Views
{
    /// <summary>
    /// Logique d'interaction pour MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private bool _isSidebarVisible = true;

        public MainWindow()
        {
            InitializeComponent();
        }

        private void HamburgerButton_Click(object sender, RoutedEventArgs e)
        {
            ToggleSidebar();
        }

        private void ToggleSidebar()
        {
            var sidebar = NavigationSidebar;
            var duration = TimeSpan.FromMilliseconds(300);

            if (_isSidebarVisible)
            {
                // Masquer la sidebar
                var hideAnimation = new DoubleAnimation
                {
                    From = 250,
                    To = 0,
                    Duration = duration,
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
                };

                hideAnimation.Completed += (s, e) =>
                {
                    sidebar.Visibility = Visibility.Collapsed;
                };

                sidebar.BeginAnimation(FrameworkElement.WidthProperty, hideAnimation);
                _isSidebarVisible = false;
            }
            else
            {
                // Afficher la sidebar
                sidebar.Visibility = Visibility.Visible;
                var showAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 250,
                    Duration = duration,
                    EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
                };

                sidebar.BeginAnimation(FrameworkElement.WidthProperty, showAnimation);
                _isSidebarVisible = true;
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // Afficher le menu contextuel
            var button = sender as Button;
            if (button?.ContextMenu != null)
            {
                button.ContextMenu.PlacementTarget = button;
                button.ContextMenu.IsOpen = true;
            }
        }

        private void DatabaseConfig_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var configWindow = new DatabaseConnectionWindow();
                configWindow.Owner = this;

                var result = configWindow.ShowDialog();

                if (result == true && configWindow.ConnectionSaved)
                {
                    // Optionnel: Redémarrer l'application ou recharger la configuration
                    MessageBox.Show("Configuration sauvegardée avec succès!\n" +
                                  "Les changements seront appliqués au prochain démarrage de l'application.",
                                  "Configuration Base de Données",
                                  MessageBoxButton.OK,
                                  MessageBoxImage.Information);
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ouverture de la configuration: {ex.Message}",
                              "Erreur",
                              MessageBoxButton.OK,
                              MessageBoxImage.Error);
            }
        }

        private void GeneralSettings_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("Paramètres généraux - À implémenter",
                          "Information",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }

        private void About_Click(object sender, RoutedEventArgs e)
        {
            var aboutMessage = "ZinStore - Système de Gestion de Supermarché\n\n" +
                             "Version: 1.0\n" +
                             "Développé avec: WPF, .NET 6, SQLite, Material Design\n" +
                             "Base de données: SQLite avec Dapper ORM\n\n" +
                             "© 2024 ZinStore. Tous droits réservés.";

            MessageBox.Show(aboutMessage,
                          "À propos de ZinStore",
                          MessageBoxButton.OK,
                          MessageBoxImage.Information);
        }
    }
}
