# إعداد قاعدة البيانات - ZinStore

## نظرة عامة

يوفر ZinStore عدة طرق لإعداد وإدارة قاعدة البيانات:

1. **نموذج إعداد قاعدة البيانات الرسومي** (في التطبيق)
2. **أداة سطر الأوامر** (ZinStore.DatabaseTool)
3. **إعداد تلقائي** (عند تشغيل التطبيق لأول مرة)

## 1. نموذج إعداد قاعدة البيانات الرسومي

### الوصول إلى النموذج:
1. شغل تطبيق ZinStore
2. انقر على زر "Paramètres" في شريط القائمة العلوي
3. اختر "Configuration Base de Données" من القائمة المنسدلة

### الميزات المتوفرة:
- **دعم قواعد بيانات متعددة**: SQLite, SQL Server, MySQL, PostgreSQL
- **إنشاء سلسلة الاتصال تلقائياً**
- **اختبار الاتصال** قبل الحفظ
- **تصفح الملفات** لاختيار موقع قاعدة البيانات
- **حفظ الإعدادات** في ملف التكوين

### خطوات الإعداد:

#### لقاعدة بيانات SQLite:
1. اختر "SQLite (Fichier local)" من قائمة نوع قاعدة البيانات
2. حدد مسار ملف قاعدة البيانات أو استخدم زر "Parcourir"
3. تأكد من تفعيل "Créer la base de données si elle n'existe pas"
4. انقر "Générer" لإنشاء سلسلة الاتصال
5. انقر "Tester la Connexion" للتأكد من صحة الإعدادات
6. انقر "Sauvegarder" لحفظ الإعدادات

#### لقاعدة بيانات SQL Server:
1. اختر "SQL Server" من قائمة نوع قاعدة البيانات
2. أدخل اسم الخادم
3. أدخل اسم قاعدة البيانات
4. اختر نوع المصادقة:
   - **Windows Authentication**: تفعيل "Utiliser l'authentification Windows"
   - **SQL Authentication**: إلغاء التفعيل وإدخال اسم المستخدم وكلمة المرور
5. انقر "Générer" لإنشاء سلسلة الاتصال
6. انقر "Tester la Connexion" للتأكد من صحة الإعدادات
7. انقر "Sauvegarder" لحفظ الإعدادات

## 2. أداة سطر الأوامر (ZinStore.DatabaseTool)

### بناء الأداة:
```bash
dotnet build ZinStore.DatabaseTool
```

### الأوامر المتوفرة:

#### إنشاء قاعدة بيانات جديدة:
```bash
dotnet run --project ZinStore.DatabaseTool create <مسار_قاعدة_البيانات>
```

**مثال:**
```bash
dotnet run --project ZinStore.DatabaseTool create .\Data\ZinStore.db
```

#### اختبار الاتصال:
```bash
dotnet run --project ZinStore.DatabaseTool test <مسار_قاعدة_البيانات>
```

**مثال:**
```bash
dotnet run --project ZinStore.DatabaseTool test .\Data\ZinStore.db
```

#### عرض معلومات قاعدة البيانات:
```bash
dotnet run --project ZinStore.DatabaseTool info <مسار_قاعدة_البيانات>
```

**مثال:**
```bash
dotnet run --project ZinStore.DatabaseTool info .\Data\ZinStore.db
```

#### عرض المساعدة:
```bash
dotnet run --project ZinStore.DatabaseTool help
```

## 3. الإعداد التلقائي

عند تشغيل التطبيق لأول مرة، سيقوم تلقائياً بـ:
1. إنشاء مجلد `Data` في مجلد التطبيق
2. إنشاء ملف قاعدة البيانات `ZinStore.db`
3. إنشاء جميع الجداول المطلوبة
4. إدراج البيانات الأولية (مستخدم admin)

## معلومات المستخدم الافتراضي

بعد إنشاء قاعدة البيانات، يتم إنشاء مستخدم افتراضي:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الصلاحيات**: جميع الصلاحيات (مدير النظام)

## ملفات التكوين

### App.config
يحتوي على سلسلة الاتصال الافتراضية:
```xml
<connectionStrings>
    <add name="ZinStoreDB" connectionString="Data Source=.\Data\ZinStore.db;Version=3;" />
</connectionStrings>
```

### تغيير سلسلة الاتصال
يمكن تغيير سلسلة الاتصال بعدة طرق:
1. **من خلال النموذج الرسومي** (الطريقة المفضلة)
2. **تعديل ملف App.config يدوياً**
3. **استخدام DatabaseConfigurationHelper في الكود**

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### "قاعدة البيانات غير موجودة"
- تأكد من وجود مجلد `Data`
- استخدم أداة سطر الأوامر لإنشاء قاعدة البيانات
- تحقق من صلاحيات الكتابة في المجلد

#### "فشل في الاتصال"
- تحقق من صحة مسار قاعدة البيانات
- تأكد من وجود ملف قاعدة البيانات
- تحقق من سلسلة الاتصال في App.config

#### "خطأ في الصلاحيات"
- تأكد من صلاحيات القراءة والكتابة للمجلد
- شغل التطبيق كمدير إذا لزم الأمر

## أمثلة عملية

### إنشاء قاعدة بيانات في مجلد مخصص:
```bash
dotnet run --project ZinStore.DatabaseTool create "C:\MyData\ZinStore.db"
```

### اختبار قاعدة بيانات موجودة:
```bash
dotnet run --project ZinStore.DatabaseTool test "C:\MyData\ZinStore.db"
```

### عرض معلومات مفصلة:
```bash
dotnet run --project ZinStore.DatabaseTool info "C:\MyData\ZinStore.db"
```

## الدعم الفني

للحصول على المساعدة:
1. تحقق من ملفات السجل في مجلد التطبيق
2. استخدم أداة سطر الأوامر لتشخيص المشاكل
3. تأكد من تحديث جميع الحزم والتبعيات

## ملاحظات مهمة

- **النسخ الاحتياطي**: احرص على عمل نسخ احتياطية منتظمة لقاعدة البيانات
- **الأمان**: غير كلمة مرور المدير الافتراضية بعد الإعداد
- **الأداء**: لقواعد البيانات الكبيرة، فكر في استخدام SQL Server بدلاً من SQLite
- **التحديثات**: تحقق من توافق إصدارات قاعدة البيانات عند التحديث
