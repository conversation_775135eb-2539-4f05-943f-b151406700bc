# Guide d'Installation - ZinStore

## 📋 Prérequis Système

### Configuration Minimale
- **Système d'exploitation**: Windows 7 SP1 ou supérieur (Windows 10/11 recommandé)
- **Processeur**: Intel Core i3 ou équivalent AMD
- **Mémoire RAM**: 4 GB minimum (8 GB recommandé)
- **Espace disque**: 500 MB pour l'application + espace pour les données
- **Résolution écran**: 1024x768 minimum (1920x1080 recommandé)

### Logiciels Requis
- **.NET Framework 4.7.2** ou supérieur
- **Visual C++ Redistributable** (généralement déjà installé)

## 🚀 Installation pour Utilisateurs

### Option 1: Installation Simple (Recommandée)
1. Téléchargez le fichier `ZinStore-Setup.exe`
2. Exécutez le fichier d'installation en tant qu'administrateur
3. Suivez les instructions de l'assistant d'installation
4. Lancez ZinStore depuis le menu Démarrer

### Option 2: Installation Portable
1. Téléchargez le fichier `ZinStore-Portable.zip`
2. Extrayez le contenu dans un dossier de votre choix
3. Exécutez `ZinStore.exe`

## 🛠️ Installation pour Développeurs

### Prérequis de Développement
- **Visual Studio 2019** ou supérieur (Community Edition suffisante)
- **.NET Framework 4.7.2 SDK**
- **Git** (pour cloner le repository)

### Étapes d'Installation
1. **Cloner le repository**
   ```bash
   git clone https://github.com/zinstore/zinstore.git
   cd zinstore
   ```

2. **Ouvrir la solution**
   ```bash
   # Ouvrir avec Visual Studio
   start ZinStore.sln
   ```

3. **Restaurer les packages NuGet**
   - Dans Visual Studio: `Tools > NuGet Package Manager > Restore NuGet Packages`
   - Ou via ligne de commande: `nuget restore ZinStore.sln`

4. **Compiler la solution**
   - Dans Visual Studio: `Build > Build Solution` (Ctrl+Shift+B)
   - Ou via ligne de commande: `msbuild ZinStore.sln`

5. **Exécuter l'application**
   - Définir `ZinStore.UI` comme projet de démarrage
   - Appuyer sur F5 ou cliquer sur "Start"

## 🔧 Configuration Initiale

### Premier Démarrage
1. **Connexion Administrateur**
   - Utilisateur: `admin`
   - Mot de passe: `admin123`

2. **Configuration Obligatoire**
   - Changez immédiatement le mot de passe administrateur
   - Configurez les informations de votre entreprise
   - Définissez les paramètres de TVA et devise

3. **Configuration Optionnelle**
   - Créez des utilisateurs supplémentaires
   - Configurez les catégories de produits
   - Importez vos données existantes

### Structure des Dossiers
```
ZinStore/
├── Data/                   # Base de données SQLite
├── Backups/               # Sauvegardes automatiques
├── Reports/               # Rapports générés
├── Logs/                  # Fichiers de log
└── Config/                # Fichiers de configuration
```

## 🔒 Sécurité et Permissions

### Permissions Windows
L'application nécessite les permissions suivantes:
- **Lecture/Écriture** dans le dossier d'installation
- **Accès réseau** (pour les futures fonctionnalités)
- **Accès aux imprimantes** (pour l'impression des factures)

### Sécurité des Données
- La base de données est chiffrée localement
- Les mots de passe sont hachés avec salt
- Sauvegarde automatique recommandée

## 🔄 Mise à Jour

### Mise à Jour Automatique
- L'application vérifie automatiquement les mises à jour
- Notification lors de la disponibilité d'une nouvelle version
- Mise à jour en un clic (nécessite une connexion internet)

### Mise à Jour Manuelle
1. Téléchargez la nouvelle version
2. Fermez ZinStore
3. Sauvegardez vos données
4. Installez la nouvelle version
5. Vos données seront automatiquement migrées

## 🆘 Résolution de Problèmes

### Problèmes Courants

#### L'application ne démarre pas
- Vérifiez que .NET Framework 4.7.2 est installé
- Exécutez en tant qu'administrateur
- Vérifiez les logs dans le dossier `Logs/`

#### Erreur de base de données
- Vérifiez les permissions du dossier `Data/`
- Restaurez depuis une sauvegarde récente
- Contactez le support technique

#### Performance lente
- Vérifiez l'espace disque disponible
- Optimisez la base de données (menu Outils)
- Augmentez la RAM si possible

### Fichiers de Log
Les logs sont stockés dans `Logs/ZinStore.log` et contiennent:
- Erreurs système
- Actions utilisateur importantes
- Performances de l'application

## 📞 Support Technique

### Assistance
- **Email**: <EMAIL>
- **Téléphone**: +213 XXX XXX XXX
- **Documentation**: https://docs.zinstore.com
- **Forum**: https://forum.zinstore.com

### Informations à Fournir
Lors d'une demande de support, incluez:
- Version de ZinStore
- Version de Windows
- Description détaillée du problème
- Fichiers de log récents
- Captures d'écran si applicable

## 📄 Licence et Conditions

- ZinStore est sous licence MIT
- Utilisation commerciale autorisée
- Support technique inclus pendant 1 an
- Mises à jour gratuites pendant 1 an

---

**ZinStore Team** - Votre partenaire pour la gestion de supermarché 🇩🇿
