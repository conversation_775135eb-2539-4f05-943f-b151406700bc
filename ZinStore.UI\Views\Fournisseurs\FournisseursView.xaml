<UserControl x:Class="ZinStore.UI.Views.FournisseursView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">

    <UserControl.DataContext>
        <viewModels:FournisseursViewModel />
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0"
                  Text="Gestion des Fournisseurs"
                  Style="{StaticResource TitleText}"/>

        <!-- Barre d'outils -->
        <materialDesign:Card Grid.Row="1"
                           Margin="0,0,0,20"
                           Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche -->
                <TextBox Grid.Column="0"
                        materialDesign:HintAssist.Hint="Rechercher un fournisseur..."
                        Style="{StaticResource CustomTextBox}"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        Width="300"
                        HorizontalAlignment="Left"/>

                <!-- Boutons -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal">
                    <Button Content="Nouveau Fournisseur"
                           Style="{StaticResource PrimaryButton}"
                           Command="{Binding AddFournisseurCommand}"/>
                    <Button Content="Modifier"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding EditFournisseurCommand}"/>
                    <Button Content="Supprimer"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding DeleteFournisseurCommand}"/>
                    <Button Content="Actualiser"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding RefreshCommand}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des fournisseurs -->
        <materialDesign:Card Grid.Row="2"
                           Padding="15">
            <DataGrid ItemsSource="{Binding Fournisseurs}"
                     SelectedItem="{Binding SelectedFournisseur}"
                     Style="{StaticResource CustomDataGrid}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Code"
                                      Binding="{Binding CodeFournisseur}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Raison Sociale"
                                      Binding="{Binding RaisonSociale}"
                                      Width="200"/>
                    <DataGridTextColumn Header="Contact"
                                      Binding="{Binding PersonneContact}"
                                      Width="150"/>
                    <DataGridTextColumn Header="Téléphone"
                                      Binding="{Binding Telephone}"
                                      Width="120"/>
                    <DataGridTextColumn Header="Email"
                                      Binding="{Binding Email}"
                                      Width="200"/>
                    <DataGridTextColumn Header="Ville"
                                      Binding="{Binding Ville}"
                                      Width="120"/>
                    <DataGridTextColumn Header="Solde"
                                      Binding="{Binding SoldeCompte, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridCheckBoxColumn Header="Actif"
                                          Binding="{Binding EstActif}"
                                          Width="60"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
