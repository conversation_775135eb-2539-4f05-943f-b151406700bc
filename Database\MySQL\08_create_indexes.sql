-- =====================================================
-- Script de création des index supplémentaires ZinStore
-- Index pour optimiser les performances
-- Version: MySQL 8.0+
-- =====================================================

USE zinstore;

-- =====================================================
-- INDEX COMPOSITES POUR LES REQUÊTES FRÉQUENTES
-- =====================================================

-- Index pour les recherches de ventes par date et client
CREATE INDEX idx_ventes_date_client ON ventes(date_vente, client_id, statut_paiement);

-- Index pour les recherches de ventes par utilisateur et période
CREATE INDEX idx_ventes_utilisateur_date ON ventes(utilisateur_id, date_vente, statut_paiement);

-- Index pour les recherches d'achats par fournisseur et date
CREATE INDEX idx_achats_fournisseur_date ON achats(fournisseur_id, date_commande, statut_commande);

-- Index pour les recherches de produits par catégorie et statut
CREATE INDEX idx_produits_categorie_actif ON produits(categorie_id, est_actif, prix_vente);

-- Index pour les recherches de stock par quantité
CREATE INDEX idx_stock_quantites ON stock(quantite_actuelle, quantite_disponible, produit_id);

-- Index pour les mouvements de stock par produit et date
CREATE INDEX idx_mouvements_produit_date ON mouvements_stock(produit_id, date_mouvement, type_mouvement);

-- Index pour les revenus par date et catégorie
CREATE INDEX idx_revenus_date_categorie ON revenus(date_revenu, categorie, montant);

-- Index pour les dépenses par date et catégorie
CREATE INDEX idx_depenses_date_categorie ON depenses(date_depense, categorie, montant);

-- Index pour les clients par type et statut
CREATE INDEX idx_clients_type_actif ON clients(type_client, est_actif, date_creation);

-- Index pour les fournisseurs par statut
CREATE INDEX idx_fournisseurs_actif ON fournisseurs(est_actif, nom);

-- =====================================================
-- INDEX FULL-TEXT POUR LA RECHERCHE TEXTUELLE
-- =====================================================

-- Index full-text pour la recherche de produits
ALTER TABLE produits ADD FULLTEXT(nom, description, code_produit);

-- Index full-text pour la recherche de clients
ALTER TABLE clients ADD FULLTEXT(nom, prenom, email, adresse);

-- Index full-text pour la recherche de fournisseurs
ALTER TABLE fournisseurs ADD FULLTEXT(nom, contact_nom, email, adresse);

-- Index full-text pour la recherche de catégories
ALTER TABLE categories ADD FULLTEXT(nom, description);

-- =====================================================
-- INDEX POUR LES COLONNES CALCULÉES ET GÉNÉRÉES
-- =====================================================

-- Index sur les colonnes générées pour les produits
CREATE INDEX idx_produits_marge ON produits(marge_benefice);
CREATE INDEX idx_produits_expiration ON produits(a_expiration, date_expiration);

-- Index sur les colonnes générées pour les ventes
CREATE INDEX idx_ventes_montant_total ON ventes(montant_total);
CREATE INDEX idx_ventes_montant_restant ON ventes(montant_restant);

-- Index sur les colonnes générées pour les achats
CREATE INDEX idx_achats_montant_total ON achats(montant_total);
CREATE INDEX idx_achats_montant_restant ON achats(montant_restant);

-- Index sur les colonnes générées pour le stock
CREATE INDEX idx_stock_valeur ON stock(valeur_stock);

-- =====================================================
-- INDEX POUR LES REQUÊTES DE REPORTING
-- =====================================================

-- Index pour les rapports de ventes par mois
CREATE INDEX idx_ventes_reporting ON ventes(
    YEAR(date_vente), 
    MONTH(date_vente), 
    statut_paiement, 
    montant_total
);

-- Index pour les rapports d'achats par mois
CREATE INDEX idx_achats_reporting ON achats(
    YEAR(date_commande), 
    MONTH(date_commande), 
    statut_paiement, 
    montant_total
);

-- Index pour les rapports financiers
CREATE INDEX idx_revenus_reporting ON revenus(
    YEAR(date_revenu), 
    MONTH(date_revenu), 
    categorie, 
    montant
);

CREATE INDEX idx_depenses_reporting ON depenses(
    YEAR(date_depense), 
    MONTH(date_depense), 
    categorie, 
    montant
);

-- =====================================================
-- INDEX POUR LES JOINTURES FRÉQUENTES
-- =====================================================

-- Index pour les jointures ventes-détails
CREATE INDEX idx_ventes_details_join ON ventes_details(vente_id, produit_id, quantite);

-- Index pour les jointures achats-détails
CREATE INDEX idx_achats_details_join ON achats_details(achat_id, produit_id, quantite_commandee);

-- Index pour les jointures produits-stock
CREATE INDEX idx_produits_stock_join ON stock(produit_id);

-- =====================================================
-- INDEX POUR LES CONTRAINTES MÉTIER
-- =====================================================

-- Index unique composite pour éviter les doublons de stock par produit
-- (Déjà créé dans la définition de table, mais documenté ici)
-- UNIQUE KEY unique_produit_stock (produit_id)

-- Index pour garantir l'unicité des codes produits actifs
CREATE UNIQUE INDEX idx_produits_code_actif ON produits(code_produit, est_actif);

-- Index pour garantir l'unicité des numéros de clients actifs
CREATE UNIQUE INDEX idx_clients_numero_actif ON clients(numero_client, est_actif);

-- Index pour garantir l'unicité des numéros de fournisseurs actifs
CREATE UNIQUE INDEX idx_fournisseurs_numero_actif ON fournisseurs(numero_fournisseur, est_actif);

-- =====================================================
-- INDEX POUR LES REQUÊTES D'AUDIT ET TRAÇABILITÉ
-- =====================================================

-- Index pour tracer les modifications par utilisateur
CREATE INDEX idx_ventes_audit ON ventes(utilisateur_id, date_creation, date_modification);
CREATE INDEX idx_achats_audit ON achats(utilisateur_id, date_creation, date_modification);
CREATE INDEX idx_mouvements_audit ON mouvements_stock(utilisateur_id, date_mouvement);

-- Index pour tracer les connexions utilisateurs
CREATE INDEX idx_utilisateurs_connexion ON utilisateurs(derniere_connexion, est_actif);

-- =====================================================
-- INDEX POUR LES REQUÊTES DE PERFORMANCE DASHBOARD
-- =====================================================

-- Index pour les statistiques rapides du dashboard
CREATE INDEX idx_dashboard_ventes ON ventes(
    DATE(date_vente), 
    statut_paiement, 
    montant_total
);

CREATE INDEX idx_dashboard_stock ON stock(
    quantite_actuelle, 
    quantite_disponible
);

-- Index pour les top produits vendus
CREATE INDEX idx_top_produits ON ventes_details(
    produit_id, 
    quantite, 
    sous_total
);

-- =====================================================
-- ANALYSE DES INDEX EXISTANTS
-- =====================================================

-- Requête pour vérifier l'utilisation des index
-- (À exécuter périodiquement pour optimiser)
/*
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'zinstore'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
*/

-- =====================================================
-- RECOMMANDATIONS D'OPTIMISATION
-- =====================================================

/*
RECOMMANDATIONS POUR L'OPTIMISATION DES PERFORMANCES:

1. MONITORING DES INDEX:
   - Utiliser EXPLAIN pour analyser les requêtes lentes
   - Surveiller l'utilisation des index avec SHOW INDEX
   - Identifier les index inutilisés avec sys.schema_unused_indexes

2. MAINTENANCE PÉRIODIQUE:
   - ANALYZE TABLE pour mettre à jour les statistiques
   - OPTIMIZE TABLE pour défragmenter (MyISAM seulement)
   - Surveiller la taille des index vs tables

3. REQUÊTES À OPTIMISER EN PRIORITÉ:
   - Recherche de produits par nom/code
   - Rapports de ventes par période
   - Calculs de stock en temps réel
   - Statistiques du dashboard

4. INDEX À SURVEILLER:
   - Index full-text (performance variable selon la taille)
   - Index sur colonnes calculées (overhead sur les écritures)
   - Index composites (ordre des colonnes important)

5. PARTITIONING (pour grandes bases):
   - Partitionner les ventes par mois/année
   - Partitionner les mouvements de stock par date
   - Archiver les données anciennes
*/
