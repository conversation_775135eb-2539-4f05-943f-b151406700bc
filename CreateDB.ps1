# Script simple pour creer la base de donnees ZinStore
Write-Host "Creation de la base de donnees ZinStore..." -ForegroundColor Green

# Chemin de la base de donnees
$dbPath = "ZinStore.UI\Data\ZinStore.db"
$dataDir = "ZinStore.UI\Data"

# Creer le dossier Data s'il n'existe pas
if (!(Test-Path $dataDir)) {
    New-Item -ItemType Directory -Path $dataDir -Force
    Write-Host "Dossier Data cree" -ForegroundColor Green
}

# Verifier si la base de donnees existe deja
if (Test-Path $dbPath) {
    Write-Host "La base de donnees existe deja" -ForegroundColor Yellow
} else {
    # Creer un fichier vide pour la base de donnees
    New-Item -ItemType File -Path $dbPath -Force
    Write-Host "Fichier de base de donnees cree: $dbPath" -ForegroundColor Green
}

Write-Host "Terminé! Vous pouvez maintenant lancer l'application." -ForegroundColor Green
