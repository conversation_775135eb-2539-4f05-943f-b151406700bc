using System;
using System.ComponentModel.DataAnnotations;
using ZinStore.Core.Enums;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les mouvements de stock
    /// </summary>
    public class MouvementStock : BaseEntity
    {
        [Required]
        public int ProduitId { get; set; }

        [Required]
        public TypeMouvement TypeMouvement { get; set; }

        [Required]
        public decimal Quantite { get; set; }

        public decimal QuantiteAvant { get; set; }

        public decimal QuantiteApres { get; set; }

        public decimal CoutUnitaire { get; set; }

        public decimal ValeurMouvement { get; set; }

        [Required]
        public DateTime DateMouvement { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string Reference { get; set; }

        [StringLength(100)]
        public string Motif { get; set; }

        public int? VenteId { get; set; }

        public int? AchatId { get; set; }

        public int? UtilisateurId { get; set; }

        public string Notes { get; set; }

        // Propriétés de navigation
        public virtual Produit Produit { get; set; }
        public virtual Vente Vente { get; set; }
        public virtual Achat Achat { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
    }
}
