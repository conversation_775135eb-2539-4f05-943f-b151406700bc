<Window x:Class="ZinStore.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
        Title="ZinStore - Gestion de Supermarché"
        Height="800"
        Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.DataContext>
        <viewModels:MainViewModel />
    </Window.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Sidebar Navigation -->
        <materialDesign:Card Grid.Column="0"
                           x:Name="NavigationSidebar"
                           Width="250"
                           materialDesign:ElevationAssist.Elevation="Dp4"
                           Background="{DynamicResource MaterialDesignCardBackground}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header avec logo -->
                <Border Grid.Row="0"
                       Background="{DynamicResource PrimaryHueMidBrush}"
                       Padding="20,15">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Store"
                                               Width="28"
                                               Height="28"
                                               Foreground="White"
                                               VerticalAlignment="Center"/>
                        <TextBlock Text="ZinStore"
                                  FontSize="18"
                                  FontWeight="Bold"
                                  Foreground="White"
                                  VerticalAlignment="Center"
                                  Margin="10,0,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Menu Navigation -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="0,10">

                        <!-- Dashboard -->
                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowDashboardCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ViewDashboard" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Tableau de Bord" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Separator -->
                        <Separator Margin="15,10"/>

                        <!-- Section Ventes -->
                        <TextBlock Text="VENTES &amp; CLIENTS"
                                  FontSize="12"
                                  FontWeight="Bold"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                                  Margin="20,10,20,5"/>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowPOSCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CashRegister" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Point de Vente" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowVentesCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartLine" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Gestion Ventes" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowClientsCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountGroup" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Clients" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Section Stock -->
                        <TextBlock Text="STOCK &amp; PRODUITS"
                                  FontSize="12"
                                  FontWeight="Bold"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                                  Margin="20,15,20,5"/>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowStockCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Package" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Gestion Stock" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowProduitsCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Tag" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Produits" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowCategoriesCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderMultiple" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Catégories" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowAchatsCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ShoppingCart" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Achats" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowFournisseursCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Truck" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Fournisseurs" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Section Finance -->
                        <TextBlock Text="FINANCE &amp; RAPPORTS"
                                  FontSize="12"
                                  FontWeight="Bold"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                                  Margin="20,15,20,5"/>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowFinanceCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Finance" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowProfitLossCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="TrendingUp" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Profits &amp; Pertes" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowRapportsCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FileChart" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Rapports" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- Section Administration -->
                        <TextBlock Text="ADMINISTRATION"
                                  FontSize="12"
                                  FontWeight="Bold"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                                  Margin="20,15,20,5"/>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowUsersCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="AccountMultiple" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Utilisateurs" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                               Command="{Binding ShowSettingsCommand}"
                               HorizontalAlignment="Stretch"
                               HorizontalContentAlignment="Left"
                               Padding="20,12"
                               Margin="5,2">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" Margin="0,0,15,0"/>
                                <TextBlock Text="Paramètres" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                    </StackPanel>
                </ScrollViewer>

                <!-- Footer avec utilisateur -->
                <Border Grid.Row="2"
                       Background="{DynamicResource MaterialDesignDivider}"
                       Padding="15,10">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                            <materialDesign:PackIcon Kind="Account" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="{Binding CurrentUserName}" FontSize="12" FontWeight="Bold"/>
                        </StackPanel>
                        <Button Content="Déconnexion"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding LogoutCommand}"
                               HorizontalAlignment="Stretch"
                               Height="30"
                               FontSize="11"/>
                    </StackPanel>
                </Border>
            </Grid>
        </materialDesign:Card>

        <!-- Main Content Area -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Top Bar avec bouton hamburger -->
            <materialDesign:Card Grid.Row="0"
                               materialDesign:ElevationAssist.Elevation="Dp2"
                               Margin="0,0,0,5"
                               Padding="15,10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- Bouton hamburger pour masquer/afficher sidebar -->
                    <Button Grid.Column="0"
                           x:Name="HamburgerButton"
                           Style="{StaticResource MaterialDesignIconButton}"
                           Click="HamburgerButton_Click"
                           ToolTip="Masquer/Afficher le menu"
                           Margin="0,0,15,0">
                        <materialDesign:PackIcon Kind="Menu" Width="24" Height="24"/>
                    </Button>

                    <!-- Titre de la page actuelle -->
                    <TextBlock Grid.Column="1"
                              Text="{Binding CurrentPageTitle}"
                              FontSize="18"
                              FontWeight="Bold"
                              VerticalAlignment="Center"/>

                    <!-- Actions rapides -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <!-- Menu Paramètres -->
                        <Button x:Name="SettingsButton"
                               Style="{StaticResource MaterialDesignIconButton}"
                               Margin="0,0,10,0"
                               ToolTip="Paramètres"
                               Click="SettingsButton_Click">
                            <materialDesign:PackIcon Kind="Settings" Width="20" Height="20"/>
                            <Button.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="Configuration Base de Données"
                                             Click="DatabaseConfig_Click">
                                        <MenuItem.Icon>
                                            <materialDesign:PackIcon Kind="Database"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <MenuItem Header="Paramètres Généraux"
                                             Click="GeneralSettings_Click">
                                        <MenuItem.Icon>
                                            <materialDesign:PackIcon Kind="Cog"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                    <Separator/>
                                    <MenuItem Header="À propos"
                                             Click="About_Click">
                                        <MenuItem.Icon>
                                            <materialDesign:PackIcon Kind="Information"/>
                                        </MenuItem.Icon>
                                    </MenuItem>
                                </ContextMenu>
                            </Button.ContextMenu>
                        </Button>

                        <!-- Notifications -->
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="Notifications"
                               Margin="0,0,10,0">
                            <materialDesign:PackIcon Kind="Bell" Width="20" Height="20"/>
                        </Button>

                        <!-- Profil utilisateur -->
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                               ToolTip="{Binding CurrentUserName}">
                            <materialDesign:PackIcon Kind="Account" Width="20" Height="20"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:Card>

            <!-- Contenu principal -->
            <ContentControl Grid.Row="1"
                           Content="{Binding CurrentView}"
                           Margin="15"/>

            <!-- Barre d'état -->
            <materialDesign:Card Grid.Row="2"
                               materialDesign:ElevationAssist.Elevation="Dp2"
                               Margin="0,5,0,0"
                               Padding="15,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                              Text="{Binding StatusMessage}"
                              VerticalAlignment="Center"/>

                    <TextBlock Grid.Column="1"
                              Text="{Binding CurrentDateTime, StringFormat='dd/MM/yyyy HH:mm'}"
                              VerticalAlignment="Center"
                              Margin="0,0,20,0"/>

                    <TextBlock Grid.Column="2"
                              Text="Version 1.0"
                              VerticalAlignment="Center"
                              Opacity="0.6"/>
                </Grid>
            </materialDesign:Card>
        </Grid>
    </Grid>
</Window>
