using System;
using System.Data.SQLite;
using System.Security.Cryptography;
using System.Text;

class Program
{
    static void Main()
    {
        string dbPath = @".\Data\ZinStore.db";
        string connectionString = $"Data Source={dbPath};Version=3;";
        
        Console.WriteLine("=== Test de connexion ZinStore ===");
        Console.WriteLine($"Base de données: {dbPath}");
        Console.WriteLine();
        
        try
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("✓ Connexion à la base de données réussie");
                
                // Lire l'utilisateur admin
                var command = new SQLiteCommand("SELECT NomUtilisateur, MotDePasse FROM Utilisateurs WHERE NomUtilisateur = 'admin'", connection);
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        string username = reader["NomUtilisateur"].ToString();
                        string storedPassword = reader["MotDePasse"].ToString();
                        
                        Console.WriteLine($"✓ Utilisateur trouvé: {username}");
                        Console.WriteLine($"✓ Mot de passe stocké: {storedPassword.Substring(0, 20)}...");
                        
                        // Tester le hachage
                        string testPassword = "admin123";
                        string hashedTest = HashPassword(testPassword);
                        
                        Console.WriteLine($"✓ Mot de passe test haché: {hashedTest.Substring(0, 20)}...");
                        Console.WriteLine($"✓ Correspondance: {storedPassword == hashedTest}");
                        
                        if (storedPassword == hashedTest)
                        {
                            Console.WriteLine("🎉 Le système de connexion devrait fonctionner!");
                        }
                        else
                        {
                            Console.WriteLine("❌ Problème de correspondance des mots de passe");
                        }
                    }
                    else
                    {
                        Console.WriteLine("❌ Utilisateur admin non trouvé");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erreur: {ex.Message}");
        }
        
        Console.WriteLine();
        Console.WriteLine("Appuyez sur une touche pour continuer...");
        Console.ReadKey();
    }
    
    static string HashPassword(string password)
    {
        if (string.IsNullOrEmpty(password))
            return string.Empty;

        using (SHA256 sha256Hash = SHA256.Create())
        {
            // Ajouter un salt pour plus de sécurité
            string saltedPassword = password + "ZinStore2024!@#";
            
            // Calculer le hash
            byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));

            // Convertir en string hexadécimal
            StringBuilder builder = new StringBuilder();
            for (int i = 0; i < bytes.Length; i++)
            {
                builder.Append(bytes[i].ToString("x2"));
            }
            return builder.ToString();
        }
    }
}
