using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class ClientsViewModel : BaseViewModel
    {
        private readonly ClientService _clientService;

        public ClientsViewModel()
        {
            _clientService = new ClientService(new DatabaseContext());

            Clients = new ObservableCollection<Client>();

            AddClientCommand = new RelayCommand(AddClient);
            EditClientCommand = new RelayCommand(EditClient, CanEditClient);
            DeleteClientCommand = new RelayCommand(DeleteClient, CanDeleteClient);
            RefreshCommand = new RelayCommand(async () => await LoadClientsAsync());

            _ = LoadClientsAsync();
        }

        public ObservableCollection<Client> Clients { get; }

        private Client _selectedClient;
        public Client SelectedClient
        {
            get => _selectedClient;
            set => SetProperty(ref _selectedClient, value);
        }

        private string _searchText;
        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                SearchClients();
            }
        }

        public ICommand AddClientCommand { get; }
        public ICommand EditClientCommand { get; }
        public ICommand DeleteClientCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadClientsAsync()
        {
            try
            {
                IsBusy = true;
                var clients = await _clientService.GetAllClientsAsync();

                Clients.Clear();
                foreach (var client in clients)
                {
                    Clients.Add(client);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des clients: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async void SearchClients()
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await LoadClientsAsync();
                return;
            }

            try
            {
                var clients = await _clientService.SearchClientsAsync(SearchText);

                Clients.Clear();
                foreach (var client in clients)
                {
                    Clients.Add(client);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void AddClient()
        {
            var window = new Views.Clients.ClientFormWindow();
            if (window.ShowDialog() == true && window.ClientSaved)
            {
                _ = LoadClientsAsync();
            }
        }

        private void EditClient()
        {
            if (SelectedClient == null) return;

            var window = new Views.Clients.ClientFormWindow(SelectedClient);
            if (window.ShowDialog() == true && window.ClientSaved)
            {
                _ = LoadClientsAsync();
            }
        }

        private async void DeleteClient()
        {
            if (SelectedClient == null) return;

            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir supprimer le client {SelectedClient.Nom} {SelectedClient.Prenom} ?"))
            {
                try
                {
                    var result = await _clientService.DeleteClientAsync(SelectedClient.Id);
                    if (result.Success)
                    {
                        MessageBoxHelper.ShowSuccess(result.Message);
                        await LoadClientsAsync();
                    }
                    else
                    {
                        MessageBoxHelper.ShowError(result.Message);
                    }
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la suppression: {ex.Message}");
                }
            }
        }

        private bool CanEditClient()
        {
            return SelectedClient != null;
        }

        private bool CanDeleteClient()
        {
            return SelectedClient != null;
        }
    }
}
