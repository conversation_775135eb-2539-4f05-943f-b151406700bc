using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.Views.Products
{
    public partial class ImageSelectorWindow : Window
    {
        public ObservableCollection<ProductImage> SelectedImages { get; set; }
        public List<string> ImagePaths { get; private set; }

        private readonly string[] _supportedFormats = { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };

        public ImageSelectorWindow()
        {
            InitializeComponent();
            SelectedImages = new ObservableCollection<ProductImage>();
            ImagePaths = new List<string>();
            SelectedImagesPanel.ItemsSource = SelectedImages;
            UpdateImageCount();
        }

        public ImageSelectorWindow(List<string> existingImages) : this()
        {
            if (existingImages != null)
            {
                foreach (var imagePath in existingImages)
                {
                    if (File.Exists(imagePath))
                    {
                        AddImage(imagePath);
                    }
                }
            }
        }

        private void BrowseButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Sélectionner des images",
                Filter = "Images|*.jpg;*.jpeg;*.png;*.bmp;*.gif|Tous les fichiers|*.*",
                Multiselect = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                foreach (var fileName in openFileDialog.FileNames)
                {
                    AddImage(fileName);
                }
            }
        }

        private void CameraButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité de capture par caméra en cours de développement.");
        }

        private void DropZone_Drop(object sender, DragEventArgs e)
        {
            ResetDropZoneStyle();

            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                foreach (var file in files)
                {
                    if (IsImageFile(file))
                    {
                        AddImage(file);
                    }
                }
            }
        }

        private void DropZone_DragOver(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files.Any(IsImageFile))
                {
                    e.Effects = DragDropEffects.Copy;
                    HighlightDropZone();
                }
                else
                {
                    e.Effects = DragDropEffects.None;
                }
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
            e.Handled = true;
        }

        private void DropZone_DragLeave(object sender, DragEventArgs e)
        {
            ResetDropZoneStyle();
        }

        private void SetMainImage_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductImage image)
            {
                // Déplacer l'image sélectionnée en première position
                var index = SelectedImages.IndexOf(image);
                if (index > 0)
                {
                    SelectedImages.Move(index, 0);
                    UpdateImagePaths();
                    MessageBoxHelper.ShowSuccess("Image principale définie avec succès.");
                }
            }
        }

        private void RemoveImage_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ProductImage image)
            {
                SelectedImages.Remove(image);
                UpdateImagePaths();
                UpdateImageCount();
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void AddImage(string filePath)
        {
            try
            {
                if (!IsImageFile(filePath))
                {
                    MessageBoxHelper.ShowWarning($"Le fichier {Path.GetFileName(filePath)} n'est pas un format d'image supporté.");
                    return;
                }

                // Vérifier si l'image n'est pas déjà ajoutée
                if (SelectedImages.Any(img => img.ImagePath == filePath))
                {
                    return;
                }

                var fileInfo = new FileInfo(filePath);
                var productImage = new ProductImage
                {
                    ImagePath = filePath,
                    FileName = fileInfo.Name,
                    FileSize = FormatFileSize(fileInfo.Length)
                };

                SelectedImages.Add(productImage);
                UpdateImagePaths();
                UpdateImageCount();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ajout de l'image: {ex.Message}");
            }
        }

        private bool IsImageFile(string filePath)
        {
            var extension = Path.GetExtension(filePath).ToLower();
            return _supportedFormats.Contains(extension);
        }

        private void UpdateImagePaths()
        {
            ImagePaths = SelectedImages.Select(img => img.ImagePath).ToList();
        }

        private void UpdateImageCount()
        {
            ImageCountText.Text = $"({SelectedImages.Count})";
        }

        private void HighlightDropZone()
        {
            DropZone.BorderBrush = new SolidColorBrush(Colors.DodgerBlue);
            DropZone.Background = new SolidColorBrush(Color.FromArgb(20, 30, 144, 255));
        }

        private void ResetDropZoneStyle()
        {
            DropZone.BorderBrush = (Brush)FindResource("MaterialDesignDivider");
            DropZone.Background = (Brush)FindResource("MaterialDesignCardBackground");
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    public class ProductImage
    {
        public string ImagePath { get; set; }
        public string FileName { get; set; }
        public string FileSize { get; set; }
    }
}
