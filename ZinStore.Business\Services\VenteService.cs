using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;

namespace ZinStore.Business.Services
{
    public class VenteService
    {
        private readonly VenteRepository _venteRepository;

        public VenteService(DatabaseContext context)
        {
            _venteRepository = new VenteRepository(context);
        }

        public async Task<IEnumerable<Vente>> GetAllVentesAsync()
        {
            return await _venteRepository.GetAllAsync();
        }

        public async Task<Vente> GetVenteByIdAsync(int id)
        {
            return await _venteRepository.GetByIdAsync(id);
        }

        public async Task<(bool Success, string Message, int VenteId)> AddVenteAsync(Vente vente)
        {
            try
            {
                var validationResult = ValidateVente(vente);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage, 0);
                }

                vente.DateCreation = DateTime.Now;
                int venteId = await _venteRepository.AddAsync(vente);
                return (true, "Vente enregistrée avec succès.", venteId);
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de l'enregistrement de la vente: {ex.Message}", 0);
            }
        }

        public async Task<string> GenerateNextNumeroFactureAsync()
        {
            return await _venteRepository.GenerateNextNumeroFactureAsync("FV");
        }

        private (bool IsValid, string ErrorMessage) ValidateVente(Vente vente)
        {
            if (vente == null)
                return (false, "Les données de la vente sont requises.");

            if (string.IsNullOrWhiteSpace(vente.NumeroFacture))
                return (false, "Le numéro de facture est obligatoire.");

            if (vente.ClientId <= 0)
                return (false, "Le client est obligatoire.");

            if (vente.LignesVente == null || !vente.LignesVente.Any())
                return (false, "Au moins un article doit être ajouté à la vente.");

            if (vente.MontantTotal <= 0)
                return (false, "Le montant total doit être positif.");

            return (true, string.Empty);
        }
    }
}
