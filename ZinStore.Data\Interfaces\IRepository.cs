using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;

namespace ZinStore.Data.Interfaces
{
    /// <summary>
    /// Interface générique pour les repositories
    /// </summary>
    /// <typeparam name="T">Type d'entité</typeparam>
    public interface IRepository<T> where T : BaseEntity
    {
        /// <summary>
        /// Obtient toutes les entités
        /// </summary>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// Obtient une entité par son ID
        /// </summary>
        Task<T> GetByIdAsync(int id);

        /// <summary>
        /// Ajoute une nouvelle entité
        /// </summary>
        Task<int> AddAsync(T entity);

        /// <summary>
        /// Met à jour une entité existante
        /// </summary>
        Task<bool> UpdateAsync(T entity);

        /// <summary>
        /// Supprime une entité (suppression logique)
        /// </summary>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// Supprime définitivement une entité
        /// </summary>
        Task<bool> HardDeleteAsync(int id);

        /// <summary>
        /// Obtient les entités avec pagination
        /// </summary>
        Task<IEnumerable<T>> GetPagedAsync(int pageNumber, int pageSize);

        /// <summary>
        /// Compte le nombre total d'entités
        /// </summary>
        Task<int> CountAsync();

        /// <summary>
        /// Recherche des entités selon un critère
        /// </summary>
        Task<IEnumerable<T>> SearchAsync(string searchTerm);

        /// <summary>
        /// Vérifie si une entité existe
        /// </summary>
        Task<bool> ExistsAsync(int id);
    }
}
