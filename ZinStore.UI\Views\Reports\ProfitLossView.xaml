<UserControl x:Class="ZinStore.UI.Views.Reports.ProfitLossView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.DataContext>
        <viewModels:ProfitLossViewModel/>
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="ChartLine" 
                                           Width="32" Height="32"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="📊 Rapport Profits et Pertes"
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              FontWeight="Bold"
                              VerticalAlignment="Center"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              Margin="10,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding GenerateReportCommand}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileChart" VerticalAlignment="Center"/>
                            <TextBlock Text="Générer Rapport" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding ExportCommand}"
                           Margin="0,0,10,0">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileExport" VerticalAlignment="Center"/>
                            <TextBlock Text="Exporter" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding PrintCommand}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Printer" VerticalAlignment="Center"/>
                            <TextBlock Text="Imprimer" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Filtres et période -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" 
                          Text="Période:" 
                          FontWeight="Bold"
                          VerticalAlignment="Center"
                          Margin="0,0,10,0"/>

                <ComboBox Grid.Column="1"
                         SelectedValue="{Binding SelectedPeriod}"
                         materialDesign:HintAssist.Hint="Sélectionner période"
                         Style="{StaticResource MaterialDesignOutlinedComboBox}">
                    <ComboBoxItem Content="Aujourd'hui" Tag="Today"/>
                    <ComboBoxItem Content="Cette semaine" Tag="ThisWeek"/>
                    <ComboBoxItem Content="Ce mois" Tag="ThisMonth"/>
                    <ComboBoxItem Content="Ce trimestre" Tag="ThisQuarter"/>
                    <ComboBoxItem Content="Cette année" Tag="ThisYear"/>
                    <ComboBoxItem Content="Personnalisé" Tag="Custom"/>
                </ComboBox>

                <DatePicker Grid.Column="3"
                           SelectedDate="{Binding DateFrom}"
                           materialDesign:HintAssist.Hint="Du"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>

                <DatePicker Grid.Column="5"
                           SelectedDate="{Binding DateTo}"
                           materialDesign:HintAssist.Hint="Au"
                           Style="{StaticResource MaterialDesignOutlinedDatePicker}"/>

                <Button Grid.Column="7"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Command="{Binding RefreshCommand}"
                       Margin="10,0,0,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Refresh" VerticalAlignment="Center"/>
                        <TextBlock Text="Actualiser" Margin="5,0,0,0"/>
                    </StackPanel>
                </Button>
            </Grid>
        </materialDesign:Card>

        <!-- Contenu principal -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Rapport détaillé -->
            <materialDesign:Card Grid.Column="0" Padding="0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- En-tête du rapport -->
                        <Border Background="{DynamicResource PrimaryHueLightBrush}" 
                               Padding="30,20">
                            <StackPanel>
                                <TextBlock Text="{Binding CompanyName}" 
                                          FontSize="18" 
                                          FontWeight="Bold"
                                          Foreground="White"
                                          HorizontalAlignment="Center"/>
                                <TextBlock Text="RAPPORT PROFITS ET PERTES"
                                          FontSize="16" 
                                          FontWeight="Bold"
                                          Foreground="White"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                                <TextBlock Text="{Binding ReportPeriod}" 
                                          FontSize="14"
                                          Foreground="White"
                                          HorizontalAlignment="Center"
                                          Margin="0,5,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- Section Revenus -->
                        <Border Padding="30,20" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,0,1">
                            <StackPanel>
                                <TextBlock Text="REVENUS" 
                                          FontSize="16" 
                                          FontWeight="Bold"
                                          Foreground="{DynamicResource PrimaryHueMidBrush}"
                                          Margin="0,0,0,15"/>

                                <ItemsControl ItemsSource="{Binding RevenueItems}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Margin="0,0,0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="150"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <TextBlock Grid.Column="0" 
                                                          Text="{Binding Description}" 
                                                          VerticalAlignment="Center"
                                                          Margin="20,0,0,0"/>
                                                
                                                <TextBlock Grid.Column="1" 
                                                          Text="{Binding Amount, StringFormat='{}{0:F2} DA'}" 
                                                          FontWeight="Bold"
                                                          TextAlignment="Right"
                                                          VerticalAlignment="Center"/>
                                            </Grid>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <Separator Margin="0,10"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="150"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" 
                                              Text="TOTAL REVENUS" 
                                              FontWeight="Bold"
                                              FontSize="14"/>
                                    
                                    <TextBlock Grid.Column="1" 
                                              Text="{Binding TotalRevenue, StringFormat='{}{0:F2} DA'}" 
                                              FontWeight="Bold"
                                              FontSize="14"
                                              Foreground="Green"
                                              TextAlignment="Right"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Section Coût des ventes -->
                        <Border Padding="30,20" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,0,1">
                            <StackPanel>
                                <TextBlock Text="COÛT DES VENTES" 
                                          FontSize="16" 
                                          FontWeight="Bold"
                                          Foreground="{DynamicResource PrimaryHueMidBrush}"
                                          Margin="0,0,0,15"/>

                                <ItemsControl ItemsSource="{Binding CostOfSalesItems}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Margin="0,0,0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="150"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <TextBlock Grid.Column="0" 
                                                          Text="{Binding Description}" 
                                                          VerticalAlignment="Center"
                                                          Margin="20,0,0,0"/>
                                                
                                                <TextBlock Grid.Column="1" 
                                                          Text="{Binding Amount, StringFormat='{}{0:F2} DA'}" 
                                                          FontWeight="Bold"
                                                          TextAlignment="Right"
                                                          VerticalAlignment="Center"/>
                                            </Grid>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <Separator Margin="0,10"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="150"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" 
                                              Text="TOTAL COÛT DES VENTES" 
                                              FontWeight="Bold"
                                              FontSize="14"/>
                                    
                                    <TextBlock Grid.Column="1" 
                                              Text="{Binding TotalCostOfSales, StringFormat='{}{0:F2} DA'}" 
                                              FontWeight="Bold"
                                              FontSize="14"
                                              Foreground="Red"
                                              TextAlignment="Right"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Marge brute -->
                        <Border Padding="30,15" Background="{DynamicResource MaterialDesignCardBackground}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="150"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" 
                                          Text="MARGE BRUTE" 
                                          FontWeight="Bold"
                                          FontSize="16"
                                          Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                                
                                <TextBlock Grid.Column="1" 
                                          Text="{Binding GrossMargin, StringFormat='{}{0:F2} DA'}" 
                                          FontWeight="Bold"
                                          FontSize="16"
                                          Foreground="{Binding GrossMargin, Converter={StaticResource ProfitToColorConverter}}"
                                          TextAlignment="Right"/>
                            </Grid>
                        </Border>

                        <!-- Section Charges d'exploitation -->
                        <Border Padding="30,20" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,0,0,1">
                            <StackPanel>
                                <TextBlock Text="CHARGES D'EXPLOITATION" 
                                          FontSize="16" 
                                          FontWeight="Bold"
                                          Foreground="{DynamicResource PrimaryHueMidBrush}"
                                          Margin="0,0,0,15"/>

                                <ItemsControl ItemsSource="{Binding OperatingExpenseItems}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Grid Margin="0,0,0,8">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="150"/>
                                                </Grid.ColumnDefinitions>
                                                
                                                <TextBlock Grid.Column="0" 
                                                          Text="{Binding Description}" 
                                                          VerticalAlignment="Center"
                                                          Margin="20,0,0,0"/>
                                                
                                                <TextBlock Grid.Column="1" 
                                                          Text="{Binding Amount, StringFormat='{}{0:F2} DA'}" 
                                                          FontWeight="Bold"
                                                          TextAlignment="Right"
                                                          VerticalAlignment="Center"/>
                                            </Grid>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <Separator Margin="0,10"/>
                                
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="150"/>
                                    </Grid.ColumnDefinitions>
                                    
                                    <TextBlock Grid.Column="0" 
                                              Text="TOTAL CHARGES D'EXPLOITATION" 
                                              FontWeight="Bold"
                                              FontSize="14"/>
                                    
                                    <TextBlock Grid.Column="1" 
                                              Text="{Binding TotalOperatingExpenses, StringFormat='{}{0:F2} DA'}" 
                                              FontWeight="Bold"
                                              FontSize="14"
                                              Foreground="Red"
                                              TextAlignment="Right"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Résultat net -->
                        <Border Padding="30,20" Background="{DynamicResource PrimaryHueLightBrush}">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="150"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" 
                                          Text="RÉSULTAT NET" 
                                          FontWeight="Bold"
                                          FontSize="18"
                                          Foreground="White"/>
                                
                                <TextBlock Grid.Column="1" 
                                          Text="{Binding NetResult, StringFormat='{}{0:F2} DA'}" 
                                          FontWeight="Bold"
                                          FontSize="18"
                                          Foreground="White"
                                          TextAlignment="Right"/>
                            </Grid>
                        </Border>

                        <!-- Pied de rapport -->
                        <Border Padding="30,15" BorderBrush="{DynamicResource MaterialDesignDivider}" BorderThickness="0,1,0,0">
                            <StackPanel>
                                <TextBlock Text="{Binding GeneratedDate, StringFormat='Rapport généré le {0:dd/MM/yyyy à HH:mm}'}" 
                                          FontSize="12"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                          HorizontalAlignment="Center"/>
                                <TextBlock Text="{Binding GeneratedBy, StringFormat='Par: {0}'}" 
                                          FontSize="12"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                                          HorizontalAlignment="Center"
                                          Margin="0,2,0,0"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </materialDesign:Card>

            <!-- Panneau latéral - Analyses et graphiques -->
            <StackPanel Grid.Column="2">
                <!-- Indicateurs clés -->
                <materialDesign:Card Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Indicateurs Clés" 
                                  FontWeight="Bold" 
                                  FontSize="14"
                                  Margin="0,0,0,15"/>
                        
                        <!-- Marge brute % -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="Marge brute" FontSize="12"/>
                            <TextBlock Grid.Column="1" 
                                      Text="{Binding GrossMarginPercentage, StringFormat='{}{0:F1}%'}" 
                                      FontWeight="Bold"
                                      Foreground="{Binding GrossMarginPercentage, Converter={StaticResource PercentageToColorConverter}}"/>
                        </Grid>
                        
                        <!-- Marge nette % -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="Marge nette" FontSize="12"/>
                            <TextBlock Grid.Column="1" 
                                      Text="{Binding NetMarginPercentage, StringFormat='{}{0:F1}%'}" 
                                      FontWeight="Bold"
                                      Foreground="{Binding NetMarginPercentage, Converter={StaticResource PercentageToColorConverter}}"/>
                        </Grid>
                        
                        <!-- ROI -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="ROI" FontSize="12"/>
                            <TextBlock Grid.Column="1" 
                                      Text="{Binding ROI, StringFormat='{}{0:F1}%'}" 
                                      FontWeight="Bold"
                                      Foreground="{Binding ROI, Converter={StaticResource PercentageToColorConverter}}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Comparaison périodes -->
                <materialDesign:Card Padding="15" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Comparaison" 
                                  FontWeight="Bold" 
                                  FontSize="14"
                                  Margin="0,0,0,15"/>
                        
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="vs Période précédente" FontSize="12"/>
                            <TextBlock Grid.Column="1" 
                                      Text="{Binding PreviousPeriodComparison, StringFormat='{}{0:+0.0;-0.0}%'}" 
                                      FontWeight="Bold"
                                      Foreground="{Binding PreviousPeriodComparison, Converter={StaticResource PercentageToColorConverter}}"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="vs Même période année dernière" FontSize="12"/>
                            <TextBlock Grid.Column="1" 
                                      Text="{Binding YearOverYearComparison, StringFormat='{}{0:+0.0;-0.0}%'}" 
                                      FontWeight="Bold"
                                      Foreground="{Binding YearOverYearComparison, Converter={StaticResource PercentageToColorConverter}}"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Actions rapides -->
                <materialDesign:Card Padding="15">
                    <StackPanel>
                        <TextBlock Text="Actions" 
                                  FontWeight="Bold" 
                                  FontSize="14"
                                  Margin="0,0,0,15"/>
                        
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding ComparePeriodsCommand}"
                               Margin="0,0,0,10">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Compare" VerticalAlignment="Center"/>
                                <TextBlock Text="Comparer" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding DrillDownCommand}"
                               Margin="0,0,0,10">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartBox" VerticalAlignment="Center"/>
                                <TextBlock Text="Détails" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding ScheduleReportCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="CalendarClock" VerticalAlignment="Center"/>
                                <TextBlock Text="Programmer" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>

        <!-- Overlay de chargement -->
        <Grid Background="#80000000"
              Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center"
                       VerticalAlignment="Center">
                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Value="0"
                           IsIndeterminate="True"
                           Width="50"
                           Height="50"
                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="{Binding BusyMessage}"
                          FontSize="14"
                          Foreground="White"
                          HorizontalAlignment="Center"
                          Margin="0,10,0,0"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
