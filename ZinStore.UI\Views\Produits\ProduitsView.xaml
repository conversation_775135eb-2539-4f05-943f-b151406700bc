<UserControl x:Class="ZinStore.UI.Views.ProduitsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">

    <UserControl.DataContext>
        <viewModels:ProduitsViewModel />
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0"
                  Text="Gestion des Produits"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Margin="0,0,0,20"
                  Foreground="{DynamicResource PrimaryHueMidBrush}"/>

        <!-- <PERSON><PERSON> d'outils -->
        <materialDesign:Card Grid.Row="1"
                           Margin="0,0,0,20"
                           Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche -->
                <TextBox Grid.Column="0"
                        materialDesign:HintAssist.Hint="Rechercher un produit (nom, code, catégorie)..."
                        materialDesign:HintAssist.IsFloating="True"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        Width="400"
                        Height="56"
                        FontSize="14"
                        VerticalContentAlignment="Center"
                        HorizontalAlignment="Left">
                    <TextBox.InputBindings>
                        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                    </TextBox.InputBindings>
                </TextBox>

                <!-- Boutons -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal"
                           VerticalAlignment="Center">
                    <Button Content="➕ Nouveau Produit"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddProduitCommand}"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Foreground="White"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="✏️ Modifier"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding EditProduitCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="🗑️ Supprimer"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding DeleteProduitCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="🔄 Actualiser"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding RefreshCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="📷 Scanner"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding ScanBarcodeCommand}"
                           Background="#4CAF50"
                           Foreground="White"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"
                           ToolTip="Scanner un code-barres pour rechercher un produit"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des produits -->
        <materialDesign:Card Grid.Row="2"
                           Padding="0"
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- En-tête -->
                <Border Grid.Row="0"
                       Background="{DynamicResource MaterialDesignToolBarBackground}"
                       Padding="15,10">
                    <TextBlock Text="Liste des Produits"
                              Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                              FontWeight="Bold"/>
                </Border>

                <!-- DataGrid avec états -->
                <Grid Grid.Row="1">
                    <!-- DataGrid principal -->
                    <DataGrid ItemsSource="{Binding Produits}"
                             SelectedItem="{Binding SelectedProduit}"
                             Style="{StaticResource MaterialDesignDataGrid}"
                             AutoGenerateColumns="False"
                             CanUserAddRows="False"
                             CanUserDeleteRows="False"
                             IsReadOnly="True"
                             SelectionMode="Single"
                             GridLinesVisibility="Horizontal"
                             HeadersVisibility="Column"
                             AlternatingRowBackground="{DynamicResource MaterialDesignDivider}"
                             Margin="15"
                             Visibility="{Binding IsBusy, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Code Produit"
                                          Binding="{Binding CodeProduit}"
                                          Width="120"
                                          FontWeight="Bold">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Nom du Produit"
                                          Binding="{Binding Nom}"
                                          Width="250">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Catégorie"
                                          Binding="{Binding NomCategorie}"
                                          Width="140">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Prix Achat"
                                          Binding="{Binding PrixAchat, StringFormat='{}{0:N2} DA'}"
                                          Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="#FF2196F3"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Prix Vente"
                                          Binding="{Binding PrixVente, StringFormat='{}{0:N2} DA'}"
                                          Width="120">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="#FF4CAF50"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Stock Actuel"
                                          Binding="{Binding StockActuel}"
                                          Width="100">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="Stock Min."
                                          Binding="{Binding StockMinimum}"
                                          Width="90">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Padding" Value="8"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridCheckBoxColumn Header="Actif"
                                              Binding="{Binding EstActif}"
                                              Width="70">
                            <DataGridCheckBoxColumn.ElementStyle>
                                <Style TargetType="CheckBox">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="IsEnabled" Value="False"/>
                                </Style>
                            </DataGridCheckBoxColumn.ElementStyle>
                        </DataGridCheckBoxColumn>
                    </DataGrid.Columns>
                    </DataGrid>

                    <!-- Indicateur de chargement -->
                    <Grid Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Margin="50">
                            <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                       IsIndeterminate="True"
                                       Width="60"
                                       Height="60"
                                       Margin="0,0,0,20"/>
                            <TextBlock Text="Chargement des produits..."
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                                      HorizontalAlignment="Center"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Grid>

                    <!-- Message liste vide -->
                    <StackPanel HorizontalAlignment="Center"
                               VerticalAlignment="Center"
                               Margin="50"
                               Visibility="{Binding IsEmpty, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <materialDesign:PackIcon Kind="PackageVariant"
                                               Width="80"
                                               Height="80"
                                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                                               HorizontalAlignment="Center"/>
                        <TextBlock Text="Aucun produit trouvé"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  HorizontalAlignment="Center"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  Margin="0,20,0,10"/>
                        <TextBlock Text="Ajoutez des produits ou modifiez vos critères de recherche"
                                  Style="{StaticResource MaterialDesignBody2TextBlock}"
                                  HorizontalAlignment="Center"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
