<UserControl x:Class="ZinStore.UI.Views.ProduitsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">

    <UserControl.DataContext>
        <viewModels:ProduitsViewModel />
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0"
                  Text="Gestion des Produits"
                  Style="{StaticResource TitleText}"/>

        <!-- Barre d'outils -->
        <materialDesign:Card Grid.Row="1"
                           Margin="0,0,0,20"
                           Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche -->
                <TextBox Grid.Column="0"
                        materialDesign:HintAssist.Hint="Rechercher un produit..."
                        Style="{StaticResource CustomTextBox}"
                        Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                        Width="300"
                        HorizontalAlignment="Left"/>

                <!-- Boutons -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal">
                    <Button Content="Nouveau Produit"
                           Style="{StaticResource PrimaryButton}"
                           Command="{Binding AddProduitCommand}"/>
                    <Button Content="Modifier"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding EditProduitCommand}"/>
                    <Button Content="Supprimer"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding DeleteProduitCommand}"/>
                    <Button Content="Actualiser"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding RefreshCommand}"/>
                    <Button Content="📷 Scanner"
                           Style="{StaticResource PrimaryButton}"
                           Command="{Binding ScanBarcodeCommand}"
                           Background="#FF4CAF50"
                           ToolTip="Scanner un code-barres pour rechercher un produit"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des produits -->
        <materialDesign:Card Grid.Row="2"
                           Padding="15">
            <DataGrid ItemsSource="{Binding Produits}"
                     SelectedItem="{Binding SelectedProduit}"
                     Style="{StaticResource CustomDataGrid}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Code"
                                      Binding="{Binding CodeProduit}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Nom"
                                      Binding="{Binding Nom}"
                                      Width="200"/>
                    <DataGridTextColumn Header="Catégorie"
                                      Binding="{Binding NomCategorie}"
                                      Width="120"/>
                    <DataGridTextColumn Header="Prix Achat"
                                      Binding="{Binding PrixAchat, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Prix Vente"
                                      Binding="{Binding PrixVente, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Stock"
                                      Binding="{Binding StockActuel}"
                                      Width="80"/>
                    <DataGridTextColumn Header="Stock Min"
                                      Binding="{Binding StockMinimum}"
                                      Width="80"/>
                    <DataGridCheckBoxColumn Header="Actif"
                                          Binding="{Binding EstActif}"
                                          Width="60"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
