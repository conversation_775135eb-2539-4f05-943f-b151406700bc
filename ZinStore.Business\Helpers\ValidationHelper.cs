using System;
using System.Text.RegularExpressions;

namespace ZinStore.Business.Helpers
{
    /// <summary>
    /// Classe d'aide pour la validation des données
    /// </summary>
    public static class ValidationHelper
    {
        /// <summary>
        /// Valide une adresse email
        /// </summary>
        public static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var regex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return regex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Valide un numéro de téléphone
        /// </summary>
        public static bool IsValidPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return false;

            // Accepte les formats: +213XXXXXXXXX, 0XXXXXXXXX, XXXXXXXXX
            var regex = new Regex(@"^(\+213|0)?[5-7][0-9]{8}$");
            return regex.IsMatch(phoneNumber.Replace(" ", "").Replace("-", ""));
        }

        /// <summary>
        /// Valide un mot de passe
        /// </summary>
        public static bool IsValidPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return false;

            // Au moins 6 caractères
            return password.Length >= 6;
        }

        /// <summary>
        /// Valide un code postal algérien
        /// </summary>
        public static bool IsValidAlgerianPostalCode(string postalCode)
        {
            if (string.IsNullOrWhiteSpace(postalCode))
                return false;

            // Code postal algérien: 5 chiffres
            var regex = new Regex(@"^[0-9]{5}$");
            return regex.IsMatch(postalCode);
        }

        /// <summary>
        /// Valide un numéro d'identification fiscale algérien (NIF)
        /// </summary>
        public static bool IsValidAlgerianNIF(string nif)
        {
            if (string.IsNullOrWhiteSpace(nif))
                return false;

            // NIF algérien: 15 chiffres
            var regex = new Regex(@"^[0-9]{15}$");
            return regex.IsMatch(nif);
        }

        /// <summary>
        /// Valide un numéro de registre de commerce algérien
        /// </summary>
        public static bool IsValidAlgerianRC(string rc)
        {
            if (string.IsNullOrWhiteSpace(rc))
                return false;

            // RC algérien: format variable mais généralement des chiffres et lettres
            var regex = new Regex(@"^[0-9A-Za-z\-/]{5,20}$");
            return regex.IsMatch(rc);
        }

        /// <summary>
        /// Valide un montant (doit être positif)
        /// </summary>
        public static bool IsValidAmount(decimal amount)
        {
            return amount >= 0;
        }

        /// <summary>
        /// Valide une quantité (doit être positive)
        /// </summary>
        public static bool IsValidQuantity(decimal quantity)
        {
            return quantity > 0;
        }

        /// <summary>
        /// Valide un pourcentage (entre 0 et 100)
        /// </summary>
        public static bool IsValidPercentage(decimal percentage)
        {
            return percentage >= 0 && percentage <= 100;
        }

        /// <summary>
        /// Valide une date (ne doit pas être dans le futur pour certains cas)
        /// </summary>
        public static bool IsValidDate(DateTime date, bool allowFuture = true)
        {
            if (!allowFuture && date > DateTime.Now)
                return false;

            return date >= new DateTime(1900, 1, 1) && date <= new DateTime(2100, 12, 31);
        }

        /// <summary>
        /// Nettoie et valide un code (supprime les espaces et caractères spéciaux)
        /// </summary>
        public static string CleanCode(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return string.Empty;

            return code.Trim().ToUpper().Replace(" ", "");
        }

        /// <summary>
        /// Valide un code produit/client/fournisseur
        /// </summary>
        public static bool IsValidCode(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            // Accepte lettres, chiffres et tirets
            var regex = new Regex(@"^[A-Za-z0-9\-]{1,20}$");
            return regex.IsMatch(code);
        }
    }
}
