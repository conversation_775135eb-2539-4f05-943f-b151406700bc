# Guide Utilisateur - ZinStore

## 📚 Table des Matières

1. [Démarrage Rapide](#démarrage-rapide)
2. [Interface Principale](#interface-principale)
3. [Gestion des Clients](#gestion-des-clients)
4. [Gestion des Fournisseurs](#gestion-des-fournisseurs)
5. [Gestion des Produits](#gestion-des-produits)
6. [Point de Vente](#point-de-vente)
7. [Gestion des Achats](#gestion-des-achats)
8. [Gestion du Stock](#gestion-du-stock)
9. [Comptabilité](#comptabilité)
10. [Rapports](#rapports)
11. [Administration](#administration)
12. [FAQ](#faq)

## 🚀 Démarrage Rapide

### Première Connexion
1. **Lancez ZinStore** depuis le menu Démarrer ou le raccourci bureau
2. **Connectez-vous** avec les identifiants par défaut :
   - Utilisateur : `admin`
   - Mot de passe : `admin123`
3. **Changez immédiatement** le mot de passe par défaut
4. **Configurez** les informations de votre entreprise

### Configuration Initiale
1. **Accédez aux Paramètres** (icône engrenage)
2. **Renseignez les informations de votre société** :
   - Nom de la société
   - Adresse complète
   - Numéros de téléphone/fax
   - Email et site web
   - Numéros d'identification fiscale
3. **Configurez la TVA** (19% par défaut en Algérie)
4. **Définissez la devise** (Dinar algérien - DZD)

## 🖥️ Interface Principale

### Barre de Menu
- **Tableau de Bord** : Vue d'ensemble des activités
- **Clients** : Gestion du fichier clients
- **Fournisseurs** : Gestion du fichier fournisseurs
- **Produits** : Catalogue et gestion des produits
- **Ventes** : Point de vente et historique
- **Achats** : Commandes et réceptions
- **Stock** : Inventaire et mouvements
- **Rapports** : Analyses et statistiques

### Tableau de Bord
Le tableau de bord affiche :
- **Chiffre d'affaires du jour**
- **Nombre de clients actifs**
- **Produits en stock**
- **Alertes de rupture de stock**
- **Ventes récentes**
- **Actions rapides**

## 👥 Gestion des Clients

### Ajouter un Client
1. **Cliquez sur "Clients"** dans le menu principal
2. **Cliquez sur "Nouveau Client"**
3. **Remplissez les informations** :
   - Code client (généré automatiquement)
   - Nom et prénom / Raison sociale
   - Adresse complète
   - Téléphone et email
   - Limite de crédit
4. **Sauvegardez**

### Rechercher un Client
- **Utilisez la barre de recherche** en haut de la liste
- **Recherche par** : nom, code, téléphone, email
- **Filtres disponibles** : actifs/inactifs, par ville

### Modifier un Client
1. **Sélectionnez le client** dans la liste
2. **Cliquez sur "Modifier"**
3. **Modifiez les informations** nécessaires
4. **Sauvegardez les changements**

### Compte Client
- **Solde du compte** : crédit/débit actuel
- **Historique des transactions**
- **Factures en attente**
- **Limite de crédit** et alertes

## 🏭 Gestion des Fournisseurs

### Ajouter un Fournisseur
1. **Accédez au module Fournisseurs**
2. **Cliquez sur "Nouveau Fournisseur"**
3. **Saisissez les informations** :
   - Code fournisseur
   - Nom/Raison sociale
   - Coordonnées complètes
   - Conditions commerciales
   - Délais de paiement
4. **Validez la création**

### Conditions Commerciales
- **Délai de paiement** (30, 60, 90 jours)
- **Remise habituelle** en pourcentage
- **Conditions particulières**
- **Personne de contact**

## 📦 Gestion des Produits

### Créer un Produit
1. **Module Produits > Nouveau Produit**
2. **Informations de base** :
   - Code produit (auto-généré)
   - Code-barres
   - Nom et description
   - Catégorie
3. **Prix et coûts** :
   - Prix d'achat
   - Prix de vente
   - Prix de vente en gros
   - Taux de TVA
4. **Gestion du stock** :
   - Stock minimum/maximum
   - Unité de mesure
   - Emplacement

### Catégories de Produits
- **Créez des catégories** pour organiser vos produits
- **Hiérarchie** : catégories et sous-catégories
- **Filtrage** et recherche par catégorie

### Images et Détails
- **Ajoutez des images** pour vos produits
- **Dimensions et poids**
- **Produits périssables** avec durée de conservation
- **Notes** et informations complémentaires

## 💰 Point de Vente

### Nouvelle Vente
1. **Module Ventes > Nouvelle Vente**
2. **Sélectionnez le client** (optionnel pour vente comptant)
3. **Ajoutez des produits** :
   - Recherche par nom/code/code-barres
   - Saisie de la quantité
   - Application de remises
4. **Finalisez la vente** :
   - Vérifiez le total
   - Choisissez le mode de paiement
   - Imprimez la facture

### Modes de Paiement
- **Espèces**
- **Chèque**
- **Carte bancaire**
- **Crédit** (pour clients avec limite)
- **Mixte** (plusieurs modes)

### Remises et Promotions
- **Remise par ligne** (produit)
- **Remise globale** sur la facture
- **Remise en pourcentage** ou montant fixe

## 🛒 Gestion des Achats

### Bon de Commande
1. **Créez un bon de commande**
2. **Sélectionnez le fournisseur**
3. **Ajoutez les produits** à commander
4. **Envoyez au fournisseur**

### Réception de Marchandises
1. **Réception partielle** ou complète
2. **Contrôle qualité**
3. **Mise à jour automatique** du stock
4. **Génération des écritures** comptables

### Factures Fournisseurs
- **Saisie des factures** reçues
- **Rapprochement** avec les commandes
- **Gestion des échéances**
- **Suivi des paiements**

## 📊 Gestion du Stock

### Inventaire
- **Stock en temps réel** par produit
- **Valorisation** du stock (FIFO, LIFO, CMP)
- **Mouvements de stock** détaillés
- **Historique** des variations

### Alertes de Stock
- **Rupture de stock** (quantité = 0)
- **Stock minimum** atteint
- **Surstock** (quantité > maximum)
- **Produits périmés** ou à péremption proche

### Ajustements de Stock
- **Inventaire physique**
- **Corrections** d'écart
- **Pertes et casses**
- **Transferts** entre emplacements

## 💼 Comptabilité

### Plan Comptable
- **Comptes de base** pré-configurés
- **Personnalisation** selon vos besoins
- **Hiérarchie** des comptes
- **Comptes clients/fournisseurs**

### Écritures Automatiques
- **Ventes** : débit client, crédit vente
- **Achats** : débit achat, crédit fournisseur
- **Paiements** : mouvements de trésorerie
- **Stock** : variations de stock

### États Financiers
- **Balance** des comptes
- **Compte de résultat**
- **Bilan** comptable
- **Tableau de trésorerie**

## 📈 Rapports

### Rapports de Vente
- **Chiffre d'affaires** par période
- **Top des ventes** (produits/clients)
- **Évolution** des ventes
- **Marges** par produit/catégorie

### Rapports de Stock
- **État du stock** à une date
- **Mouvements** de stock
- **Valorisation** du stock
- **Produits en rupture**

### Rapports Clients
- **Situation** des comptes clients
- **Créances** en cours
- **Historique** client
- **Clients inactifs**

### Export et Impression
- **Format PDF** pour impression
- **Export Excel** pour analyse
- **Envoi par email**
- **Programmation** de rapports automatiques

## ⚙️ Administration

### Gestion des Utilisateurs
- **Créer des utilisateurs**
- **Définir les rôles** :
  - Administrateur : tous droits
  - Gestionnaire : gestion commerciale
  - Caissier : ventes uniquement
  - Vendeur : ventes et clients
- **Permissions détaillées** par module

### Sauvegarde et Restauration
- **Sauvegarde automatique** quotidienne
- **Sauvegarde manuelle** à la demande
- **Restauration** depuis une sauvegarde
- **Export** des données

### Paramètres Système
- **Configuration générale**
- **Numérotation** des documents
- **Préférences d'affichage**
- **Paramètres d'impression**

## ❓ FAQ

### Questions Fréquentes

**Q : Comment changer le mot de passe ?**
R : Menu utilisateur > Changer le mot de passe

**Q : Comment imprimer une facture ?**
R : Sélectionnez la vente > Bouton Imprimer

**Q : Comment faire une sauvegarde ?**
R : Paramètres > Sauvegarde > Sauvegarder maintenant

**Q : Comment ajouter un nouveau produit rapidement ?**
R : Tableau de bord > Actions rapides > Nouveau produit

**Q : Comment voir les produits en rupture ?**
R : Stock > Filtrer par "En rupture" ou Tableau de bord

**Q : Comment modifier le taux de TVA ?**
R : Paramètres > Configuration > Taux TVA par défaut

### Résolution de Problèmes

**Problème : L'application est lente**
- Vérifiez l'espace disque disponible
- Optimisez la base de données (Outils > Optimiser)
- Fermez les autres applications

**Problème : Erreur lors de l'impression**
- Vérifiez que l'imprimante est connectée
- Mettez à jour les pilotes d'imprimante
- Testez avec une autre imprimante

**Problème : Données manquantes**
- Restaurez depuis la dernière sauvegarde
- Vérifiez les permissions du dossier Data
- Contactez le support technique

## 📞 Support

**Email** : <EMAIL>  
**Téléphone** : +213 XXX XXX XXX  
**Heures** : Dimanche à Jeudi, 8h-17h  

---

**ZinStore** - Votre solution complète de gestion 🇩🇿
