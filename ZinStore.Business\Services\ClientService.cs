using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;
using ZinStore.Business.Helpers;

namespace ZinStore.Business.Services
{
    /// <summary>
    /// Service de gestion des clients
    /// </summary>
    public class ClientService
    {
        private readonly ClientRepository _clientRepository;

        public ClientService(DatabaseContext context)
        {
            _clientRepository = new ClientRepository(context);
        }

        /// <summary>
        /// Obtient tous les clients
        /// </summary>
        public async Task<IEnumerable<Client>> GetAllClientsAsync()
        {
            return await _clientRepository.GetAllAsync();
        }

        /// <summary>
        /// Obtient un client par son ID
        /// </summary>
        public async Task<Client> GetClientByIdAsync(int id)
        {
            return await _clientRepository.GetByIdAsync(id);
        }

        /// <summary>
        /// Obtient un client par son code
        /// </summary>
        public async Task<Client> GetClientByCodeAsync(string code)
        {
            return await _clientRepository.GetByCodeAsync(code);
        }

        /// <summary>
        /// Recherche des clients
        /// </summary>
        public async Task<IEnumerable<Client>> SearchClientsAsync(string searchTerm)
        {
            return await _clientRepository.SearchAsync(searchTerm);
        }

        /// <summary>
        /// Ajoute un nouveau client
        /// </summary>
        public async Task<(bool Success, string Message, int ClientId)> AddClientAsync(Client client)
        {
            try
            {
                // Validation des données
                var validationResult = ValidateClient(client);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage, 0);
                }

                // Vérifier si le code client existe déjà
                if (await _clientRepository.CodeExistsAsync(client.CodeClient))
                {
                    return (false, "Ce code client existe déjà.", 0);
                }

                // Nettoyer les données
                CleanClientData(client);

                // Ajouter le client
                int clientId = await _clientRepository.AddAsync(client);
                return (true, "Client ajouté avec succès.", clientId);
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de l'ajout du client: {ex.Message}", 0);
            }
        }

        /// <summary>
        /// Met à jour un client existant
        /// </summary>
        public async Task<(bool Success, string Message)> UpdateClientAsync(Client client)
        {
            try
            {
                // Validation des données
                var validationResult = ValidateClient(client);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage);
                }

                // Vérifier si le code client existe déjà (excluant le client actuel)
                if (await _clientRepository.CodeExistsAsync(client.CodeClient, client.Id))
                {
                    return (false, "Ce code client existe déjà.");
                }

                // Nettoyer les données
                CleanClientData(client);

                // Mettre à jour le client
                bool success = await _clientRepository.UpdateAsync(client);
                return success ? (true, "Client mis à jour avec succès.") : (false, "Erreur lors de la mise à jour du client.");
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de la mise à jour du client: {ex.Message}");
            }
        }

        /// <summary>
        /// Supprime un client
        /// </summary>
        public async Task<(bool Success, string Message)> DeleteClientAsync(int clientId)
        {
            try
            {
                // Vérifier si le client existe
                var client = await _clientRepository.GetByIdAsync(clientId);
                if (client == null)
                {
                    return (false, "Client introuvable.");
                }

                // TODO: Vérifier si le client a des ventes en cours
                // if (await HasActiveTransactions(clientId))
                // {
                //     return (false, "Impossible de supprimer ce client car il a des transactions en cours.");
                // }

                // Supprimer le client (suppression logique)
                bool success = await _clientRepository.DeleteAsync(clientId);
                return success ? (true, "Client supprimé avec succès.") : (false, "Erreur lors de la suppression du client.");
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de la suppression du client: {ex.Message}");
            }
        }

        /// <summary>
        /// Génère le prochain code client
        /// </summary>
        public async Task<string> GenerateNextCodeAsync()
        {
            return await _clientRepository.GenerateNextCodeAsync("CL");
        }

        /// <summary>
        /// Obtient les clients actifs
        /// </summary>
        public async Task<IEnumerable<Client>> GetActiveClientsAsync()
        {
            return await _clientRepository.GetActiveClientsAsync();
        }

        /// <summary>
        /// Obtient les clients avec un solde débiteur
        /// </summary>
        public async Task<IEnumerable<Client>> GetClientsWithDebitAsync()
        {
            return await _clientRepository.GetClientsWithDebitAsync();
        }

        /// <summary>
        /// Valide les données d'un client
        /// </summary>
        private (bool IsValid, string ErrorMessage) ValidateClient(Client client)
        {
            if (client == null)
                return (false, "Les données du client sont requises.");

            if (string.IsNullOrWhiteSpace(client.CodeClient))
                return (false, "Le code client est obligatoire.");

            if (string.IsNullOrWhiteSpace(client.Nom))
                return (false, "Le nom du client est obligatoire.");

            if (!ValidationHelper.IsValidCode(client.CodeClient))
                return (false, "Le code client contient des caractères non valides.");

            if (!string.IsNullOrWhiteSpace(client.Email) && !ValidationHelper.IsValidEmail(client.Email))
                return (false, "L'adresse email n'est pas valide.");

            if (!string.IsNullOrWhiteSpace(client.Telephone) && !ValidationHelper.IsValidPhoneNumber(client.Telephone))
                return (false, "Le numéro de téléphone n'est pas valide.");

            if (!string.IsNullOrWhiteSpace(client.Mobile) && !ValidationHelper.IsValidPhoneNumber(client.Mobile))
                return (false, "Le numéro de mobile n'est pas valide.");

            if (!ValidationHelper.IsValidAmount(client.LimiteCredit))
                return (false, "La limite de crédit doit être positive.");

            return (true, string.Empty);
        }

        /// <summary>
        /// Nettoie les données d'un client
        /// </summary>
        private void CleanClientData(Client client)
        {
            client.CodeClient = ValidationHelper.CleanCode(client.CodeClient);
            client.Nom = client.Nom?.Trim();
            client.Prenom = client.Prenom?.Trim();
            client.RaisonSociale = client.RaisonSociale?.Trim();
            client.Email = client.Email?.Trim().ToLower();
            client.Telephone = client.Telephone?.Trim();
            client.Mobile = client.Mobile?.Trim();
        }
    }
}
