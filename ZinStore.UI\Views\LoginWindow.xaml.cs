using System.Windows;
using System.Windows.Controls;
using ZinStore.UI.ViewModels;

namespace ZinStore.UI.Views
{
    /// <summary>
    /// Logique d'interaction pour LoginWindow.xaml
    /// </summary>
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();

            // Permettre le déplacement de la fenêtre
            this.MouseLeftButtonDown += (sender, e) => this.DragMove();

            // Lier le mot de passe au ViewModel
            PasswordBox.PasswordChanged += (sender, e) =>
            {
                if (DataContext is LoginViewModel viewModel)
                {
                    viewModel.Password = ((PasswordBox)sender).Password;
                }
            };
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            // Mettre le focus sur le champ nom d'utilisateur
            Dispatcher.BeginInvoke(new System.Action(() =>
            {
                var textBox = FindName("UsernameTextBox") as TextBox;
                textBox?.Focus();
            }));
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

  
    }
}
