<UserControl x:Class="ZinStore.UI.Views.Dashboard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">

    <UserControl.DataContext>
        <viewModels:DashboardViewModel />
    </UserControl.DataContext>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Titre -->
            <TextBlock Grid.Row="0"
                      Text="📊 Tableau de Bord"
                      Style="{StaticResource MaterialDesignHeadline3TextBlock}"
                      Foreground="{DynamicResource PrimaryHueMidBrush}"
                      FontWeight="Bold"
                      Margin="0,0,0,30"/>

            <!-- Cartes de statistiques -->
            <UniformGrid Grid.Row="1"
                        Rows="1"
                        Columns="4"
                        Margin="0,0,0,30">

                <!-- Ventes du jour -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                                   Background="{DynamicResource PrimaryHueMidBrush}"
                                   Margin="5"
                                   Padding="20">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="CashMultiple"
                                                   Width="40"
                                                   Height="40"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                      Text="{Binding VentesAujourdhui, StringFormat='{}{0:C}'}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="💰 Ventes d'aujourd'hui"
                                  Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  Foreground="White"
                                  Opacity="0.9"
                                  FontWeight="Medium"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Nombre de clients -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                                   Background="#2196F3"
                                   Margin="5"
                                   Padding="20">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="AccountGroup"
                                                   Width="40"
                                                   Height="40"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                      Text="{Binding NombreClients}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="👥 Clients"
                                  Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  Foreground="White"
                                  Opacity="0.9"
                                  FontWeight="Medium"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Produits en stock -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                                   Background="#4CAF50"
                                   Margin="5"
                                   Padding="20">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="Package"
                                                   Width="40"
                                                   Height="40"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                      Text="{Binding NombreProduits}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="📦 Produits en stock"
                                  Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  Foreground="White"
                                  Opacity="0.9"
                                  FontWeight="Medium"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Alertes stock -->
                <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                                   Background="#FF5722"
                                   Margin="5"
                                   Padding="20">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="AlertCircle"
                                                   Width="40"
                                                   Height="40"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                      Text="{Binding ProduitsEnRupture}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="⚠️ Alertes stock"
                                  Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  Foreground="White"
                                  Opacity="0.9"
                                  FontWeight="Medium"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Actions rapides -->
            <materialDesign:Card Grid.Row="2"
                               materialDesign:ElevationAssist.Elevation="Dp2"
                               Padding="25"
                               Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="🚀 Actions Rapides"
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              FontWeight="Bold"
                              Margin="0,0,0,20"/>

                    <UniformGrid Rows="2"
                               Columns="4"
                               Margin="0,10">
                        <Button Style="{StaticResource MaterialDesignRaisedButton}"
                               Command="{Binding NouvelleVenteCommand}"
                               Background="{DynamicResource PrimaryHueMidBrush}"
                               Foreground="White"
                               Height="60"
                               Margin="5">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="CashRegister"
                                                       Width="24"
                                                       Height="24"
                                                       Margin="0,0,0,5"/>
                                <TextBlock Text="Nouvelle Vente"
                                          FontWeight="Medium"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding NouvelAchatCommand}"
                               Height="60"
                               Margin="5">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="ShoppingCart"
                                                       Width="24"
                                                       Height="24"
                                                       Margin="0,0,0,5"/>
                                <TextBlock Text="Nouvel Achat"
                                          FontWeight="Medium"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding NouveauClientCommand}"
                               Height="60"
                               Margin="5">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="AccountPlus"
                                                       Width="24"
                                                       Height="24"
                                                       Margin="0,0,0,5"/>
                                <TextBlock Text="Nouveau Client"
                                          FontWeight="Medium"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding NouveauProduitCommand}"
                               Height="60"
                               Margin="5">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="PackageVariantClosed"
                                                       Width="24"
                                                       Height="24"
                                                       Margin="0,0,0,5"/>
                                <TextBlock Text="Nouveau Produit"
                                          FontWeight="Medium"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding VoirStockCommand}"
                               Height="60"
                               Margin="5">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Warehouse"
                                                       Width="24"
                                                       Height="24"
                                                       Margin="0,0,0,5"/>
                                <TextBlock Text="Voir Stock"
                                          FontWeight="Medium"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding VoirRapportsCommand}"
                               Height="60"
                               Margin="5">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="ChartLine"
                                                       Width="24"
                                                       Height="24"
                                                       Margin="0,0,0,5"/>
                                <TextBlock Text="Rapports"
                                          FontWeight="Medium"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding ParametresCommand}"
                               Height="60"
                               Margin="5">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="Settings"
                                                       Width="24"
                                                       Height="24"
                                                       Margin="0,0,0,5"/>
                                <TextBlock Text="Paramètres"
                                          FontWeight="Medium"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                               Command="{Binding SauvegardeCommand}"
                               Height="60"
                               Margin="5">
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                <materialDesign:PackIcon Kind="DatabaseExport"
                                                       Width="24"
                                                       Height="24"
                                                       Margin="0,0,0,5"/>
                                <TextBlock Text="Sauvegarde"
                                          FontWeight="Medium"
                                          HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Activités récentes -->
            <Grid Grid.Row="3"
                 Margin="0,20,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Ventes récentes -->
                <materialDesign:Card Grid.Column="0"
                                   materialDesign:ElevationAssist.Elevation="Dp2"
                                   Padding="20"
                                   Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="📈 Ventes Récentes"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                                  FontWeight="Bold"
                                  Margin="0,0,0,15"/>
                        <DataGrid ItemsSource="{Binding VentesRecentes}"
                                 Style="{StaticResource MaterialDesignDataGrid}"
                                 AutoGenerateColumns="False"
                                 IsReadOnly="True"
                                 MaxHeight="300"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="N° Facture"
                                                  Binding="{Binding NumeroFacture}"
                                                  Width="100"/>
                                <DataGridTextColumn Header="Client"
                                                  Binding="{Binding NomClient}"
                                                  Width="*"/>
                                <DataGridTextColumn Header="Montant"
                                                  Binding="{Binding MontantTotal, StringFormat='{}{0:C}'}"
                                                  Width="100"/>
                                <DataGridTextColumn Header="Date"
                                                  Binding="{Binding DateVente, StringFormat='dd/MM/yyyy'}"
                                                  Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Produits en rupture -->
                <materialDesign:Card Grid.Column="1"
                                   materialDesign:ElevationAssist.Elevation="Dp2"
                                   Padding="20"
                                   Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="⚠️ Produits en Rupture"
                                  Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                  Foreground="#FF5722"
                                  FontWeight="Bold"
                                  Margin="0,0,0,15"/>
                        <DataGrid ItemsSource="{Binding ProduitsEnRuptureList}"
                                 Style="{StaticResource MaterialDesignDataGrid}"
                                 AutoGenerateColumns="False"
                                 IsReadOnly="True"
                                 MaxHeight="300"
                                 GridLinesVisibility="Horizontal"
                                 HeadersVisibility="Column">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Code"
                                                  Binding="{Binding CodeProduit}"
                                                  Width="80"/>
                                <DataGridTextColumn Header="Nom"
                                                  Binding="{Binding Nom}"
                                                  Width="*"/>
                                <DataGridTextColumn Header="Stock"
                                                  Binding="{Binding StockActuel}"
                                                  Width="60"/>
                                <DataGridTextColumn Header="Min"
                                                  Binding="{Binding StockMinimum}"
                                                  Width="60"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Overlay de chargement -->
            <Grid Grid.RowSpan="4"
                  Background="#80000000"
                  Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center"
                           VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                               Value="0"
                               IsIndeterminate="True"
                               Width="50"
                               Height="50"
                               Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="{Binding BusyMessage}"
                              FontSize="14"
                              Foreground="White"
                              HorizontalAlignment="Center"
                              Margin="0,10,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
