<UserControl x:Class="ZinStore.UI.Views.Dashboard"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">

    <UserControl.DataContext>
        <viewModels:DashboardViewModel />
    </UserControl.DataContext>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Titre -->
            <TextBlock Grid.Row="0"
                      Text="Tableau de Bord"
                      Style="{StaticResource TitleText}"/>

            <!-- Cartes de statistiques -->
            <UniformGrid Grid.Row="1"
                        Rows="1"
                        Columns="4"
                        Margin="0,0,0,30">

                <!-- Ventes du jour -->
                <materialDesign:Card Style="{StaticResource CardStyle}"
                                   Background="{DynamicResource PrimaryHueLightBrush}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="CashMultiple"
                                                   Width="40"
                                                   Height="40"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                      Text="{Binding VentesAujourdhui, StringFormat='{}{0:C}'}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="Ventes d'aujourd'hui"
                                  FontSize="14"
                                  Foreground="White"
                                  Opacity="0.8"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Nombre de clients -->
                <materialDesign:Card Style="{StaticResource CardStyle}"
                                   Background="{DynamicResource SecondaryHueMidBrush}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="AccountGroup"
                                                   Width="40"
                                                   Height="40"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                      Text="{Binding NombreClients}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="Clients"
                                  FontSize="14"
                                  Foreground="White"
                                  Opacity="0.8"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Produits en stock -->
                <materialDesign:Card Style="{StaticResource CardStyle}"
                                   Background="#4CAF50">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="Package"
                                                   Width="40"
                                                   Height="40"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                      Text="{Binding NombreProduits}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="Produits en stock"
                                  FontSize="14"
                                  Foreground="White"
                                  Opacity="0.8"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Alertes stock -->
                <materialDesign:Card Style="{StaticResource CardStyle}"
                                   Background="#FF5722">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <materialDesign:PackIcon Grid.Column="0"
                                                   Kind="AlertCircle"
                                                   Width="40"
                                                   Height="40"
                                                   Foreground="White"
                                                   VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="1"
                                      Text="{Binding ProduitsEnRupture}"
                                      FontSize="24"
                                      FontWeight="Bold"
                                      Foreground="White"
                                      VerticalAlignment="Center"
                                      HorizontalAlignment="Right"/>
                        </Grid>
                        <TextBlock Text="Alertes stock"
                                  FontSize="14"
                                  Foreground="White"
                                  Opacity="0.8"
                                  Margin="0,10,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </UniformGrid>

            <!-- Actions rapides -->
            <materialDesign:Card Grid.Row="2"
                               Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="Actions Rapides"
                              Style="{StaticResource SubtitleText}"/>

                    <UniformGrid Rows="2"
                               Columns="4">
                        <Button Content="Nouvelle Vente"
                               Style="{StaticResource PrimaryButton}"
                               Command="{Binding NouvelleVenteCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Plus"
                                                               Width="16"
                                                               Height="16"
                                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="Nouvel Achat"
                               Style="{StaticResource SecondaryButton}"
                               Command="{Binding NouvelAchatCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ShoppingCart"
                                                               Width="16"
                                                               Height="16"
                                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="Nouveau Client"
                               Style="{StaticResource SecondaryButton}"
                               Command="{Binding NouveauClientCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="AccountPlus"
                                                               Width="16"
                                                               Height="16"
                                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="Nouveau Produit"
                               Style="{StaticResource SecondaryButton}"
                               Command="{Binding NouveauProduitCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="PackageVariantClosed"
                                                               Width="16"
                                                               Height="16"
                                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="Voir Stock"
                               Style="{StaticResource SecondaryButton}"
                               Command="{Binding VoirStockCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Warehouse"
                                                               Width="16"
                                                               Height="16"
                                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="Rapports"
                               Style="{StaticResource SecondaryButton}"
                               Command="{Binding VoirRapportsCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="ChartLine"
                                                               Width="16"
                                                               Height="16"
                                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="Paramètres"
                               Style="{StaticResource SecondaryButton}"
                               Command="{Binding ParametresCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="Settings"
                                                               Width="16"
                                                               Height="16"
                                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>

                        <Button Content="Sauvegarde"
                               Style="{StaticResource SecondaryButton}"
                               Command="{Binding SauvegardeCommand}">
                            <Button.ContentTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="DatabaseExport"
                                                               Width="16"
                                                               Height="16"
                                                               Margin="0,0,5,0"/>
                                        <TextBlock Text="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </Button.ContentTemplate>
                        </Button>
                    </UniformGrid>
                </StackPanel>
            </materialDesign:Card>

            <!-- Activités récentes -->
            <Grid Grid.Row="3"
                 Margin="0,20,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Ventes récentes -->
                <materialDesign:Card Grid.Column="0"
                                   Style="{StaticResource CardStyle}"
                                   Margin="0,0,10,0">
                    <StackPanel>
                        <TextBlock Text="Ventes Récentes"
                                  Style="{StaticResource SubtitleText}"/>
                        <DataGrid ItemsSource="{Binding VentesRecentes}"
                                 Style="{StaticResource CustomDataGrid}"
                                 MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="N° Facture"
                                                  Binding="{Binding NumeroFacture}"
                                                  Width="100"/>
                                <DataGridTextColumn Header="Client"
                                                  Binding="{Binding NomClient}"
                                                  Width="*"/>
                                <DataGridTextColumn Header="Montant"
                                                  Binding="{Binding MontantTotal, StringFormat='{}{0:C}'}"
                                                  Width="100"/>
                                <DataGridTextColumn Header="Date"
                                                  Binding="{Binding DateVente, StringFormat='dd/MM/yyyy'}"
                                                  Width="100"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Produits en rupture -->
                <materialDesign:Card Grid.Column="1"
                                   Style="{StaticResource CardStyle}"
                                   Margin="10,0,0,0">
                    <StackPanel>
                        <TextBlock Text="Produits en Rupture"
                                  Style="{StaticResource SubtitleText}"/>
                        <DataGrid ItemsSource="{Binding ProduitsEnRuptureList}"
                                 Style="{StaticResource CustomDataGrid}"
                                 MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Code"
                                                  Binding="{Binding CodeProduit}"
                                                  Width="80"/>
                                <DataGridTextColumn Header="Nom"
                                                  Binding="{Binding Nom}"
                                                  Width="*"/>
                                <DataGridTextColumn Header="Stock"
                                                  Binding="{Binding StockActuel}"
                                                  Width="60"/>
                                <DataGridTextColumn Header="Min"
                                                  Binding="{Binding StockMinimum}"
                                                  Width="60"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
