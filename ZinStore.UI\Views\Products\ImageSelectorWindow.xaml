<Window x:Class="ZinStore.UI.Views.Products.ImageSelectorWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Sélectionner des images" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Style="{StaticResource MaterialDesignWindow}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- En-tête -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15">
            <StackPanel>
                <TextBlock Text="Sélectionner des images pour le produit" 
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                          HorizontalAlignment="Center"/>
                <TextBlock Text="Vous pouvez sélectionner plusieurs images. La première sera l'image principale."
                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                          HorizontalAlignment="Center"
                          Margin="0,5,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Zone principale -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Zone de sélection -->
            <materialDesign:Card Grid.Column="0" Padding="15">
                <StackPanel>
                    <TextBlock Text="Ajouter des images" 
                              Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                              Margin="0,0,0,15"/>

                    <!-- Boutons d'ajout -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
                        <Button x:Name="BrowseButton"
                               Style="{StaticResource MaterialDesignRaisedButton}"
                               Click="BrowseButton_Click"
                               Margin="5">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FolderImage" Margin="0,0,5,0"/>
                                <TextBlock Text="Parcourir"/>
                            </StackPanel>
                        </Button>

                        <Button x:Name="CameraButton"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Click="CameraButton_Click"
                               Margin="5">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Camera" Margin="0,0,5,0"/>
                                <TextBlock Text="Caméra"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>

                    <!-- Zone de glisser-déposer -->
                    <Border x:Name="DropZone"
                           Height="200"
                           BorderBrush="{DynamicResource MaterialDesignDivider}"
                           BorderThickness="2"
                           Background="{DynamicResource MaterialDesignCardBackground}"
                           AllowDrop="True"
                           Drop="DropZone_Drop"
                           DragOver="DropZone_DragOver"
                           DragLeave="DropZone_DragLeave">
                        <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="CloudUpload" 
                                                   Width="48" Height="48"
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            <TextBlock Text="Glissez et déposez vos images ici"
                                      Style="{StaticResource MaterialDesignBody1TextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      HorizontalAlignment="Center"
                                      Margin="0,10,0,0"/>
                            <TextBlock Text="Formats supportés: JPG, PNG, BMP, GIF"
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"
                                      HorizontalAlignment="Center"
                                      Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </materialDesign:Card>

            <!-- Séparateur -->
            <Border Grid.Column="1" Width="1" Background="{DynamicResource MaterialDesignDivider}" Margin="10,0"/>

            <!-- Images sélectionnées -->
            <materialDesign:Card Grid.Column="2" Padding="15">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <TextBlock Text="Images sélectionnées" 
                                  Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                  VerticalAlignment="Center"/>
                        <TextBlock x:Name="ImageCountText"
                                  Text="(0)"
                                  Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                  VerticalAlignment="Center"
                                  Margin="5,0,0,0"/>
                    </StackPanel>

                    <ScrollViewer MaxHeight="400" VerticalScrollBarVisibility="Auto">
                        <ItemsControl x:Name="SelectedImagesPanel">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <materialDesign:Card Margin="0,0,0,10" Padding="10">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="60"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Miniature -->
                                            <Border Grid.Column="0" 
                                                   Width="50" Height="50"
                                                   CornerRadius="5"
                                                   Background="{DynamicResource MaterialDesignCardBackground}">
                                                <Image Source="{Binding ImagePath}" 
                                                      Stretch="UniformToFill"/>
                                            </Border>

                                            <!-- Informations -->
                                            <StackPanel Grid.Column="1" Margin="10,0,0,0" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding FileName}" 
                                                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                <TextBlock Text="{Binding FileSize}" 
                                                          Style="{StaticResource MaterialDesignCaptionTextBlock}"/>
                                            </StackPanel>

                                            <!-- Actions -->
                                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                       ToolTip="Définir comme image principale"
                                                       Click="SetMainImage_Click"
                                                       Tag="{Binding}">
                                                    <materialDesign:PackIcon Kind="Star"/>
                                                </Button>
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                       ToolTip="Supprimer"
                                                       Click="RemoveImage_Click"
                                                       Tag="{Binding}">
                                                    <materialDesign:PackIcon Kind="Delete"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>
                                    </materialDesign:Card>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Boutons d'action -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="15">
            <Button x:Name="CancelButton"
                   Content="Annuler"
                   Style="{StaticResource MaterialDesignFlatButton}"
                   Click="CancelButton_Click"
                   Margin="5"/>
            <Button x:Name="OkButton"
                   Content="Valider"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Click="OkButton_Click"
                   Margin="5"/>
        </StackPanel>
    </Grid>
</Window>
