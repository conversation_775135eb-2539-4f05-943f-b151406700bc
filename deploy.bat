@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    ZinStore - Script de Deploiement
echo ========================================
echo.

:: Configuration
set "BUILD_CONFIG=Release"
set "PLATFORM=Any CPU"
set "OUTPUT_DIR=Deploy"
set "APP_NAME=ZinStore"
set "VERSION=1.0.0"

:: Nettoyage du dossier de sortie
if exist "%OUTPUT_DIR%" (
    echo Nettoyage du dossier de deploiement...
    rmdir /s /q "%OUTPUT_DIR%"
)
mkdir "%OUTPUT_DIR%"

:: Compilation en mode Release
echo.
echo Compilation en mode Release...
msbuild ZinStore.sln /p:Configuration=%BUILD_CONFIG% /p:Platform="%PLATFORM%" /p:OutputPath="..\%OUTPUT_DIR%\bin\"
if %errorlevel% neq 0 (
    echo Erreur lors de la compilation
    pause
    exit /b 1
)

:: Copie des fichiers necessaires
echo.
echo Copie des fichiers de l'application...
xcopy "ZinStore.UI\bin\%BUILD_CONFIG%\*" "%OUTPUT_DIR%\" /E /I /Y

:: Creation du dossier Data
mkdir "%OUTPUT_DIR%\Data" 2>nul
mkdir "%OUTPUT_DIR%\Backups" 2>nul
mkdir "%OUTPUT_DIR%\Reports" 2>nul
mkdir "%OUTPUT_DIR%\Logs" 2>nul

:: Copie des fichiers de configuration
echo Copie des fichiers de configuration...
copy "README.md" "%OUTPUT_DIR%\" >nul
copy "USER_GUIDE.md" "%OUTPUT_DIR%\" >nul
copy "INSTALLATION.md" "%OUTPUT_DIR%\" >nul
copy "LICENSE" "%OUTPUT_DIR%\" >nul

:: Creation du fichier de version
echo %VERSION% > "%OUTPUT_DIR%\version.txt"
echo %DATE% %TIME% >> "%OUTPUT_DIR%\version.txt"

:: Creation du script de lancement
echo @echo off > "%OUTPUT_DIR%\Lancer_ZinStore.bat"
echo echo Demarrage de ZinStore... >> "%OUTPUT_DIR%\Lancer_ZinStore.bat"
echo start "" "ZinStore.exe" >> "%OUTPUT_DIR%\Lancer_ZinStore.bat"

:: Creation du fichier de desinstallation
echo @echo off > "%OUTPUT_DIR%\Desinstaller.bat"
echo echo Desinstallation de ZinStore... >> "%OUTPUT_DIR%\Desinstaller.bat"
echo echo. >> "%OUTPUT_DIR%\Desinstaller.bat"
echo echo ATTENTION: Cette operation supprimera tous les fichiers ZinStore >> "%OUTPUT_DIR%\Desinstaller.bat"
echo echo y compris vos donnees si elles sont dans ce dossier. >> "%OUTPUT_DIR%\Desinstaller.bat"
echo echo. >> "%OUTPUT_DIR%\Desinstaller.bat"
echo pause >> "%OUTPUT_DIR%\Desinstaller.bat"
echo cd .. >> "%OUTPUT_DIR%\Desinstaller.bat"
echo rmdir /s /q "%OUTPUT_DIR%" >> "%OUTPUT_DIR%\Desinstaller.bat"

:: Creation d'un fichier README pour le deploiement
echo # ZinStore - Version Deployee > "%OUTPUT_DIR%\README_DEPLOY.txt"
echo. >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo Version: %VERSION% >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo Date de compilation: %DATE% %TIME% >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo. >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo ## Installation >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo 1. Copiez ce dossier vers l'emplacement souhaite >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo 2. Executez ZinStore.exe ou utilisez Lancer_ZinStore.bat >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo 3. Connectez-vous avec admin/admin123 >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo 4. Changez immediatement le mot de passe par defaut >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo. >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo ## Support >> "%OUTPUT_DIR%\README_DEPLOY.txt"
echo Email: <EMAIL> >> "%OUTPUT_DIR%\README_DEPLOY.txt"

:: Verification des fichiers critiques
echo.
echo Verification des fichiers critiques...
set "CRITICAL_FILES=ZinStore.exe ZinStore.Core.dll ZinStore.Data.dll ZinStore.Business.dll"
for %%f in (%CRITICAL_FILES%) do (
    if not exist "%OUTPUT_DIR%\%%f" (
        echo ERREUR: Fichier critique manquant: %%f
        pause
        exit /b 1
    )
)

:: Creation d'une archive ZIP (si 7-Zip est disponible)
where 7z >nul 2>nul
if %errorlevel% equ 0 (
    echo.
    echo Creation de l'archive ZIP...
    7z a -tzip "%APP_NAME%_v%VERSION%_Portable.zip" "%OUTPUT_DIR%\*"
    if %errorlevel% equ 0 (
        echo Archive creee: %APP_NAME%_v%VERSION%_Portable.zip
    )
) else (
    echo 7-Zip non trouve, archive ZIP non creee
)

:: Affichage du resume
echo.
echo ========================================
echo    Deploiement termine avec succes!
echo ========================================
echo.
echo Dossier de deploiement: %OUTPUT_DIR%
echo Version: %VERSION%
echo Date: %DATE% %TIME%
echo.
echo Fichiers principaux:
dir "%OUTPUT_DIR%\*.exe" /b
echo.
echo Pour tester l'application:
echo cd %OUTPUT_DIR%
echo ZinStore.exe
echo.
echo Pour distribuer:
echo - Copiez le dossier %OUTPUT_DIR% complet
echo - Ou utilisez l'archive ZIP si creee
echo.
pause
