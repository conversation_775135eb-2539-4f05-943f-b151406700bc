using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.UI.Helpers;
using ZinStore.UI.Views;

namespace ZinStore.UI.ViewModels
{
    /// <summary>
    /// ViewModel pour la fenêtre de connexion
    /// </summary>
    public class LoginViewModel : BaseViewModel
    {
        private readonly AuthenticationService _authService;

        public LoginViewModel()
        {
            _authService = AuthenticationService.Instance;
            LoginCommand = new RelayCommand(async () => await LoginAsync(), CanLogin);
            ExitCommand = new RelayCommand(Exit);
        }

        private string _username;
        public string Username
        {
            get => _username;
            set
            {
                SetProperty(ref _username, value);
                ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
            }
        }

        private string _password;
        public string Password
        {
            get => _password;
            set
            {
                SetProperty(ref _password, value);
                ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
            }
        }

        private string _errorMessage;
        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                SetProperty(ref _errorMessage, value);
                OnPropertyChanged(nameof(HasError));
            }
        }

        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

        public ICommand LoginCommand { get; }
        public ICommand ExitCommand { get; }

        protected override void OnIsBusyChanged()
        {
            base.OnIsBusyChanged();
            ((RelayCommand)LoginCommand).RaiseCanExecuteChanged();
        }

        private bool CanLogin()
        {
            return !string.IsNullOrWhiteSpace(Username) &&
                   !string.IsNullOrWhiteSpace(Password) &&
                   !IsBusy;
        }

        private async Task LoginAsync()
        {
            try
            {
                IsBusy = true;
                BusyMessage = "Connexion en cours...";
                ErrorMessage = string.Empty;

                // Debug: Afficher les informations de connexion
                System.Diagnostics.Debug.WriteLine($"Tentative de connexion - Utilisateur: {Username}");
                System.Diagnostics.Debug.WriteLine($"Mot de passe fourni: {Password}");

                // Vérifier si les champs sont remplis
                if (string.IsNullOrWhiteSpace(Username) || string.IsNullOrWhiteSpace(Password))
                {
                    ErrorMessage = "Veuillez saisir le nom d'utilisateur et le mot de passe.";
                    return;
                }

                bool success = await _authService.LoginAsync(Username, Password);

                System.Diagnostics.Debug.WriteLine($"Résultat de la connexion: {success}");

                if (success)
                {
                    // Ouvrir la fenêtre principale
                    var mainWindow = new MainWindow();
                    mainWindow.Show();

                    // Fermer la fenêtre de connexion
                    foreach (Window window in Application.Current.Windows)
                    {
                        if (window is LoginWindow)
                        {
                            window.Close();
                            break;
                        }
                    }
                }
                else
                {
                    ErrorMessage = "Nom d'utilisateur ou mot de passe incorrect.\n\nUtilisateur par défaut:\nNom: admin\nMot de passe: admin123";
                    System.Diagnostics.Debug.WriteLine("Échec de la connexion");
                }
            }
            catch (System.Exception ex)
            {
                ErrorMessage = $"Erreur de connexion: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Exception lors de la connexion: {ex}");
            }
            finally
            {
                IsBusy = false;
                BusyMessage = string.Empty;
            }
        }

        private void Exit()
        {
            Application.Current.Shutdown();
        }
    }
}
