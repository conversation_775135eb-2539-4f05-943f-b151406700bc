using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class RevenuRepository : BaseRepository<Revenu>
    {
        public RevenuRepository(DatabaseContext context) : base(context, "Revenus")
        {
        }

        public override async Task<IEnumerable<Revenu>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT r.*, c.Nom as ClientNom, u.NomComplet as UtilisateurNom
                FROM Revenus r
                LEFT JOIN Clients c ON r.ClientId = c.Id
                LEFT JOIN Utilisateurs u ON r.UtilisateurId = u.Id
                WHERE r.EstSupprime = 0 
                AND (r.NumeroReference LIKE @SearchTerm OR r.Description LIKE @SearchTerm)
                ORDER BY r.DateRevenu DESC";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Revenu>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }
    }
}
