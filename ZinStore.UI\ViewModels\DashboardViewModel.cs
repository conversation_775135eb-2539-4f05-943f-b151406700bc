using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    /// <summary>
    /// ViewModel pour le tableau de bord
    /// </summary>
    public class DashboardViewModel : BaseViewModel
    {
        private readonly ClientService _clientService;
        private readonly ProduitService _produitService;
        private readonly VenteService _venteService;
        private readonly RapportService _rapportService;

        public DashboardViewModel()
        {
            var context = new DatabaseContext();
            _clientService = new ClientService(context);
            _produitService = new ProduitService(context);
            _venteService = new VenteService(context);
            _rapportService = new RapportService(context);

            // Initialiser les commandes
            NouvelleVenteCommand = new RelayCommand(NouvelleVente);
            NouvelAchatCommand = new RelayCommand(NouvelAchat);
            NouveauClientCommand = new RelayCommand(NouveauClient);
            NouveauProduitCommand = new RelayCommand(NouveauProduit);
            VoirStockCommand = new RelayCommand(VoirStock);
            VoirRapportsCommand = new RelayCommand(VoirRapports);
            ParametresCommand = new RelayCommand(Parametres);
            SauvegardeCommand = new RelayCommand(Sauvegarde);

            // Initialiser les collections
            VentesRecentes = new ObservableCollection<Vente>();
            ProduitsEnRuptureList = new ObservableCollection<Produit>();

            // Charger les données
            LoadDataAsync();
        }

        // Propriétés pour les statistiques
        private decimal _ventesAujourdhui;
        public decimal VentesAujourdhui
        {
            get => _ventesAujourdhui;
            set => SetProperty(ref _ventesAujourdhui, value);
        }

        private int _nombreClients;
        public int NombreClients
        {
            get => _nombreClients;
            set => SetProperty(ref _nombreClients, value);
        }

        private int _nombreProduits;
        public int NombreProduits
        {
            get => _nombreProduits;
            set => SetProperty(ref _nombreProduits, value);
        }

        private int _produitsEnRupture;
        public int ProduitsEnRupture
        {
            get => _produitsEnRupture;
            set => SetProperty(ref _produitsEnRupture, value);
        }

        // Collections
        public ObservableCollection<Vente> VentesRecentes { get; }
        public ObservableCollection<Produit> ProduitsEnRuptureList { get; }

        // Commandes
        public ICommand NouvelleVenteCommand { get; }
        public ICommand NouvelAchatCommand { get; }
        public ICommand NouveauClientCommand { get; }
        public ICommand NouveauProduitCommand { get; }
        public ICommand VoirStockCommand { get; }
        public ICommand VoirRapportsCommand { get; }
        public ICommand ParametresCommand { get; }
        public ICommand SauvegardeCommand { get; }

        private async void LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                BusyMessage = "Chargement des données...";

                // Charger les statistiques
                await LoadStatisticsAsync();

                // Charger les ventes récentes
                await LoadRecentSalesAsync();

                // Charger les produits en rupture
                await LoadLowStockProductsAsync();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
                BusyMessage = string.Empty;
            }
        }

        private async Task LoadStatisticsAsync()
        {
            // Simuler le chargement des statistiques
            // En production, ces données viendraient de la base de données
            VentesAujourdhui = 15750.50m;
            NombreClients = 125;
            NombreProduits = 450;
            ProduitsEnRupture = 8;
        }

        private async Task LoadRecentSalesAsync()
        {
            // Simuler le chargement des ventes récentes
            VentesRecentes.Clear();
            
            // En production, charger depuis la base de données
            for (int i = 1; i <= 5; i++)
            {
                VentesRecentes.Add(new Vente
                {
                    NumeroFacture = $"FV{DateTime.Now:yyyyMMdd}{i:D3}",
                    NomClient = $"Client {i}",
                    MontantTotal = 1500 + (i * 250),
                    DateVente = DateTime.Now.AddHours(-i)
                });
            }
        }

        private async Task LoadLowStockProductsAsync()
        {
            try
            {
                var produits = await _produitService.GetProduitsEnRuptureAsync();
                ProduitsEnRuptureList.Clear();
                
                foreach (var produit in produits)
                {
                    ProduitsEnRuptureList.Add(produit);
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, ajouter des données de test
                ProduitsEnRuptureList.Clear();
                for (int i = 1; i <= 3; i++)
                {
                    ProduitsEnRuptureList.Add(new Produit
                    {
                        CodeProduit = $"PR{i:D6}",
                        Nom = $"Produit en rupture {i}",
                        StockActuel = i,
                        StockMinimum = 10
                    });
                }
            }
        }

        // Méthodes des commandes
        private void NouvelleVente()
        {
            MessageBoxHelper.ShowInfo("Module de vente en cours de développement.");
        }

        private void NouvelAchat()
        {
            MessageBoxHelper.ShowInfo("Module d'achat en cours de développement.");
        }

        private void NouveauClient()
        {
            MessageBoxHelper.ShowInfo("Module de gestion des clients en cours de développement.");
        }

        private void NouveauProduit()
        {
            MessageBoxHelper.ShowInfo("Module de gestion des produits en cours de développement.");
        }

        private void VoirStock()
        {
            MessageBoxHelper.ShowInfo("Module de gestion du stock en cours de développement.");
        }

        private void VoirRapports()
        {
            MessageBoxHelper.ShowInfo("Module des rapports en cours de développement.");
        }

        private void Parametres()
        {
            MessageBoxHelper.ShowInfo("Module des paramètres en cours de développement.");
        }

        private void Sauvegarde()
        {
            if (MessageBoxHelper.ShowConfirmation("Voulez-vous effectuer une sauvegarde de la base de données ?"))
            {
                try
                {
                    // Implémenter la logique de sauvegarde
                    MessageBoxHelper.ShowInfo("Sauvegarde effectuée avec succès.");
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la sauvegarde: {ex.Message}");
                }
            }
        }
    }
}
