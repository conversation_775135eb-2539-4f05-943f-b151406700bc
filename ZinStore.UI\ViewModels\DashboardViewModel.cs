using System;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Business.Services;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    /// <summary>
    /// ViewModel pour le tableau de bord
    /// </summary>
    public class DashboardViewModel : BaseViewModel
    {
        private readonly ClientService _clientService;
        private readonly ProduitService _produitService;
        private readonly VenteService _venteService;
        private readonly RapportService _rapportService;

        public DashboardViewModel()
        {
            var context = new DatabaseContext();
            _clientService = new ClientService(context);
            _produitService = new ProduitService(context);
            _venteService = new VenteService(context);
            _rapportService = new RapportService(context);

            // Initialiser les commandes
            NouvelleVenteCommand = new RelayCommand(NouvelleVente);
            NouvelAchatCommand = new RelayCommand(NouvelAchat);
            NouveauClientCommand = new RelayCommand(NouveauClient);
            NouveauProduitCommand = new RelayCommand(NouveauProduit);
            VoirStockCommand = new RelayCommand(VoirStock);
            VoirRapportsCommand = new RelayCommand(VoirRapports);
            ParametresCommand = new RelayCommand(Parametres);
            SauvegardeCommand = new RelayCommand(Sauvegarde);

            // Initialiser les collections
            VentesRecentes = new ObservableCollection<Vente>();
            ProduitsEnRuptureList = new ObservableCollection<Produit>();

            // Charger les données
            LoadDataAsync();
        }

        // Propriétés pour les statistiques
        private decimal _ventesAujourdhui;
        public decimal VentesAujourdhui
        {
            get => _ventesAujourdhui;
            set => SetProperty(ref _ventesAujourdhui, value);
        }

        private int _nombreClients;
        public int NombreClients
        {
            get => _nombreClients;
            set => SetProperty(ref _nombreClients, value);
        }

        private int _nombreProduits;
        public int NombreProduits
        {
            get => _nombreProduits;
            set => SetProperty(ref _nombreProduits, value);
        }

        private int _produitsEnRupture;
        public int ProduitsEnRupture
        {
            get => _produitsEnRupture;
            set => SetProperty(ref _produitsEnRupture, value);
        }

        // Collections
        public ObservableCollection<Vente> VentesRecentes { get; }
        public ObservableCollection<Produit> ProduitsEnRuptureList { get; }

        // Commandes
        public ICommand NouvelleVenteCommand { get; }
        public ICommand NouvelAchatCommand { get; }
        public ICommand NouveauClientCommand { get; }
        public ICommand NouveauProduitCommand { get; }
        public ICommand VoirStockCommand { get; }
        public ICommand VoirRapportsCommand { get; }
        public ICommand ParametresCommand { get; }
        public ICommand SauvegardeCommand { get; }

        private async void LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                BusyMessage = "Chargement des données...";

                // Charger les statistiques
                await LoadStatisticsAsync();

                // Charger les ventes récentes
                await LoadRecentSalesAsync();

                // Charger les produits en rupture
                await LoadLowStockProductsAsync();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
                BusyMessage = string.Empty;
            }
        }

        private async Task LoadStatisticsAsync()
        {
            try
            {
                // Charger les statistiques réelles depuis la base de données
                await Task.Run(async () =>
                {
                    // Ventes d'aujourd'hui
                    var ventesAujourdhui = await _venteService.GetVentesParPeriodeAsync(DateTime.Today, DateTime.Today.AddDays(1));
                    VentesAujourdhui = ventesAujourdhui?.Sum(v => v.MontantTotal) ?? 0;

                    // Nombre de clients
                    var clients = await _clientService.GetAllClientsAsync();
                    NombreClients = clients?.Count() ?? 0;

                    // Nombre de produits
                    var produits = await _produitService.GetAllProduitsAsync();
                    NombreProduits = produits?.Count() ?? 0;

                    // Produits en rupture
                    var produitsRupture = await _produitService.GetProduitsEnRuptureAsync();
                    ProduitsEnRupture = produitsRupture?.Count() ?? 0;
                });
            }
            catch (Exception ex)
            {
                // En cas d'erreur, utiliser des données par défaut
                System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement des statistiques: {ex.Message}");
                VentesAujourdhui = 15750.50m;
                NombreClients = 125;
                NombreProduits = 450;
                ProduitsEnRupture = 8;
            }
        }

        private async Task LoadRecentSalesAsync()
        {
            try
            {
                // Charger les ventes récentes depuis la base de données
                var ventesRecentes = await _venteService.GetVentesRecentesAsync(10);
                VentesRecentes.Clear();

                if (ventesRecentes != null && ventesRecentes.Any())
                {
                    foreach (var vente in ventesRecentes.Take(5))
                    {
                        VentesRecentes.Add(vente);
                    }
                }
                else
                {
                    // Données d'exemple si aucune vente trouvée
                    for (int i = 1; i <= 5; i++)
                    {
                        VentesRecentes.Add(new Vente
                        {
                            NumeroFacture = $"FV{DateTime.Now:yyyyMMdd}{i:D3}",
                            NomClient = $"Client {i}",
                            MontantTotal = 1500 + (i * 250),
                            DateVente = DateTime.Now.AddHours(-i)
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erreur lors du chargement des ventes récentes: {ex.Message}");
                // Charger des données d'exemple en cas d'erreur
                VentesRecentes.Clear();
                for (int i = 1; i <= 5; i++)
                {
                    VentesRecentes.Add(new Vente
                    {
                        NumeroFacture = $"FV{DateTime.Now:yyyyMMdd}{i:D3}",
                        NomClient = $"Client {i}",
                        MontantTotal = 1500 + (i * 250),
                        DateVente = DateTime.Now.AddHours(-i)
                    });
                }
            }
        }

        private async Task LoadLowStockProductsAsync()
        {
            try
            {
                var produits = await _produitService.GetProduitsEnRuptureAsync();
                ProduitsEnRuptureList.Clear();
                
                foreach (var produit in produits)
                {
                    ProduitsEnRuptureList.Add(produit);
                }
            }
            catch (Exception ex)
            {
                // En cas d'erreur, ajouter des données de test
                ProduitsEnRuptureList.Clear();
                for (int i = 1; i <= 3; i++)
                {
                    ProduitsEnRuptureList.Add(new Produit
                    {
                        CodeProduit = $"PR{i:D6}",
                        Nom = $"Produit en rupture {i}",
                        StockActuel = i,
                        StockMinimum = 10
                    });
                }
            }
        }

        // Méthodes des commandes
        private void NouvelleVente()
        {
            try
            {
                // Ouvrir la fenêtre de nouvelle vente
                var venteWindow = new Views.Ventes.VenteFormWindow();
                if (venteWindow.ShowDialog() == true)
                {
                    // Actualiser les données après ajout
                    _ = LoadDataAsync();
                    MessageBoxHelper.ShowSuccess("Vente ajoutée avec succès.");
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ouverture du module de vente: {ex.Message}");
            }
        }

        private void NouvelAchat()
        {
            try
            {
                // Ouvrir la fenêtre de nouvel achat
                var achatWindow = new Views.AchatsView();
                MessageBoxHelper.ShowInfo("Redirection vers le module d'achat...");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ouverture du module d'achat: {ex.Message}");
            }
        }

        private void NouveauClient()
        {
            try
            {
                // Ouvrir la fenêtre de nouveau client
                var clientWindow = new Views.Clients.ClientFormWindow();
                if (clientWindow.ShowDialog() == true)
                {
                    // Actualiser les statistiques
                    _ = LoadStatisticsAsync();
                    MessageBoxHelper.ShowSuccess("Client ajouté avec succès.");
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ouverture du formulaire client: {ex.Message}");
            }
        }

        private void NouveauProduit()
        {
            try
            {
                // Ouvrir la fenêtre de nouveau produit
                var produitWindow = new Views.Produits.ProduitFormWindow();
                if (produitWindow.ShowDialog() == true)
                {
                    // Actualiser les statistiques
                    _ = LoadStatisticsAsync();
                    MessageBoxHelper.ShowSuccess("Produit ajouté avec succès.");
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ouverture du formulaire produit: {ex.Message}");
            }
        }

        private void VoirStock()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Redirection vers le module de gestion du stock...");
                // Navigation vers la vue stock
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ouverture du module stock: {ex.Message}");
            }
        }

        private void VoirRapports()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Redirection vers le module des rapports...");
                // Navigation vers la vue rapports
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ouverture du module rapports: {ex.Message}");
            }
        }

        private void Parametres()
        {
            try
            {
                MessageBoxHelper.ShowInfo("Redirection vers les paramètres...");
                // Navigation vers la vue paramètres
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ouverture des paramètres: {ex.Message}");
            }
        }

        private async void Sauvegarde()
        {
            if (MessageBoxHelper.ShowConfirmation("Voulez-vous effectuer une sauvegarde de la base de données ?"))
            {
                try
                {
                    IsBusy = true;
                    BusyMessage = "Sauvegarde en cours...";

                    // Implémenter la logique de sauvegarde
                    await Task.Run(() =>
                    {
                        // Créer un nom de fichier avec timestamp
                        var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                        var backupFileName = $"ZinStore_Backup_{timestamp}.db";
                        var backupPath = System.IO.Path.Combine(
                            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                            "ZinStore_Backups",
                            backupFileName);

                        // Créer le dossier s'il n'existe pas
                        var backupDir = System.IO.Path.GetDirectoryName(backupPath);
                        if (!System.IO.Directory.Exists(backupDir))
                        {
                            System.IO.Directory.CreateDirectory(backupDir);
                        }

                        // Copier le fichier de base de données
                        var sourcePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "ZinStore.db");
                        if (System.IO.File.Exists(sourcePath))
                        {
                            System.IO.File.Copy(sourcePath, backupPath, true);
                        }
                        else
                        {
                            throw new FileNotFoundException("Fichier de base de données introuvable.");
                        }

                        // Simuler un délai pour montrer le processus
                        System.Threading.Thread.Sleep(2000);
                    });

                    MessageBoxHelper.ShowSuccess($"Sauvegarde effectuée avec succès.\nFichier sauvegardé dans: Documents/ZinStore_Backups/");
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la sauvegarde: {ex.Message}");
                }
                finally
                {
                    IsBusy = false;
                    BusyMessage = string.Empty;
                }
            }
        }

        // Méthode pour actualiser les données
        public async Task RefreshDataAsync()
        {
            await LoadDataAsync();
        }
    }
}
