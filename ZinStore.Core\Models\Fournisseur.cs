using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les fournisseurs
    /// </summary>
    public class Fournisseur : BaseEntity
    {
        [Required(ErrorMessage = "Le code fournisseur est obligatoire")]
        [StringLength(20, ErrorMessage = "Le code fournisseur ne peut pas dépasser 20 caractères")]
        public string CodeFournisseur { get; set; }

        [Required(ErrorMessage = "Le nom du fournisseur est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom du fournisseur ne peut pas dépasser 100 caractères")]
        public string Nom { get; set; }

        [StringLength(100)]
        public string RaisonSociale { get; set; }

        [StringLength(100)]
        public string NomCommercial { get; set; }

        [StringLength(200)]
        public string Adresse { get; set; }

        [StringLength(50)]
        public string Ville { get; set; }

        [StringLength(10)]
        public string CodePostal { get; set; }

        [StringLength(50)]
        public string Pays { get; set; }

        [StringLength(20)]
        public string Telephone { get; set; }

        [StringLength(20)]
        public string Fax { get; set; }

        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(100)]
        public string SiteWeb { get; set; }

        [StringLength(20)]
        public string NumeroRegistreCommerce { get; set; }

        [StringLength(20)]
        public string NumeroIdentificationFiscale { get; set; }

        [StringLength(20)]
        public string NumeroTVA { get; set; }

        [StringLength(20)]
        public string RegistreCommerce { get; set; }

        public decimal LimiteCredit { get; set; } = 0;

        public decimal SoldeCompte { get; set; } = 0;

        public bool EstActif { get; set; } = true;

        public string Notes { get; set; }

        // Informations de contact
        [StringLength(100)]
        public string PersonneContact { get; set; }

        [StringLength(50)]
        public string FonctionContact { get; set; }

        [StringLength(20)]
        public string TelephoneContact { get; set; }

        [StringLength(100)]
        public string EmailContact { get; set; }

        // Conditions commerciales
        public int DelaiPaiement { get; set; } = 30; // en jours

        public int DelaiLivraison { get; set; } = 7; // en jours

        [StringLength(50)]
        public string ConditionsPaiement { get; set; }

        public decimal RemiseHabituelle { get; set; } = 0;
    }
}
