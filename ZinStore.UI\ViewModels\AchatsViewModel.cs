using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Core.Models;
using ZinStore.Business.Services;
using ZinStore.Data.Context;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class AchatsViewModel : BaseViewModel
    {
        private readonly AchatService _achatService;
        private ObservableCollection<Achat> _achats;
        private Achat _selectedAchat;
        private string _searchText;
        private DateTime? _dateDebut;
        private DateTime? _dateFin;

        public AchatsViewModel()
        {
            // Initialiser le service avec DatabaseContext
            _achatService = new AchatService(new DatabaseContext());

            Achats = new ObservableCollection<Achat>();

            // Commandes
            AddAchatCommand = new RelayCommand(AddAchat);
            ViewDetailsCommand = new RelayCommand(ViewDetails, CanViewDetails);
            PrintCommand = new RelayCommand(Print, CanPrint);
            RefreshCommand = new RelayCommand(async () => await LoadAchatsAsync());
            SearchCommand = new RelayCommand(async () => await SearchAchatsAsync());

            // Charger les données
            _ = LoadAchatsAsync();
        }

        public ObservableCollection<Achat> Achats
        {
            get => _achats;
            set => SetProperty(ref _achats, value);
        }

        public Achat SelectedAchat
        {
            get => _selectedAchat;
            set => SetProperty(ref _selectedAchat, value);
        }

        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public DateTime? DateDebut
        {
            get => _dateDebut;
            set => SetProperty(ref _dateDebut, value);
        }

        public DateTime? DateFin
        {
            get => _dateFin;
            set => SetProperty(ref _dateFin, value);
        }

        // Commandes
        public ICommand AddAchatCommand { get; }
        public ICommand ViewDetailsCommand { get; }
        public ICommand PrintCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand SearchCommand { get; }

        private async Task LoadAchatsAsync()
        {
            try
            {
                IsBusy = true;
                var achats = await _achatService.GetAllAchatsAsync();

                Achats.Clear();
                foreach (var achat in achats)
                {
                    Achats.Add(achat);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des achats: {ex.Message}");
                // Charger des données de test en cas d'erreur
                LoadTestData();
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task SearchAchatsAsync()
        {
            try
            {
                IsBusy = true;
                var achats = await _achatService.SearchAchatsAsync(SearchText, DateDebut, DateFin);

                Achats.Clear();
                foreach (var achat in achats)
                {
                    Achats.Add(achat);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void AddAchat()
        {
            try
            {
                var window = new Views.Achats.AchatFormWindow();
                if (window.ShowDialog() == true)
                {
                    _ = LoadAchatsAsync();
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ouverture du formulaire: {ex.Message}");
            }
        }

        private void ViewDetails()
        {
            if (SelectedAchat == null) return;

            try
            {
                var window = new Views.Achats.AchatFormWindow();
                // Passer l'achat sélectionné au formulaire
                if (window.DataContext is ViewModels.AchatFormViewModel viewModel)
                {
                    viewModel.LoadAchat(SelectedAchat);
                }
                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'affichage des détails: {ex.Message}");
            }
        }

        private void Print()
        {
            if (SelectedAchat == null) return;

            try
            {
                // Logique d'impression
                MessageBoxHelper.ShowInfo("Fonctionnalité d'impression en cours de développement.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'impression: {ex.Message}");
            }
        }

        private bool CanViewDetails()
        {
            return SelectedAchat != null;
        }

        private bool CanPrint()
        {
            return SelectedAchat != null;
        }

        private void LoadTestData()
        {
            // Données de test pour le design-time
            Achats.Clear();
            Achats.Add(new Achat
            {
                Id = 1,
                NumeroBon = "BA001",
                DateAchat = DateTime.Now.AddDays(-5),
                NomFournisseur = "Fournisseur ABC",
                MontantHT = 1500.00m,
                MontantTVA = 285.00m,
                MontantTotal = 1785.00m,
                StatutAchat = "Livré",
                ModePaiement = "Espèces"
            });
            
            Achats.Add(new Achat
            {
                Id = 2,
                NumeroBon = "BA002",
                DateAchat = DateTime.Now.AddDays(-3),
                NomFournisseur = "Fournisseur XYZ",
                MontantHT = 2300.00m,
                MontantTVA = 437.00m,
                MontantTotal = 2737.00m,
                StatutAchat = "En attente",
                ModePaiement = "Chèque"
            });
            
            Achats.Add(new Achat
            {
                Id = 3,
                NumeroBon = "BA003",
                DateAchat = DateTime.Now.AddDays(-1),
                NomFournisseur = "Fournisseur DEF",
                MontantHT = 890.00m,
                MontantTVA = 169.10m,
                MontantTotal = 1059.10m,
                StatutAchat = "Livré",
                ModePaiement = "Virement"
            });
        }
    }
}
