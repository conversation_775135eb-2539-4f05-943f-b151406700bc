using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les catégories de produits
    /// </summary>
    public class Categorie : BaseEntity
    {
        [Required(ErrorMessage = "Le code catégorie est obligatoire")]
        [StringLength(20, ErrorMessage = "Le code catégorie ne peut pas dépasser 20 caractères")]
        public string CodeCategorie { get; set; }

        [Required(ErrorMessage = "Le nom de la catégorie est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom de la catégorie ne peut pas dépasser 100 caractères")]
        public string Nom { get; set; }

        [StringLength(500)]
        public string Description { get; set; }

        public int? CategorieParentId { get; set; }

        public bool EstActive { get; set; } = true;

        public string Image { get; set; }

        [StringLength(20)]
        public string Couleur { get; set; } = "#2196F3";

        [StringLength(50)]
        public string Icone { get; set; } = "FolderMultiple";

        public bool AfficherDansMenu { get; set; } = true;

        public bool AutoriserSousCategories { get; set; } = false;

        public string MotsCles { get; set; }

        public string Notes { get; set; }

        public int OrdreAffichage { get; set; } = 0;

        // Propriétés de navigation
        public virtual Categorie CategorieParent { get; set; }
    }
}
