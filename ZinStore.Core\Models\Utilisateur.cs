using System.ComponentModel.DataAnnotations;
using ZinStore.Core.Enums;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les utilisateurs du système
    /// </summary>
    public class Utilisateur : BaseEntity
    {
        [Required(ErrorMessage = "Le nom d'utilisateur est obligatoire")]
        [StringLength(50, ErrorMessage = "Le nom d'utilisateur ne peut pas dépasser 50 caractères")]
        public string NomUtilisateur { get; set; }

        [Required(ErrorMessage = "Le mot de passe est obligatoire")]
        [StringLength(255)]
        public string MotDePasse { get; set; }

        [Required(ErrorMessage = "Le nom complet est obligatoire")]
        [StringLength(100, ErrorMessage = "Le nom complet ne peut pas dépasser 100 caractères")]
        public string NomComplet { get; set; }

        [EmailAddress(ErrorMessage = "Format d'email invalide")]
        [StringLength(100)]
        public string Email { get; set; }

        [StringLength(20)]
        public string Telephone { get; set; }

        [Required]
        public TypeUtilisateur TypeUtilisateur { get; set; }

        public bool EstActif { get; set; } = true;

        public string Photo { get; set; }

        public string Adresse { get; set; }

        // Propriétés pour les permissions
        public bool PeutGererUtilisateurs { get; set; }
        public bool PeutGererClients { get; set; }
        public bool PeutGererFournisseurs { get; set; }
        public bool PeutGererProduits { get; set; }
        public bool PeutGererVentes { get; set; }
        public bool PeutGererAchats { get; set; }
        public bool PeutGererStock { get; set; }
        public bool PeutGererComptabilite { get; set; }
        public bool PeutVoirRapports { get; set; }
        public bool PeutGererParametres { get; set; }
    }
}
