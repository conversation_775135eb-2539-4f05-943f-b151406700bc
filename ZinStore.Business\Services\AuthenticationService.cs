using System;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;
using ZinStore.Business.Helpers;

namespace ZinStore.Business.Services
{
    /// <summary>
    /// Service d'authentification et de gestion des utilisateurs
    /// </summary>
    public class AuthenticationService
    {
        private readonly UtilisateurRepository _utilisateurRepository;
        private Utilisateur _currentUser;
        private static AuthenticationService _instance;
        private static readonly object _lock = new object();

        public AuthenticationService(DatabaseContext context)
        {
            _utilisateurRepository = new UtilisateurRepository(context);
        }

        /// <summary>
        /// Instance singleton du service d'authentification
        /// </summary>
        public static AuthenticationService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new AuthenticationService(new DatabaseContext());
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Utilisateur actuellement connecté
        /// </summary>
        public Utilisateur CurrentUser => _currentUser;

        /// <summary>
        /// Vérifie si un utilisateur est connecté
        /// </summary>
        public bool IsAuthenticated => _currentUser != null;

        /// <summary>
        /// Authentifie un utilisateur
        /// </summary>
        public async Task<bool> LoginAsync(string nomUtilisateur, string motDePasse)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"AuthenticationService.LoginAsync - Début");
                System.Diagnostics.Debug.WriteLine($"Nom d'utilisateur: {nomUtilisateur}");

                if (string.IsNullOrWhiteSpace(nomUtilisateur) || string.IsNullOrWhiteSpace(motDePasse))
                {
                    System.Diagnostics.Debug.WriteLine("Nom d'utilisateur ou mot de passe vide");
                    return false;
                }

                // Hacher le mot de passe pour la comparaison
                string hashedPassword = PasswordHelper.HashPassword(motDePasse);
                System.Diagnostics.Debug.WriteLine($"Mot de passe haché: {hashedPassword.Substring(0, 20)}...");

                // Authentifier l'utilisateur
                var user = await _utilisateurRepository.AuthenticateAsync(nomUtilisateur, hashedPassword);
                System.Diagnostics.Debug.WriteLine($"Utilisateur trouvé: {user != null}");

                if (user != null)
                {
                    _currentUser = user;
                    System.Diagnostics.Debug.WriteLine($"Connexion réussie pour: {user.NomComplet}");
                    return true;
                }

                System.Diagnostics.Debug.WriteLine("Aucun utilisateur correspondant trouvé");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Exception dans LoginAsync: {ex}");
                return false;
            }
        }

        /// <summary>
        /// Déconnecte l'utilisateur actuel
        /// </summary>
        public void Logout()
        {
            _currentUser = null;
        }

        /// <summary>
        /// Change le mot de passe de l'utilisateur actuel
        /// </summary>
        public async Task<bool> ChangePasswordAsync(string ancienMotDePasse, string nouveauMotDePasse)
        {
            if (!IsAuthenticated)
                return false;

            try
            {
                // Vérifier l'ancien mot de passe
                string hashedOldPassword = PasswordHelper.HashPassword(ancienMotDePasse);
                if (_currentUser.MotDePasse != hashedOldPassword)
                    return false;

                // Valider le nouveau mot de passe
                if (!ValidationHelper.IsValidPassword(nouveauMotDePasse))
                    return false;

                // Mettre à jour le mot de passe
                string hashedNewPassword = PasswordHelper.HashPassword(nouveauMotDePasse);
                bool success = await _utilisateurRepository.UpdatePasswordAsync(_currentUser.Id, hashedNewPassword);

                if (success)
                {
                    _currentUser.MotDePasse = hashedNewPassword;
                }

                return success;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Vérifie si l'utilisateur actuel a une permission spécifique
        /// </summary>
        public bool HasPermission(string permission)
        {
            if (!IsAuthenticated)
                return false;

            switch (permission.ToLower())
            {
                case "gerer_utilisateurs":
                    return _currentUser.PeutGererUtilisateurs;
                case "gerer_clients":
                    return _currentUser.PeutGererClients;
                case "gerer_fournisseurs":
                    return _currentUser.PeutGererFournisseurs;
                case "gerer_produits":
                    return _currentUser.PeutGererProduits;
                case "gerer_ventes":
                    return _currentUser.PeutGererVentes;
                case "gerer_achats":
                    return _currentUser.PeutGererAchats;
                case "gerer_stock":
                    return _currentUser.PeutGererStock;
                case "gerer_comptabilite":
                    return _currentUser.PeutGererComptabilite;
                case "voir_rapports":
                    return _currentUser.PeutVoirRapports;
                case "gerer_parametres":
                    return _currentUser.PeutGererParametres;
                default:
                    return false;
            }
        }

        /// <summary>
        /// Vérifie si l'utilisateur est administrateur
        /// </summary>
        public bool IsAdmin()
        {
            return IsAuthenticated && _currentUser.TypeUtilisateur == Core.Enums.TypeUtilisateur.Administrateur;
        }

        /// <summary>
        /// Rafraîchit les informations de l'utilisateur actuel
        /// </summary>
        public async Task<bool> RefreshCurrentUserAsync()
        {
            if (!IsAuthenticated)
                return false;

            try
            {
                var refreshedUser = await _utilisateurRepository.GetByIdAsync(_currentUser.Id);
                if (refreshedUser != null && refreshedUser.EstActif)
                {
                    _currentUser = refreshedUser;
                    return true;
                }
                else
                {
                    // L'utilisateur a été désactivé ou supprimé
                    Logout();
                    return false;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
