using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace ZinStore.UI.Views.Branches
{
    /// <summary>
    /// Logique d'interaction pour BranchesView.xaml
    /// </summary>
    public partial class BranchesView : UserControl
    {
        public BranchesView()
        {
            InitializeComponent();
        }

        private void BranchCard_Click(object sender, MouseButtonEventArgs e)
        {
            // Gérer le clic sur la carte de filiale
            if (sender is FrameworkElement element && element.Tag != null)
            {
                var viewModel = DataContext as ViewModels.BranchesViewModel;
                viewModel?.ViewBranchDetailsCommand.Execute(element.Tag);
            }
        }
    }
}
