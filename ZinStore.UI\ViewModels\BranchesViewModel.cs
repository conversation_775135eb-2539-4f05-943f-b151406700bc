using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class BranchesViewModel : BaseViewModel
    {
        private string _searchText;
        private string _regionFilter = "All";
        private string _statusFilter = "All";
        private int _totalBranches;
        private int _activeBranches;
        private decimal _totalRevenue;
        private int _totalEmployees;
        private decimal _averagePerformance;

        public BranchesViewModel()
        {
            Branches = new ObservableCollection<Branch>();

            // Commandes
            AddBranchCommand = new RelayCommand(AddBranch);
            EditBranchCommand = new RelayCommand(param => EditBranch(param as Branch));
            ViewBranchDetailsCommand = new RelayCommand(param => ViewBranchDetails(param as Branch));
            SyncBranchesCommand = new RelayCommand(async () => await SyncBranchesAsync());
            SearchCommand = new RelayCommand(async () => await SearchBranchesAsync());
            RefreshCommand = new RelayCommand(async () => await LoadBranchesAsync());

            _ = LoadBranchesAsync();
        }

        // Propriétés
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public string RegionFilter
        {
            get => _regionFilter;
            set
            {
                SetProperty(ref _regionFilter, value);
                _ = SearchBranchesAsync();
            }
        }

        public string StatusFilter
        {
            get => _statusFilter;
            set
            {
                SetProperty(ref _statusFilter, value);
                _ = SearchBranchesAsync();
            }
        }

        public int TotalBranches
        {
            get => _totalBranches;
            set => SetProperty(ref _totalBranches, value);
        }

        public int ActiveBranches
        {
            get => _activeBranches;
            set => SetProperty(ref _activeBranches, value);
        }

        public decimal TotalRevenue
        {
            get => _totalRevenue;
            set => SetProperty(ref _totalRevenue, value);
        }

        public int TotalEmployees
        {
            get => _totalEmployees;
            set => SetProperty(ref _totalEmployees, value);
        }

        public decimal AveragePerformance
        {
            get => _averagePerformance;
            set => SetProperty(ref _averagePerformance, value);
        }

        public bool IsEmpty => !IsBusy && Branches.Count == 0;

        // Collections
        public ObservableCollection<Branch> Branches { get; }

        // Commandes
        public ICommand AddBranchCommand { get; }
        public ICommand EditBranchCommand { get; }
        public ICommand ViewBranchDetailsCommand { get; }
        public ICommand SyncBranchesCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadBranchesAsync()
        {
            try
            {
                IsBusy = true;

                // TODO: Charger depuis la base de données
                await Task.Delay(500); // Simulation

                Branches.Clear();

                // Données d'exemple
                var sampleBranches = new[]
                {
                    new Branch
                    {
                        Id = 1,
                        Code = "ALG001",
                        Name = "Filiale Alger Centre",
                        Address = "Rue Didouche Mourad, Alger",
                        Region = "North",
                        ManagerName = "Ahmed Benali",
                        Phone = "021-123-456",
                        Status = "Active",
                        MonthlyRevenue = 450000,
                        EmployeeCount = 12,
                        Performance = 95.5m
                    },
                    new Branch
                    {
                        Id = 2,
                        Code = "ORN001",
                        Name = "Filiale Oran",
                        Address = "Boulevard de la Soummam, Oran",
                        Region = "West",
                        ManagerName = "Fatima Zohra",
                        Phone = "041-789-123",
                        Status = "Active",
                        MonthlyRevenue = 380000,
                        EmployeeCount = 10,
                        Performance = 88.2m
                    },
                    new Branch
                    {
                        Id = 3,
                        Code = "CON001",
                        Name = "Filiale Constantine",
                        Address = "Rue Larbi Ben M'hidi, Constantine",
                        Region = "East",
                        ManagerName = "Mohamed Khelil",
                        Phone = "031-456-789",
                        Status = "Active",
                        MonthlyRevenue = 320000,
                        EmployeeCount = 8,
                        Performance = 82.7m
                    },
                    new Branch
                    {
                        Id = 4,
                        Code = "ANN001",
                        Name = "Filiale Annaba",
                        Address = "Avenue de l'ALN, Annaba",
                        Region = "East",
                        ManagerName = "Leila Mansouri",
                        Phone = "038-654-321",
                        Status = "Construction",
                        MonthlyRevenue = 0,
                        EmployeeCount = 3,
                        Performance = 0
                    },
                    new Branch
                    {
                        Id = 5,
                        Code = "SET001",
                        Name = "Filiale Sétif",
                        Address = "Rue 8 Mai 1945, Sétif",
                        Region = "East",
                        ManagerName = "Karim Boudjelal",
                        Phone = "036-987-654",
                        Status = "Active",
                        MonthlyRevenue = 280000,
                        EmployeeCount = 7,
                        Performance = 76.3m
                    },
                    new Branch
                    {
                        Id = 6,
                        Code = "BLI001",
                        Name = "Filiale Blida",
                        Address = "Boulevard Boumediene, Blida",
                        Region = "Center",
                        ManagerName = "Nadia Hamidi",
                        Phone = "025-147-258",
                        Status = "Inactive",
                        MonthlyRevenue = 0,
                        EmployeeCount = 0,
                        Performance = 0
                    }
                };

                foreach (var branch in sampleBranches)
                {
                    Branches.Add(branch);
                }

                CalculateStatistics();
                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des filiales: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void CalculateStatistics()
        {
            TotalBranches = Branches.Count;
            ActiveBranches = Branches.Count(b => b.Status == "Active");
            TotalRevenue = Branches.Sum(b => b.MonthlyRevenue);
            TotalEmployees = Branches.Sum(b => b.EmployeeCount);
            AveragePerformance = Branches.Where(b => b.Status == "Active").Average(b => b.Performance);
        }

        private async Task SearchBranchesAsync()
        {
            try
            {
                // TODO: Implémenter la recherche dans la base de données
                await Task.Delay(200); // Simulation
                await LoadBranchesAsync();
                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void AddBranch()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'ajout de filiale en cours de développement.");
        }

        private void EditBranch(Branch branch)
        {
            if (branch == null) return;
            MessageBoxHelper.ShowInfo($"Modification de la filiale '{branch.Name}' en cours de développement.");
        }

        private void ViewBranchDetails(Branch branch)
        {
            if (branch == null) return;
            MessageBoxHelper.ShowInfo($"Affichage des détails de la filiale '{branch.Name}' en cours de développement.");
        }

        private async Task SyncBranchesAsync()
        {
            try
            {
                IsBusy = true;
                await Task.Delay(2000); // Simulation
                await LoadBranchesAsync();
                MessageBoxHelper.ShowSuccess("Synchronisation des filiales terminée avec succès.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la synchronisation: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }

    // Modèle pour les filiales
    public class Branch
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Region { get; set; }
        public string ManagerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Status { get; set; } // Active, Inactive, Construction
        public decimal MonthlyRevenue { get; set; }
        public int EmployeeCount { get; set; }
        public decimal Performance { get; set; }
        public DateTime OpeningDate { get; set; }
        public string ImagePath { get; set; }
        public string Description { get; set; }
    }
}
