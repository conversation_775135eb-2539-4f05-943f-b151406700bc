using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class BranchesViewModel : BaseViewModel
    {
        private string _searchText;
        private string _regionFilter = "All";
        private string _statusFilter = "All";
        private int _totalBranches;
        private int _activeBranches;
        private decimal _totalRevenue;
        private int _totalEmployees;
        private decimal _averagePerformance;

        public BranchesViewModel()
        {
            Branches = new ObservableCollection<Branch>();

            // Commandes
            AddBranchCommand = new RelayCommand(AddBranch);
            EditBranchCommand = new RelayCommand(param => EditBranch(param as Branch));
            ViewBranchDetailsCommand = new RelayCommand(param => ViewBranchDetails(param as Branch));
            SyncBranchesCommand = new RelayCommand(async () => await SyncBranchesAsync());
            SearchCommand = new RelayCommand(async () => await SearchBranchesAsync());
            RefreshCommand = new RelayCommand(async () => await LoadBranchesAsync());

            _ = LoadBranchesAsync();
        }

        // Propriétés
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public string RegionFilter
        {
            get => _regionFilter;
            set
            {
                SetProperty(ref _regionFilter, value);
                _ = SearchBranchesAsync();
            }
        }

        public string StatusFilter
        {
            get => _statusFilter;
            set
            {
                SetProperty(ref _statusFilter, value);
                _ = SearchBranchesAsync();
            }
        }

        public int TotalBranches
        {
            get => _totalBranches;
            set => SetProperty(ref _totalBranches, value);
        }

        public int ActiveBranches
        {
            get => _activeBranches;
            set => SetProperty(ref _activeBranches, value);
        }

        public decimal TotalRevenue
        {
            get => _totalRevenue;
            set => SetProperty(ref _totalRevenue, value);
        }

        public int TotalEmployees
        {
            get => _totalEmployees;
            set => SetProperty(ref _totalEmployees, value);
        }

        public decimal AveragePerformance
        {
            get => _averagePerformance;
            set => SetProperty(ref _averagePerformance, value);
        }

        public bool IsEmpty => !IsBusy && Branches.Count == 0;

        // Collections
        public ObservableCollection<Branch> Branches { get; }

        // Commandes
        public ICommand AddBranchCommand { get; }
        public ICommand EditBranchCommand { get; }
        public ICommand ViewBranchDetailsCommand { get; }
        public ICommand SyncBranchesCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadBranchesAsync()
        {
            try
            {
                IsBusy = true;

                // Charger toutes les filiales
                var allBranches = await GetAllBranchesDataAsync();

                Branches.Clear();
                foreach (var branch in allBranches)
                {
                    Branches.Add(branch);
                }

                CalculateStatistics();
                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des filiales: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private void CalculateStatistics()
        {
            TotalBranches = Branches.Count;
            ActiveBranches = Branches.Count(b => b.Status == "Active");
            TotalRevenue = Branches.Sum(b => b.MonthlyRevenue);
            TotalEmployees = Branches.Sum(b => b.EmployeeCount);
            AveragePerformance = Branches.Where(b => b.Status == "Active").Average(b => b.Performance);
        }

        private async Task SearchBranchesAsync()
        {
            try
            {
                IsBusy = true;
                await Task.Delay(300); // Simulation de recherche

                // Filtrer les filiales selon les critères
                var allBranches = await GetAllBranchesDataAsync();
                var filteredBranches = allBranches.AsEnumerable();

                // Filtrer par texte de recherche
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    var searchLower = SearchText.ToLower();
                    filteredBranches = filteredBranches.Where(b =>
                        b.Name.ToLower().Contains(searchLower) ||
                        b.Code.ToLower().Contains(searchLower) ||
                        b.Address.ToLower().Contains(searchLower) ||
                        b.ManagerName.ToLower().Contains(searchLower));
                }

                // Filtrer par région
                if (RegionFilter != "All")
                {
                    filteredBranches = filteredBranches.Where(b => b.Region == RegionFilter);
                }

                // Filtrer par statut
                if (StatusFilter != "All")
                {
                    filteredBranches = filteredBranches.Where(b => b.Status == StatusFilter);
                }

                // Mettre à jour la collection
                Branches.Clear();
                foreach (var branch in filteredBranches)
                {
                    Branches.Add(branch);
                }

                CalculateStatistics();
                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task<List<Branch>> GetAllBranchesDataAsync()
        {
            // Simuler le chargement de toutes les filiales
            await Task.Delay(100);

            return new List<Branch>
            {
                new Branch
                {
                    Id = 1,
                    Code = "ALG001",
                    Name = "Filiale Alger Centre",
                    Address = "Rue Didouche Mourad, Alger",
                    Region = "North",
                    ManagerName = "Ahmed Benali",
                    Phone = "021-123-456",
                    Status = "Active",
                    MonthlyRevenue = 450000,
                    EmployeeCount = 12,
                    Performance = 95.5m,
                    OpeningDate = DateTime.Now.AddYears(-2)
                },
                new Branch
                {
                    Id = 2,
                    Code = "ORN001",
                    Name = "Filiale Oran",
                    Address = "Boulevard de la Soummam, Oran",
                    Region = "West",
                    ManagerName = "Fatima Zohra",
                    Phone = "041-789-123",
                    Status = "Active",
                    MonthlyRevenue = 380000,
                    EmployeeCount = 10,
                    Performance = 88.2m,
                    OpeningDate = DateTime.Now.AddYears(-1)
                },
                new Branch
                {
                    Id = 3,
                    Code = "CON001",
                    Name = "Filiale Constantine",
                    Address = "Avenue Aouati Mostefa, Constantine",
                    Region = "East",
                    ManagerName = "Karim Messaoudi",
                    Phone = "031-456-789",
                    Status = "Active",
                    MonthlyRevenue = 320000,
                    EmployeeCount = 8,
                    Performance = 82.7m,
                    OpeningDate = DateTime.Now.AddMonths(-8)
                },
                new Branch
                {
                    Id = 4,
                    Code = "ANN001",
                    Name = "Filiale Annaba",
                    Address = "Rue de la Révolution, Annaba",
                    Region = "East",
                    ManagerName = "Leila Benaissa",
                    Phone = "038-321-654",
                    Status = "Construction",
                    MonthlyRevenue = 0,
                    EmployeeCount = 0,
                    Performance = 0,
                    OpeningDate = DateTime.Now.AddMonths(2)
                },
                new Branch
                {
                    Id = 5,
                    Code = "SET001",
                    Name = "Filiale Sétif",
                    Address = "Boulevard du 1er Novembre, Sétif",
                    Region = "East",
                    ManagerName = "Mohamed Larbi",
                    Phone = "036-987-321",
                    Status = "Active",
                    MonthlyRevenue = 280000,
                    EmployeeCount = 7,
                    Performance = 75.3m,
                    OpeningDate = DateTime.Now.AddMonths(-6)
                }
            };
        }

        private void AddBranch()
        {
            try
            {
                // Créer une nouvelle filiale avec des valeurs par défaut
                var newBranch = new Branch
                {
                    Code = $"BR{DateTime.Now:yyyyMMdd}{Branches.Count + 1:D3}",
                    Name = "Nouvelle Filiale",
                    Address = "Adresse à définir",
                    Region = "North",
                    ManagerName = "Manager à assigner",
                    Phone = "000-000-000",
                    Status = "Construction",
                    MonthlyRevenue = 0,
                    EmployeeCount = 0,
                    Performance = 0,
                    OpeningDate = DateTime.Now.AddMonths(1)
                };

                // Simuler l'ajout à la base de données
                newBranch.Id = Branches.Count + 1;
                Branches.Add(newBranch);

                CalculateStatistics();
                OnPropertyChanged(nameof(IsEmpty));

                MessageBoxHelper.ShowSuccess($"Nouvelle filiale '{newBranch.Name}' ajoutée avec succès.\nCode: {newBranch.Code}");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'ajout de la filiale: {ex.Message}");
            }
        }

        private void EditBranch(Branch branch)
        {
            if (branch == null) return;

            try
            {
                var details = $"Modification de la filiale:\n\n";
                details += $"Code: {branch.Code}\n";
                details += $"Nom: {branch.Name}\n";
                details += $"Adresse: {branch.Address}\n";
                details += $"Manager: {branch.ManagerName}\n";
                details += $"Téléphone: {branch.Phone}\n";
                details += $"Statut: {branch.Status}\n";
                details += $"CA Mensuel: {branch.MonthlyRevenue:F0} DA\n";
                details += $"Employés: {branch.EmployeeCount}\n";
                details += $"Performance: {branch.Performance:F1}%\n\n";
                details += "Formulaire de modification en cours de développement.";

                MessageBoxHelper.ShowInfo(details);
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la modification: {ex.Message}");
            }
        }

        private void ViewBranchDetails(Branch branch)
        {
            if (branch == null) return;

            try
            {
                var details = $"Détails de la filiale:\n\n";
                details += $"📍 {branch.Name} ({branch.Code})\n";
                details += $"📧 {branch.Address}\n";
                details += $"👤 Manager: {branch.ManagerName}\n";
                details += $"📞 {branch.Phone}\n";
                details += $"🏷️ Statut: {branch.Status}\n";
                details += $"🌍 Région: {branch.Region}\n";
                details += $"💰 CA Mensuel: {branch.MonthlyRevenue:F0} DA\n";
                details += $"👥 Employés: {branch.EmployeeCount}\n";
                details += $"📊 Performance: {branch.Performance:F1}%\n";
                details += $"📅 Ouverture: {branch.OpeningDate:dd/MM/yyyy}\n\n";

                if (!string.IsNullOrEmpty(branch.Description))
                {
                    details += $"Description: {branch.Description}\n\n";
                }

                details += "Vue détaillée complète en cours de développement.";

                MessageBoxHelper.ShowInfo(details);
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de l'affichage des détails: {ex.Message}");
            }
        }

        private async Task SyncBranchesAsync()
        {
            try
            {
                IsBusy = true;
                await Task.Delay(2000); // Simulation
                await LoadBranchesAsync();
                MessageBoxHelper.ShowSuccess("Synchronisation des filiales terminée avec succès.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la synchronisation: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }
    }

    // Modèle pour les filiales
    public class Branch
    {
        public int Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Region { get; set; }
        public string ManagerName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string Status { get; set; } // Active, Inactive, Construction
        public decimal MonthlyRevenue { get; set; }
        public int EmployeeCount { get; set; }
        public decimal Performance { get; set; }
        public DateTime OpeningDate { get; set; }
        public string ImagePath { get; set; }
        public string Description { get; set; }
    }
}
