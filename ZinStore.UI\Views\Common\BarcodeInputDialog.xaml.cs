using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.Views.Common
{
    public partial class BarcodeInputDialog : Window, INotifyPropertyChanged
    {
        private string _scannedBarcode;
        public string ScannedBarcode
        {
            get => _scannedBarcode;
            set
            {
                _scannedBarcode = value;
                OnPropertyChanged();
            }
        }

        public ICommand SearchCommand { get; }

        public BarcodeInputDialog()
        {
            InitializeComponent();
            DataContext = this;
            
            SearchCommand = new RelayCommand(Search, CanSearch);
            
            // Focus sur le champ de saisie
            Loaded += (s, e) => BarcodeTextBox.Focus();
        }

        private bool CanSearch()
        {
            return !string.IsNullOrWhiteSpace(ScannedBarcode);
        }

        private void Search()
        {
            if (!string.IsNullOrWhiteSpace(ScannedBarcode))
            {
                DialogResult = true;
                Close();
            }
        }

        private void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && CanSearch())
            {
                Search();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
