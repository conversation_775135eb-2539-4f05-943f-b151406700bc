-- =====================================================
-- Script de création des tables ZinStore (Partie 2)
-- Tables: Achats, Finance, Configuration
-- Version: MySQL 8.0+
-- =====================================================

USE zinstore;

-- =====================================================
-- 9. TABLE DES ACHATS
-- =====================================================

CREATE TABLE achats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_commande VARCHAR(50) UNIQUE NOT NULL,
    fournisseur_id INT NOT NULL,
    utilisateur_id INT NOT NULL,
    date_commande DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_livraison_prevue DATE,
    date_livraison_reelle DATE,
    sous_total DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    taux_tva DECIMAL(5,2) DEFAULT 19.00,
    montant_tva DECIMAL(15,2) GENERATED ALWAYS AS (sous_total * taux_tva / 100) STORED,
    frais_transport DECIMAL(15,2) DEFAULT 0.00,
    remise DECIMAL(15,2) DEFAULT 0.00,
    montant_total DECIMAL(15,2) GENERATED ALWAYS AS (sous_total + (sous_total * taux_tva / 100) + frais_transport - remise) STORED,
    montant_paye DECIMAL(15,2) DEFAULT 0.00,
    montant_restant DECIMAL(15,2) GENERATED ALWAYS AS (sous_total + (sous_total * taux_tva / 100) + frais_transport - remise - montant_paye) STORED,
    statut_commande ENUM('En attente', 'Confirmé', 'Expédié', 'Livré', 'Annulé') NOT NULL DEFAULT 'En attente',
    statut_paiement ENUM('En attente', 'Partiel', 'Payé', 'Annulé') NOT NULL DEFAULT 'En attente',
    mode_paiement ENUM('Espèces', 'Carte', 'Chèque', 'Virement', 'Crédit') DEFAULT 'Virement',
    notes TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id) ON DELETE RESTRICT,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE RESTRICT,
    INDEX idx_numero_commande (numero_commande),
    INDEX idx_fournisseur (fournisseur_id),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_date_commande (date_commande),
    INDEX idx_statut_commande (statut_commande),
    INDEX idx_statut_paiement (statut_paiement),
    INDEX idx_montant_total (montant_total)
) ENGINE=InnoDB COMMENT='Table des achats (en-têtes)';

-- =====================================================
-- 10. TABLE DES DÉTAILS D'ACHAT
-- =====================================================

CREATE TABLE achats_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    achat_id INT NOT NULL,
    produit_id INT NOT NULL,
    quantite_commandee INT NOT NULL,
    quantite_recue INT DEFAULT 0,
    prix_unitaire DECIMAL(15,2) NOT NULL,
    sous_total DECIMAL(15,2) GENERATED ALWAYS AS (quantite_commandee * prix_unitaire) STORED,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (achat_id) REFERENCES achats(id) ON DELETE CASCADE,
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE RESTRICT,
    INDEX idx_achat (achat_id),
    INDEX idx_produit (produit_id),
    INDEX idx_quantite_commandee (quantite_commandee),
    INDEX idx_prix_unitaire (prix_unitaire)
) ENGINE=InnoDB COMMENT='Table des détails d\'achat (lignes)';

-- =====================================================
-- 11. TABLE DES MOUVEMENTS DE STOCK
-- =====================================================

CREATE TABLE mouvements_stock (
    id INT AUTO_INCREMENT PRIMARY KEY,
    produit_id INT NOT NULL,
    type_mouvement ENUM('Entrée', 'Sortie', 'Ajustement', 'Transfert') NOT NULL,
    quantite INT NOT NULL,
    quantite_avant INT NOT NULL,
    quantite_apres INT GENERATED ALWAYS AS (
        CASE 
            WHEN type_mouvement IN ('Entrée', 'Ajustement') AND quantite > 0 THEN quantite_avant + quantite
            WHEN type_mouvement IN ('Sortie', 'Ajustement') AND quantite < 0 THEN quantite_avant + quantite
            ELSE quantite_avant - ABS(quantite)
        END
    ) STORED,
    prix_unitaire DECIMAL(15,2),
    valeur_mouvement DECIMAL(15,2) GENERATED ALWAYS AS (ABS(quantite) * IFNULL(prix_unitaire, 0)) STORED,
    reference_type ENUM('Vente', 'Achat', 'Ajustement', 'Transfert', 'Inventaire') NOT NULL,
    reference_id INT,
    utilisateur_id INT NOT NULL,
    motif VARCHAR(200),
    date_mouvement DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (produit_id) REFERENCES produits(id) ON DELETE RESTRICT,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE RESTRICT,
    INDEX idx_produit (produit_id),
    INDEX idx_type_mouvement (type_mouvement),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_date_mouvement (date_mouvement)
) ENGINE=InnoDB COMMENT='Table des mouvements de stock';

-- =====================================================
-- 12. TABLE DES REVENUS
-- =====================================================

CREATE TABLE revenus (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description VARCHAR(200) NOT NULL,
    montant DECIMAL(15,2) NOT NULL,
    categorie ENUM('Vente', 'Service', 'Intérêt', 'Autre') NOT NULL DEFAULT 'Vente',
    reference_type ENUM('Vente', 'Service', 'Autre') DEFAULT 'Vente',
    reference_id INT,
    utilisateur_id INT NOT NULL,
    date_revenu DATE NOT NULL,
    notes TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE RESTRICT,
    INDEX idx_categorie (categorie),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_date_revenu (date_revenu),
    INDEX idx_montant (montant)
) ENGINE=InnoDB COMMENT='Table des revenus';

-- =====================================================
-- 13. TABLE DES DÉPENSES
-- =====================================================

CREATE TABLE depenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description VARCHAR(200) NOT NULL,
    montant DECIMAL(15,2) NOT NULL,
    categorie ENUM('Achat', 'Salaire', 'Loyer', 'Électricité', 'Transport', 'Marketing', 'Autre') NOT NULL DEFAULT 'Autre',
    reference_type ENUM('Achat', 'Facture', 'Autre') DEFAULT 'Autre',
    reference_id INT,
    fournisseur_id INT,
    utilisateur_id INT NOT NULL,
    date_depense DATE NOT NULL,
    mode_paiement ENUM('Espèces', 'Carte', 'Chèque', 'Virement') DEFAULT 'Espèces',
    numero_piece VARCHAR(50),
    notes TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (fournisseur_id) REFERENCES fournisseurs(id) ON DELETE SET NULL,
    FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs(id) ON DELETE RESTRICT,
    INDEX idx_categorie (categorie),
    INDEX idx_reference (reference_type, reference_id),
    INDEX idx_fournisseur (fournisseur_id),
    INDEX idx_utilisateur (utilisateur_id),
    INDEX idx_date_depense (date_depense),
    INDEX idx_montant (montant)
) ENGINE=InnoDB COMMENT='Table des dépenses';

-- =====================================================
-- 14. TABLE DES COMPTES GÉNÉRAUX
-- =====================================================

CREATE TABLE comptes_generaux (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_compte VARCHAR(20) UNIQUE NOT NULL,
    nom_compte VARCHAR(100) NOT NULL,
    type_compte ENUM('Actif', 'Passif', 'Capitaux', 'Charge', 'Produit') NOT NULL,
    sous_type VARCHAR(50),
    compte_parent_id INT,
    niveau INT NOT NULL DEFAULT 1,
    est_actif BOOLEAN NOT NULL DEFAULT TRUE,
    description TEXT,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (compte_parent_id) REFERENCES comptes_generaux(id) ON DELETE SET NULL,
    INDEX idx_numero_compte (numero_compte),
    INDEX idx_type_compte (type_compte),
    INDEX idx_compte_parent (compte_parent_id),
    INDEX idx_niveau (niveau),
    INDEX idx_est_actif (est_actif)
) ENGINE=InnoDB COMMENT='Table du plan comptable';

-- =====================================================
-- 15. TABLE DES PARAMÈTRES
-- =====================================================

CREATE TABLE parametres (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cle_parametre VARCHAR(100) UNIQUE NOT NULL,
    valeur_parametre TEXT,
    type_parametre ENUM('String', 'Integer', 'Decimal', 'Boolean', 'Date') NOT NULL DEFAULT 'String',
    categorie VARCHAR(50) NOT NULL DEFAULT 'Général',
    description VARCHAR(200),
    est_modifiable BOOLEAN NOT NULL DEFAULT TRUE,
    date_creation DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    date_modification DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_cle_parametre (cle_parametre),
    INDEX idx_categorie (categorie),
    INDEX idx_est_modifiable (est_modifiable)
) ENGINE=InnoDB COMMENT='Table des paramètres système';
