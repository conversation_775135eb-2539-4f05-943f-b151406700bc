-- =====================================================
-- Script de création des vues ZinStore
-- Vues pour faciliter les requêtes et rapports
-- Version: MySQL 8.0+
-- =====================================================

USE zinstore;

-- =====================================================
-- VUE 1: Vue complète des produits avec stock
-- =====================================================

CREATE OR REPLACE VIEW v_produits_stock AS
SELECT 
    p.id,
    p.nom,
    p.description,
    p.code_produit,
    p.code_barre,
    p.prix_achat,
    p.prix_vente,
    p.marge_benefice,
    p.unite_mesure,
    p.quantite_par_unite,
    p.quantite_carton,
    p.stock_minimum,
    p.stock_maximum,
    p.date_expiration,
    p.a_expiration,
    p.est_actif,
    c.nom AS categorie_nom,
    f.nom AS fournisseur_nom,
    s.quantite_actuelle,
    s.quantite_reservee,
    s.quantite_disponible,
    s.valeur_stock,
    s.emplacement,
    CASE 
        WHEN s.quantite_actuelle <= p.stock_minimum THEN 'Rupture'
        WHEN s.quantite_actuelle <= (p.stock_minimum * 1.5) THEN 'Faible'
        WHEN s.quantite_actuelle >= p.stock_maximum THEN 'Excès'
        ELSE 'Normal'
    END AS statut_stock,
    CASE 
        WHEN p.date_expiration IS NOT NULL AND p.date_expiration <= CURDATE() THEN 'Expiré'
        WHEN p.date_expiration IS NOT NULL AND p.date_expiration <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'Expire bientôt'
        ELSE 'OK'
    END AS statut_expiration,
    p.date_creation,
    p.date_modification
FROM produits p
LEFT JOIN categories c ON p.categorie_id = c.id
LEFT JOIN fournisseurs f ON p.fournisseur_id = f.id
LEFT JOIN stock s ON p.id = s.produit_id;

-- =====================================================
-- VUE 2: Vue des ventes avec détails client
-- =====================================================

CREATE OR REPLACE VIEW v_ventes_details AS
SELECT 
    v.id,
    v.numero_facture,
    v.date_vente,
    v.sous_total,
    v.taux_tva,
    v.montant_tva,
    v.remise,
    v.montant_total,
    v.montant_paye,
    v.montant_restant,
    v.statut_paiement,
    v.mode_paiement,
    v.notes,
    COALESCE(c.nom_complet, 'Client comptoir') AS client_nom,
    c.telephone AS client_telephone,
    c.email AS client_email,
    u.nom_complet AS vendeur_nom,
    COUNT(vd.id) AS nombre_articles,
    SUM(vd.quantite) AS quantite_totale
FROM ventes v
LEFT JOIN clients c ON v.client_id = c.id
LEFT JOIN utilisateurs u ON v.utilisateur_id = u.id
LEFT JOIN ventes_details vd ON v.id = vd.vente_id
GROUP BY v.id, v.numero_facture, v.date_vente, v.sous_total, v.taux_tva, 
         v.montant_tva, v.remise, v.montant_total, v.montant_paye, 
         v.montant_restant, v.statut_paiement, v.mode_paiement, v.notes,
         c.nom_complet, c.telephone, c.email, u.nom_complet;

-- =====================================================
-- VUE 3: Vue des achats avec détails fournisseur
-- =====================================================

CREATE OR REPLACE VIEW v_achats_details AS
SELECT 
    a.id,
    a.numero_commande,
    a.date_commande,
    a.date_livraison_prevue,
    a.date_livraison_reelle,
    a.sous_total,
    a.taux_tva,
    a.montant_tva,
    a.frais_transport,
    a.remise,
    a.montant_total,
    a.montant_paye,
    a.montant_restant,
    a.statut_commande,
    a.statut_paiement,
    a.mode_paiement,
    a.notes,
    f.nom AS fournisseur_nom,
    f.telephone AS fournisseur_telephone,
    f.email AS fournisseur_email,
    u.nom_complet AS acheteur_nom,
    COUNT(ad.id) AS nombre_articles,
    SUM(ad.quantite_commandee) AS quantite_commandee_totale,
    SUM(ad.quantite_recue) AS quantite_recue_totale,
    CASE 
        WHEN SUM(ad.quantite_recue) = 0 THEN 'Non reçu'
        WHEN SUM(ad.quantite_recue) < SUM(ad.quantite_commandee) THEN 'Partiel'
        WHEN SUM(ad.quantite_recue) = SUM(ad.quantite_commandee) THEN 'Complet'
        ELSE 'Excédent'
    END AS statut_reception
FROM achats a
LEFT JOIN fournisseurs f ON a.fournisseur_id = f.id
LEFT JOIN utilisateurs u ON a.utilisateur_id = u.id
LEFT JOIN achats_details ad ON a.id = ad.achat_id
GROUP BY a.id, a.numero_commande, a.date_commande, a.date_livraison_prevue,
         a.date_livraison_reelle, a.sous_total, a.taux_tva, a.montant_tva,
         a.frais_transport, a.remise, a.montant_total, a.montant_paye,
         a.montant_restant, a.statut_commande, a.statut_paiement,
         a.mode_paiement, a.notes, f.nom, f.telephone, f.email, u.nom_complet;

-- =====================================================
-- VUE 4: Vue des mouvements de stock avec détails
-- =====================================================

CREATE OR REPLACE VIEW v_mouvements_stock_details AS
SELECT 
    ms.id,
    ms.type_mouvement,
    ms.quantite,
    ms.quantite_avant,
    ms.quantite_apres,
    ms.prix_unitaire,
    ms.valeur_mouvement,
    ms.reference_type,
    ms.reference_id,
    ms.motif,
    ms.date_mouvement,
    p.nom AS produit_nom,
    p.code_produit,
    p.unite_mesure,
    u.nom_complet AS utilisateur_nom,
    CASE ms.reference_type
        WHEN 'Vente' THEN (SELECT numero_facture FROM ventes WHERE id = ms.reference_id)
        WHEN 'Achat' THEN (SELECT numero_commande FROM achats WHERE id = ms.reference_id)
        ELSE CONCAT(ms.reference_type, ' #', ms.reference_id)
    END AS reference_numero
FROM mouvements_stock ms
LEFT JOIN produits p ON ms.produit_id = p.id
LEFT JOIN utilisateurs u ON ms.utilisateur_id = u.id;

-- =====================================================
-- VUE 5: Vue des statistiques de vente par produit
-- =====================================================

CREATE OR REPLACE VIEW v_statistiques_ventes_produits AS
SELECT 
    p.id AS produit_id,
    p.nom AS produit_nom,
    p.code_produit,
    p.prix_vente,
    p.prix_achat,
    c.nom AS categorie_nom,
    COUNT(vd.id) AS nombre_ventes,
    SUM(vd.quantite) AS quantite_vendue,
    SUM(vd.sous_total) AS chiffre_affaires,
    AVG(vd.prix_unitaire) AS prix_moyen,
    SUM(vd.quantite * p.prix_achat) AS cout_total,
    SUM(vd.sous_total) - SUM(vd.quantite * p.prix_achat) AS benefice_total,
    CASE 
        WHEN SUM(vd.quantite * p.prix_achat) > 0 
        THEN ((SUM(vd.sous_total) - SUM(vd.quantite * p.prix_achat)) / SUM(vd.quantite * p.prix_achat)) * 100
        ELSE 0 
    END AS marge_benefice_reel,
    MAX(v.date_vente) AS derniere_vente,
    s.quantite_actuelle AS stock_actuel
FROM produits p
LEFT JOIN categories c ON p.categorie_id = c.id
LEFT JOIN ventes_details vd ON p.id = vd.produit_id
LEFT JOIN ventes v ON vd.vente_id = v.id AND v.statut_paiement != 'Annulé'
LEFT JOIN stock s ON p.id = s.produit_id
WHERE p.est_actif = TRUE
GROUP BY p.id, p.nom, p.code_produit, p.prix_vente, p.prix_achat, 
         c.nom, s.quantite_actuelle;

-- =====================================================
-- VUE 6: Vue des statistiques financières par période
-- =====================================================

CREATE OR REPLACE VIEW v_statistiques_financieres AS
SELECT 
    DATE(date_transaction) AS date_transaction,
    YEAR(date_transaction) AS annee,
    MONTH(date_transaction) AS mois,
    DAY(date_transaction) AS jour,
    DAYNAME(date_transaction) AS jour_semaine,
    SUM(CASE WHEN type_transaction = 'Revenu' THEN montant ELSE 0 END) AS total_revenus,
    SUM(CASE WHEN type_transaction = 'Dépense' THEN montant ELSE 0 END) AS total_depenses,
    SUM(CASE WHEN type_transaction = 'Revenu' THEN montant ELSE -montant END) AS benefice_net,
    COUNT(CASE WHEN type_transaction = 'Revenu' THEN 1 END) AS nombre_revenus,
    COUNT(CASE WHEN type_transaction = 'Dépense' THEN 1 END) AS nombre_depenses
FROM (
    SELECT date_revenu AS date_transaction, montant, 'Revenu' AS type_transaction FROM revenus
    UNION ALL
    SELECT date_depense AS date_transaction, montant, 'Dépense' AS type_transaction FROM depenses
) AS transactions
GROUP BY DATE(date_transaction), YEAR(date_transaction), MONTH(date_transaction), 
         DAY(date_transaction), DAYNAME(date_transaction)
ORDER BY date_transaction DESC;

-- =====================================================
-- VUE 7: Vue des clients avec statistiques
-- =====================================================

CREATE OR REPLACE VIEW v_clients_statistiques AS
SELECT 
    c.id,
    c.nom,
    c.prenom,
    c.nom_complet,
    c.telephone,
    c.email,
    c.adresse,
    c.ville,
    c.type_client,
    c.numero_client,
    c.credit_limite,
    c.solde_compte,
    c.est_actif,
    c.date_creation,
    COUNT(v.id) AS nombre_achats,
    COALESCE(SUM(v.montant_total), 0) AS total_achats,
    COALESCE(AVG(v.montant_total), 0) AS panier_moyen,
    MAX(v.date_vente) AS dernier_achat,
    DATEDIFF(CURDATE(), MAX(v.date_vente)) AS jours_depuis_dernier_achat,
    CASE 
        WHEN MAX(v.date_vente) IS NULL THEN 'Nouveau'
        WHEN DATEDIFF(CURDATE(), MAX(v.date_vente)) <= 30 THEN 'Actif'
        WHEN DATEDIFF(CURDATE(), MAX(v.date_vente)) <= 90 THEN 'Inactif'
        ELSE 'Perdu'
    END AS statut_client
FROM clients c
LEFT JOIN ventes v ON c.id = v.client_id AND v.statut_paiement != 'Annulé'
GROUP BY c.id, c.nom, c.prenom, c.nom_complet, c.telephone, c.email,
         c.adresse, c.ville, c.type_client, c.numero_client, 
         c.credit_limite, c.solde_compte, c.est_actif, c.date_creation;
