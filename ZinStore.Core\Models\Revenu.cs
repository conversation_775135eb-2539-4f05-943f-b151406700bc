using System;
using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les revenus
    /// </summary>
    public class Revenu : BaseEntity
    {
        [Required(ErrorMessage = "Le numéro de référence est obligatoire")]
        [StringLength(50)]
        public string NumeroReference { get; set; }

        [Required]
        public DateTime DateRevenu { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "La catégorie de revenu est obligatoire")]
        [StringLength(100)]
        public string CategorieRevenu { get; set; }

        [Required(ErrorMessage = "La description est obligatoire")]
        [StringLength(200)]
        public string Description { get; set; }

        [Required]
        public decimal Montant { get; set; }

        public decimal MontantTVA { get; set; } = 0;

        public decimal MontantTotal { get; set; }

        [StringLength(50)]
        public string ModePaiement { get; set; }

        [StringLength(100)]
        public string Source { get; set; }

        public int? ClientId { get; set; }

        public int? VenteId { get; set; }

        public int UtilisateurId { get; set; }

        public bool EstValidee { get; set; } = false;

        public DateTime? DateValidation { get; set; }

        public int? UtilisateurValidation { get; set; }

        public string Notes { get; set; }

        [StringLength(200)]
        public string PieceJointe { get; set; }

        // Propriétés de navigation
        public virtual Client Client { get; set; }
        public virtual Vente Vente { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }
    }
}
