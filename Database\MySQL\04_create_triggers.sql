-- =====================================================
-- Script de création des triggers ZinStore
-- Gestion automatique des stocks et calculs
-- Version: MySQL 8.0+
-- =====================================================

USE zinstore;

-- Changer le délimiteur pour les triggers
DELIMITER $$

-- =====================================================
-- TRIGGER 1: Mise à jour automatique du sous-total des ventes
-- =====================================================

CREATE TRIGGER tr_ventes_details_insert_update_total
AFTER INSERT ON ventes_details
FOR EACH ROW
BEGIN
    UPDATE ventes 
    SET sous_total = (
        SELECT IFNULL(SUM(sous_total), 0) 
        FROM ventes_details 
        WHERE vente_id = NEW.vente_id
    )
    WHERE id = NEW.vente_id;
END$$

CREATE TRIGGER tr_ventes_details_update_update_total
AFTER UPDATE ON ventes_details
FOR EACH ROW
BEGIN
    UPDATE ventes 
    SET sous_total = (
        SELECT IFNULL(SUM(sous_total), 0) 
        FROM ventes_details 
        WHERE vente_id = NEW.vente_id
    )
    WHERE id = NEW.vente_id;
END$$

CREATE TRIGGER tr_ventes_details_delete_update_total
AFTER DELETE ON ventes_details
FOR EACH ROW
BEGIN
    UPDATE ventes 
    SET sous_total = (
        SELECT IFNULL(SUM(sous_total), 0) 
        FROM ventes_details 
        WHERE vente_id = OLD.vente_id
    )
    WHERE id = OLD.vente_id;
END$$

-- =====================================================
-- TRIGGER 2: Mise à jour automatique du sous-total des achats
-- =====================================================

CREATE TRIGGER tr_achats_details_insert_update_total
AFTER INSERT ON achats_details
FOR EACH ROW
BEGIN
    UPDATE achats 
    SET sous_total = (
        SELECT IFNULL(SUM(sous_total), 0) 
        FROM achats_details 
        WHERE achat_id = NEW.achat_id
    )
    WHERE id = NEW.achat_id;
END$$

CREATE TRIGGER tr_achats_details_update_update_total
AFTER UPDATE ON achats_details
FOR EACH ROW
BEGIN
    UPDATE achats 
    SET sous_total = (
        SELECT IFNULL(SUM(sous_total), 0) 
        FROM achats_details 
        WHERE achat_id = NEW.achat_id
    )
    WHERE id = NEW.achat_id;
END$$

CREATE TRIGGER tr_achats_details_delete_update_total
AFTER DELETE ON achats_details
FOR EACH ROW
BEGIN
    UPDATE achats 
    SET sous_total = (
        SELECT IFNULL(SUM(sous_total), 0) 
        FROM achats_details 
        WHERE achat_id = OLD.achat_id
    )
    WHERE id = OLD.achat_id;
END$$

-- =====================================================
-- TRIGGER 3: Gestion automatique du stock lors des ventes
-- =====================================================

CREATE TRIGGER tr_ventes_details_insert_update_stock
AFTER INSERT ON ventes_details
FOR EACH ROW
BEGIN
    -- Mettre à jour le stock
    UPDATE stock 
    SET quantite_actuelle = quantite_actuelle - NEW.quantite,
        date_derniere_sortie = NOW()
    WHERE produit_id = NEW.produit_id;
    
    -- Créer un mouvement de stock
    INSERT INTO mouvements_stock (
        produit_id, 
        type_mouvement, 
        quantite, 
        quantite_avant, 
        prix_unitaire,
        reference_type, 
        reference_id, 
        utilisateur_id,
        motif
    ) VALUES (
        NEW.produit_id,
        'Sortie',
        NEW.quantite,
        (SELECT quantite_actuelle + NEW.quantite FROM stock WHERE produit_id = NEW.produit_id),
        NEW.prix_unitaire,
        'Vente',
        NEW.vente_id,
        (SELECT utilisateur_id FROM ventes WHERE id = NEW.vente_id),
        CONCAT('Vente - Facture: ', (SELECT numero_facture FROM ventes WHERE id = NEW.vente_id))
    );
END$$

-- =====================================================
-- TRIGGER 4: Gestion automatique du stock lors des achats
-- =====================================================

CREATE TRIGGER tr_achats_details_update_stock_on_reception
AFTER UPDATE ON achats_details
FOR EACH ROW
BEGIN
    -- Si la quantité reçue a changé
    IF NEW.quantite_recue != OLD.quantite_recue THEN
        -- Calculer la différence
        SET @quantite_diff = NEW.quantite_recue - OLD.quantite_recue;
        
        -- Mettre à jour le stock
        UPDATE stock 
        SET quantite_actuelle = quantite_actuelle + @quantite_diff,
            date_derniere_entree = NOW()
        WHERE produit_id = NEW.produit_id;
        
        -- Créer un mouvement de stock
        INSERT INTO mouvements_stock (
            produit_id, 
            type_mouvement, 
            quantite, 
            quantite_avant, 
            prix_unitaire,
            reference_type, 
            reference_id, 
            utilisateur_id,
            motif
        ) VALUES (
            NEW.produit_id,
            'Entrée',
            @quantite_diff,
            (SELECT quantite_actuelle - @quantite_diff FROM stock WHERE produit_id = NEW.produit_id),
            NEW.prix_unitaire,
            'Achat',
            NEW.achat_id,
            (SELECT utilisateur_id FROM achats WHERE id = NEW.achat_id),
            CONCAT('Réception achat - Commande: ', (SELECT numero_commande FROM achats WHERE id = NEW.achat_id))
        );
    END IF;
END$$

-- =====================================================
-- TRIGGER 5: Création automatique d'un enregistrement stock pour nouveau produit
-- =====================================================

CREATE TRIGGER tr_produits_insert_create_stock
AFTER INSERT ON produits
FOR EACH ROW
BEGIN
    INSERT INTO stock (produit_id, quantite_actuelle, quantite_reservee)
    VALUES (NEW.id, 0, 0);
END$$

-- =====================================================
-- TRIGGER 6: Mise à jour automatique des revenus lors des ventes
-- =====================================================

CREATE TRIGGER tr_ventes_insert_create_revenu
AFTER INSERT ON ventes
FOR EACH ROW
BEGIN
    -- Créer un revenu automatiquement si la vente est payée
    IF NEW.statut_paiement = 'Payé' AND NEW.montant_total > 0 THEN
        INSERT INTO revenus (
            description,
            montant,
            categorie,
            reference_type,
            reference_id,
            utilisateur_id,
            date_revenu
        ) VALUES (
            CONCAT('Vente - Facture: ', NEW.numero_facture),
            NEW.montant_total,
            'Vente',
            'Vente',
            NEW.id,
            NEW.utilisateur_id,
            DATE(NEW.date_vente)
        );
    END IF;
END$$

CREATE TRIGGER tr_ventes_update_manage_revenu
AFTER UPDATE ON ventes
FOR EACH ROW
BEGIN
    -- Si le statut de paiement change vers "Payé"
    IF OLD.statut_paiement != 'Payé' AND NEW.statut_paiement = 'Payé' THEN
        -- Créer ou mettre à jour le revenu
        INSERT INTO revenus (
            description,
            montant,
            categorie,
            reference_type,
            reference_id,
            utilisateur_id,
            date_revenu
        ) VALUES (
            CONCAT('Vente - Facture: ', NEW.numero_facture),
            NEW.montant_total,
            'Vente',
            'Vente',
            NEW.id,
            NEW.utilisateur_id,
            DATE(NEW.date_vente)
        )
        ON DUPLICATE KEY UPDATE
            montant = NEW.montant_total,
            date_modification = NOW();
    END IF;
    
    -- Si le statut de paiement change depuis "Payé"
    IF OLD.statut_paiement = 'Payé' AND NEW.statut_paiement != 'Payé' THEN
        -- Supprimer le revenu correspondant
        DELETE FROM revenus 
        WHERE reference_type = 'Vente' AND reference_id = NEW.id;
    END IF;
END$$

-- =====================================================
-- TRIGGER 7: Mise à jour automatique des dépenses lors des achats
-- =====================================================

CREATE TRIGGER tr_achats_update_manage_depense
AFTER UPDATE ON achats
FOR EACH ROW
BEGIN
    -- Si le statut de paiement change vers "Payé"
    IF OLD.statut_paiement != 'Payé' AND NEW.statut_paiement = 'Payé' THEN
        -- Créer ou mettre à jour la dépense
        INSERT INTO depenses (
            description,
            montant,
            categorie,
            reference_type,
            reference_id,
            fournisseur_id,
            utilisateur_id,
            date_depense,
            mode_paiement,
            numero_piece
        ) VALUES (
            CONCAT('Achat - Commande: ', NEW.numero_commande),
            NEW.montant_total,
            'Achat',
            'Achat',
            NEW.id,
            NEW.fournisseur_id,
            NEW.utilisateur_id,
            DATE(NEW.date_commande),
            NEW.mode_paiement,
            NEW.numero_commande
        )
        ON DUPLICATE KEY UPDATE
            montant = NEW.montant_total,
            date_modification = NOW();
    END IF;
    
    -- Si le statut de paiement change depuis "Payé"
    IF OLD.statut_paiement = 'Payé' AND NEW.statut_paiement != 'Payé' THEN
        -- Supprimer la dépense correspondante
        DELETE FROM depenses 
        WHERE reference_type = 'Achat' AND reference_id = NEW.id;
    END IF;
END$$

-- Remettre le délimiteur par défaut
DELIMITER ;
