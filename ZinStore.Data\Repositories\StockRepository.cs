using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    public class StockRepository : BaseRepository<Stock>
    {
        public StockRepository(DatabaseContext context) : base(context, "Stock")
        {
        }

        public override async Task<IEnumerable<Stock>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT s.*, p.Nom as ProduitNom, p.CodeProduit
                FROM Stock s
                INNER JOIN Produits p ON s.ProduitId = p.Id
                WHERE s.EstSupprime = 0 
                AND (p.CodeProduit LIKE @SearchTerm OR p.Nom LIKE @SearchTerm)
                ORDER BY p.Nom";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Stock>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        public async Task<Stock> GetByProduitIdAsync(int produitId)
        {
            var sql = "SELECT * FROM Stock WHERE ProduitId = @ProduitId AND EstSupprime = 0";
            using (var connection = _context.GetConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<Stock>(sql, new { ProduitId = produitId });
            }
        }
    }
}
