using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZinStore.Core.Models;
using ZinStore.Data.Context;
using ZinStore.Data.Repositories;
using ZinStore.Business.Helpers;

namespace ZinStore.Business.Services
{
    public class CategorieService
    {
        private readonly CategorieRepository _categorieRepository;

        public CategorieService(DatabaseContext context)
        {
            _categorieRepository = new CategorieRepository(context);
        }

        public async Task<IEnumerable<Categorie>> GetAllCategoriesAsync()
        {
            return await _categorieRepository.GetAllAsync();
        }

        public async Task<Categorie> GetCategorieByIdAsync(int id)
        {
            return await _categorieRepository.GetByIdAsync(id);
        }

        public async Task<IEnumerable<Categorie>> SearchCategoriesAsync(string searchTerm)
        {
            return await _categorieRepository.SearchAsync(searchTerm);
        }

        public async Task<IEnumerable<Categorie>> GetActiveCategoriesAsync()
        {
            return await _categorieRepository.GetActiveCategoriesAsync();
        }

        public async Task<(bool Success, string Message, int CategorieId)> AddCategorieAsync(Categorie categorie)
        {
            try
            {
                var validationResult = ValidateCategorie(categorie);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage, 0);
                }

                CleanCategorieData(categorie);
                int categorieId = await _categorieRepository.AddAsync(categorie);
                return (true, "Catégorie ajoutée avec succès.", categorieId);
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de l'ajout de la catégorie: {ex.Message}", 0);
            }
        }

        public async Task<(bool Success, string Message)> UpdateCategorieAsync(Categorie categorie)
        {
            try
            {
                var validationResult = ValidateCategorie(categorie);
                if (!validationResult.IsValid)
                {
                    return (false, validationResult.ErrorMessage);
                }

                CleanCategorieData(categorie);
                bool success = await _categorieRepository.UpdateAsync(categorie);
                return success ? (true, "Catégorie mise à jour avec succès.") : (false, "Erreur lors de la mise à jour de la catégorie.");
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de la mise à jour de la catégorie: {ex.Message}");
            }
        }

        public async Task<(bool Success, string Message)> DeleteCategorieAsync(int categorieId)
        {
            try
            {
                // Vérifier si la catégorie existe
                var categorie = await _categorieRepository.GetByIdAsync(categorieId);
                if (categorie == null)
                {
                    return (false, "Catégorie introuvable.");
                }

                // TODO: Vérifier si la catégorie a des produits
                // if (await HasProducts(categorieId))
                // {
                //     return (false, "Impossible de supprimer cette catégorie car elle contient des produits.");
                // }

                // Supprimer la catégorie (suppression logique)
                bool success = await _categorieRepository.DeleteAsync(categorieId);
                return success ? (true, "Catégorie supprimée avec succès.") : (false, "Erreur lors de la suppression de la catégorie.");
            }
            catch (Exception ex)
            {
                return (false, $"Erreur lors de la suppression de la catégorie: {ex.Message}");
            }
        }

        public async Task<string> GenerateNextCodeAsync()
        {
            return await _categorieRepository.GenerateNextCodeAsync("CAT");
        }

        private (bool IsValid, string ErrorMessage) ValidateCategorie(Categorie categorie)
        {
            if (categorie == null)
                return (false, "Les données de la catégorie sont requises.");

            if (string.IsNullOrWhiteSpace(categorie.CodeCategorie))
                return (false, "Le code catégorie est obligatoire.");

            if (string.IsNullOrWhiteSpace(categorie.Nom))
                return (false, "Le nom de la catégorie est obligatoire.");

            if (!ValidationHelper.IsValidCode(categorie.CodeCategorie))
                return (false, "Le code catégorie contient des caractères non valides.");

            return (true, string.Empty);
        }

        private void CleanCategorieData(Categorie categorie)
        {
            categorie.CodeCategorie = ValidationHelper.CleanCode(categorie.CodeCategorie);
            categorie.Nom = categorie.Nom?.Trim();
            categorie.Description = categorie.Description?.Trim();
        }
    }
}
