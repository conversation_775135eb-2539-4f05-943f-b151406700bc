using System;
using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Classe de base pour toutes les entités
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }
        
        public DateTime DateCreation { get; set; } = DateTime.Now;
        
        public DateTime? DateModification { get; set; }
        
        public bool EstSupprime { get; set; } = false;
        
        public string UtilisateurCreation { get; set; }
        
        public string UtilisateurModification { get; set; }
    }
}
