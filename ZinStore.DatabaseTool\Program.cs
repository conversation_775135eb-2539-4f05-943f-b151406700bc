using System;
using System.IO;
using ZinStore.Data.Context;
using ZinStore.Data.Helpers;

namespace ZinStore.DatabaseTool
{
    /// <summary>
    /// Outil en ligne de commande pour gérer la base de données ZinStore
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== ZinStore Database Tool ===");
            Console.WriteLine();

            try
            {
                if (args.Length == 0)
                {
                    ShowHelp();
                    return;
                }

                string command = args[0].ToLower();

                switch (command)
                {
                    case "create":
                        CreateDatabase(args);
                        break;
                    case "test":
                        TestConnection(args);
                        break;
                    case "info":
                        ShowDatabaseInfo(args);
                        break;
                    case "diagnostic":
                    case "diag":
                        RunDiagnostic();
                        break;
                    case "help":
                    case "-h":
                    case "--help":
                        ShowHelp();
                        break;
                    default:
                        Console.WriteLine($"Commande inconnue: {command}");
                        ShowHelp();
                        break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur: {ex.Message}");
                Environment.Exit(1);
            }
        }

        static void ShowHelp()
        {
            Console.WriteLine("Utilisation: ZinStore.DatabaseTool <commande> [options]");
            Console.WriteLine();
            Console.WriteLine("Commandes disponibles:");
            Console.WriteLine("  create <chemin>     Créer une nouvelle base de données SQLite");
            Console.WriteLine("  test <chemin>       Tester la connexion à la base de données");
            Console.WriteLine("  info <chemin>       Afficher les informations sur la base de données");
            Console.WriteLine("  diagnostic          Diagnostiquer la base de données par défaut");
            Console.WriteLine("  help               Afficher cette aide");
            Console.WriteLine();
            Console.WriteLine("Exemples:");
            Console.WriteLine("  ZinStore.DatabaseTool create ./Data/ZinStore.db");
            Console.WriteLine("  ZinStore.DatabaseTool test ./Data/ZinStore.db");
            Console.WriteLine("  ZinStore.DatabaseTool info ./Data/ZinStore.db");
            Console.WriteLine("  ZinStore.DatabaseTool diagnostic");
        }

        static async void RunDiagnostic()
        {
            await DiagnosticTool.RunDiagnostic();
        }

        static void CreateDatabase(string[] args)
        {
            if (args.Length < 2)
            {
                Console.WriteLine("Erreur: Chemin de la base de données requis");
                Console.WriteLine("Utilisation: ZinStore.DatabaseTool create <chemin>");
                return;
            }

            string dbPath = args[1];

            Console.WriteLine($"Création de la base de données: {dbPath}");
            Console.WriteLine();

            try
            {
                // Créer le répertoire si nécessaire
                DatabaseConfigurationHelper.EnsureSQLiteDirectory(dbPath);
                Console.WriteLine("✓ Répertoire créé/vérifié");

                // Créer la base de données vide
                DatabaseConfigurationHelper.CreateEmptySQLiteDatabase(dbPath);
                Console.WriteLine("✓ Fichier de base de données créé");

                // Générer la chaîne de connexion
                string connectionString = DatabaseConfigurationHelper.GenerateSQLiteConnectionString(dbPath);
                Console.WriteLine($"✓ Chaîne de connexion: {connectionString}");

                // Initialiser les tables
                using (var context = new DatabaseContext(connectionString))
                {
                    if (!context.TestConnection())
                    {
                        throw new Exception("Impossible de se connecter à la base de données");
                    }
                    Console.WriteLine("✓ Connexion établie");

                    var initializer = new DatabaseInitializer(context);
                    initializer.Initialize();
                    Console.WriteLine("✓ Tables créées");
                    Console.WriteLine("✓ Données initiales insérées");
                }

                Console.WriteLine();
                Console.WriteLine("=== Base de données créée avec succès! ===");
                Console.WriteLine($"Chemin: {Path.GetFullPath(dbPath)}");
                Console.WriteLine("Utilisateur par défaut: admin");
                Console.WriteLine("Mot de passe par défaut: admin123");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur lors de la création: {ex.Message}");
                Environment.Exit(1);
            }
        }

        static void TestConnection(string[] args)
        {
            if (args.Length < 2)
            {
                Console.WriteLine("Erreur: Chemin de la base de données requis");
                Console.WriteLine("Utilisation: ZinStore.DatabaseTool test <chemin>");
                return;
            }

            string dbPath = args[1];

            Console.WriteLine($"Test de connexion: {dbPath}");
            Console.WriteLine();

            try
            {
                // Vérifier si le fichier existe
                if (!DatabaseConfigurationHelper.SQLiteDatabaseExists(dbPath))
                {
                    Console.WriteLine("✗ Le fichier de base de données n'existe pas");
                    return;
                }

                // Générer la chaîne de connexion
                string connectionString = DatabaseConfigurationHelper.GenerateSQLiteConnectionString(dbPath);

                // Tester la connexion
                if (DatabaseConfigurationHelper.TestConnection(connectionString, out string errorMessage))
                {
                    Console.WriteLine("✓ Connexion réussie!");

                    // Tester quelques requêtes
                    using (var context = new DatabaseContext(connectionString))
                    {
                        // Test de lecture des tables
                        var tables = context.GetTableNames();
                        Console.WriteLine($"✓ Nombre de tables trouvées: {tables.Count}");

                        foreach (var table in tables)
                        {
                            Console.WriteLine($"  - {table}");
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"✗ Échec de la connexion: {errorMessage}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur lors du test: {ex.Message}");
                Environment.Exit(1);
            }
        }

        static void ShowDatabaseInfo(string[] args)
        {
            if (args.Length < 2)
            {
                Console.WriteLine("Erreur: Chemin de la base de données requis");
                Console.WriteLine("Utilisation: ZinStore.DatabaseTool info <chemin>");
                return;
            }

            string dbPath = args[1];

            Console.WriteLine($"Informations sur la base de données: {dbPath}");
            Console.WriteLine();

            try
            {
                string connectionString = DatabaseConfigurationHelper.GenerateSQLiteConnectionString(dbPath);
                var info = DatabaseConfigurationHelper.GetDatabaseInfo(connectionString);

                Console.WriteLine($"Type: {info.Type}");
                Console.WriteLine($"Chemin: {Path.GetFullPath(info.DatabasePath)}");
                Console.WriteLine($"Existe: {(info.Exists ? "Oui" : "Non")}");

                if (info.Exists)
                {
                    Console.WriteLine($"Taille: {info.Size:N0} octets ({info.Size / 1024.0:F2} KB)");
                    Console.WriteLine($"Dernière modification: {info.LastModified:dd/MM/yyyy HH:mm:ss}");

                    // Informations supplémentaires
                    if (DatabaseConfigurationHelper.TestConnection(connectionString, out string errorMessage))
                    {
                        Console.WriteLine("État de la connexion: ✓ OK");

                        using (var context = new DatabaseContext(connectionString))
                        {
                            var tables = context.GetTableNames();
                            Console.WriteLine($"Nombre de tables: {tables.Count}");

                            if (tables.Count > 0)
                            {
                                Console.WriteLine("Tables:");
                                foreach (var table in tables)
                                {
                                    Console.WriteLine($"  - {table}");
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"État de la connexion: ✗ Erreur - {errorMessage}");
                    }
                }

                if (!string.IsNullOrEmpty(info.Error))
                {
                    Console.WriteLine($"Erreur: {info.Error}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Erreur lors de la récupération des informations: {ex.Message}");
                Environment.Exit(1);
            }
        }
    }
}
