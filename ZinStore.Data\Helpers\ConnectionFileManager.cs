using System;
using System.IO;
using System.Text;

namespace ZinStore.Data.Helpers
{
    /// <summary>
    /// Gestionnaire de fichier de connexion de secours
    /// </summary>
    public static class ConnectionFileManager
    {
        private static readonly string ConnectionFileName = "connection.txt";
        private static readonly string BackupConnectionFileName = "connection_backup.txt";
        
        /// <summary>
        /// Obtient le chemin du fichier de connexion principal
        /// </summary>
        public static string GetConnectionFilePath()
        {
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, ConnectionFileName);
        }
        
        /// <summary>
        /// Obtient le chemin du fichier de connexion de sauvegarde
        /// </summary>
        public static string GetBackupConnectionFilePath()
        {
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, BackupConnectionFileName);
        }
        
        /// <summary>
        /// Vérifie si le fichier de connexion existe
        /// </summary>
        public static bool ConnectionFileExists()
        {
            return File.Exists(GetConnectionFilePath()) || File.Exists(GetBackupConnectionFilePath());
        }
        
        /// <summary>
        /// Lit la chaîne de connexion depuis le fichier
        /// </summary>
        public static string ReadConnectionString()
        {
            try
            {
                // Essayer d'abord le fichier principal
                string mainFilePath = GetConnectionFilePath();
                if (File.Exists(mainFilePath))
                {
                    string connectionString = File.ReadAllText(mainFilePath, Encoding.UTF8).Trim();
                    if (!string.IsNullOrEmpty(connectionString))
                    {
                        return connectionString;
                    }
                }
                
                // Essayer le fichier de sauvegarde
                string backupFilePath = GetBackupConnectionFilePath();
                if (File.Exists(backupFilePath))
                {
                    string connectionString = File.ReadAllText(backupFilePath, Encoding.UTF8).Trim();
                    if (!string.IsNullOrEmpty(connectionString))
                    {
                        return connectionString;
                    }
                }
                
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la lecture du fichier de connexion: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Écrit la chaîne de connexion dans le fichier
        /// </summary>
        public static void WriteConnectionString(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
            {
                throw new ArgumentException("La chaîne de connexion ne peut pas être vide", nameof(connectionString));
            }
            
            try
            {
                string mainFilePath = GetConnectionFilePath();
                string backupFilePath = GetBackupConnectionFilePath();
                
                // Écrire dans le fichier principal
                File.WriteAllText(mainFilePath, connectionString, Encoding.UTF8);
                
                // Créer une copie de sauvegarde
                File.WriteAllText(backupFilePath, connectionString, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de l'écriture du fichier de connexion: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Supprime les fichiers de connexion
        /// </summary>
        public static void DeleteConnectionFiles()
        {
            try
            {
                string mainFilePath = GetConnectionFilePath();
                string backupFilePath = GetBackupConnectionFilePath();
                
                if (File.Exists(mainFilePath))
                {
                    File.Delete(mainFilePath);
                }
                
                if (File.Exists(backupFilePath))
                {
                    File.Delete(backupFilePath);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la suppression des fichiers de connexion: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Crée un fichier de connexion par défaut
        /// </summary>
        public static void CreateDefaultConnectionFile()
        {
            try
            {
                // Créer une chaîne de connexion par défaut pour SQLite
                string defaultDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "ZinStore.db");
                string defaultConnectionString = $"Data Source={defaultDbPath};Version=3;";
                
                WriteConnectionString(defaultConnectionString);
            }
            catch (Exception ex)
            {
                throw new Exception($"Erreur lors de la création du fichier de connexion par défaut: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Valide une chaîne de connexion
        /// </summary>
        public static bool ValidateConnectionString(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
            {
                return false;
            }
            
            try
            {
                // Vérifier si c'est une chaîne de connexion SQLite
                if (connectionString.Contains("Data Source", StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
                
                // Vérifier si c'est une chaîne de connexion SQL Server
                if (connectionString.Contains("Server", StringComparison.OrdinalIgnoreCase) ||
                    connectionString.Contains("Data Source", StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
                
                return false;
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Obtient des informations sur le fichier de connexion
        /// </summary>
        public static ConnectionFileInfo GetConnectionFileInfo()
        {
            var info = new ConnectionFileInfo();
            
            string mainFilePath = GetConnectionFilePath();
            string backupFilePath = GetBackupConnectionFilePath();
            
            info.MainFileExists = File.Exists(mainFilePath);
            info.BackupFileExists = File.Exists(backupFilePath);
            
            if (info.MainFileExists)
            {
                var fileInfo = new FileInfo(mainFilePath);
                info.MainFileSize = fileInfo.Length;
                info.MainFileLastModified = fileInfo.LastWriteTime;
            }
            
            if (info.BackupFileExists)
            {
                var fileInfo = new FileInfo(backupFilePath);
                info.BackupFileSize = fileInfo.Length;
                info.BackupFileLastModified = fileInfo.LastWriteTime;
            }
            
            try
            {
                info.ConnectionString = ReadConnectionString();
                info.IsValid = ValidateConnectionString(info.ConnectionString);
            }
            catch (Exception ex)
            {
                info.ErrorMessage = ex.Message;
                info.IsValid = false;
            }
            
            return info;
        }
    }
    
    /// <summary>
    /// Informations sur le fichier de connexion
    /// </summary>
    public class ConnectionFileInfo
    {
        public bool MainFileExists { get; set; }
        public bool BackupFileExists { get; set; }
        public long MainFileSize { get; set; }
        public long BackupFileSize { get; set; }
        public DateTime MainFileLastModified { get; set; }
        public DateTime BackupFileLastModified { get; set; }
        public string ConnectionString { get; set; }
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
        
        public bool HasAnyFile => MainFileExists || BackupFileExists;
    }
}
