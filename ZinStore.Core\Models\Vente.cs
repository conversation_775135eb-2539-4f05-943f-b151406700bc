using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using ZinStore.Core.Enums;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les ventes
    /// </summary>
    public class Vente : BaseEntity
    {
        [Required(ErrorMessage = "Le numéro de facture est obligatoire")]
        [StringLength(50)]
        public string NumeroFacture { get; set; }

        [Required]
        public DateTime DateVente { get; set; } = DateTime.Now;

        public int? ClientId { get; set; }

        [StringLength(100)]
        public string NomClient { get; set; }

        [Required]
        public int UtilisateurId { get; set; }

        public StatutFacture Statut { get; set; } = StatutFacture.Brouillon;

        public decimal SousTotal { get; set; }

        public decimal MontantTVA { get; set; }

        public decimal MontantRemise { get; set; }

        public decimal MontantHT { get; set; }

        public decimal MontantTotal { get; set; }

        public decimal MontantPaye { get; set; }

        public decimal MontantRestant { get; set; }

        [StringLength(50)]
        public string ModePaiement { get; set; }

        [StringLength(50)]
        public string StatutVente { get; set; } = "Brouillon";

        public DateTime? DateEcheance { get; set; }

        public string Notes { get; set; }

        public bool EstComptant { get; set; } = true;

        public bool EstLivree { get; set; } = false;

        public DateTime? DateLivraison { get; set; }

        [StringLength(200)]
        public string AdresseLivraison { get; set; }

        // Propriétés de navigation
        public virtual Client Client { get; set; }
        public virtual Utilisateur Utilisateur { get; set; }

        // Collection des lignes de vente
        public virtual List<LigneVente> LignesVente { get; set; } = new List<LigneVente>();

        // Propriétés calculées
        public bool EstPayee => MontantRestant <= 0;
        public bool EstEnRetard => DateEcheance.HasValue && DateEcheance < DateTime.Now && MontantRestant > 0;
    }
}
