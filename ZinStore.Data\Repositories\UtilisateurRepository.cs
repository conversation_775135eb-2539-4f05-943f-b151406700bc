using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using ZinStore.Core.Models;
using ZinStore.Data.Context;

namespace ZinStore.Data.Repositories
{
    /// <summary>
    /// Repository pour les utilisateurs
    /// </summary>
    public class UtilisateurRepository : BaseRepository<Utilisateur>
    {
        public UtilisateurRepository(DatabaseContext context) : base(context, "Utilisateurs")
        {
        }

        /// <summary>
        /// Recherche des utilisateurs par nom d'utilisateur ou nom complet
        /// </summary>
        public override async Task<IEnumerable<Utilisateur>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT * FROM Utilisateurs 
                WHERE EstSupprime = 0 
                AND (NomUtilisateur LIKE @SearchTerm OR NomComplet LIKE @SearchTerm)
                ORDER BY NomComplet";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Utilisateur>(sql, new { SearchTerm = $"%{searchTerm}%" });
            }
        }

        /// <summary>
        /// Authentifie un utilisateur
        /// </summary>
        public async Task<Utilisateur> AuthenticateAsync(string nomUtilisateur, string motDePasse)
        {
            var sql = @"
                SELECT * FROM Utilisateurs 
                WHERE NomUtilisateur = @NomUtilisateur 
                AND MotDePasse = @MotDePasse 
                AND EstActif = 1 
                AND EstSupprime = 0";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryFirstOrDefaultAsync<Utilisateur>(sql, 
                    new { NomUtilisateur = nomUtilisateur, MotDePasse = motDePasse });
            }
        }

        /// <summary>
        /// Vérifie si un nom d'utilisateur existe déjà
        /// </summary>
        public async Task<bool> UsernameExistsAsync(string nomUtilisateur, int? excludeId = null)
        {
            var sql = "SELECT COUNT(*) FROM Utilisateurs WHERE NomUtilisateur = @NomUtilisateur AND EstSupprime = 0";
            
            if (excludeId.HasValue)
            {
                sql += " AND Id != @ExcludeId";
            }

            using (var connection = _context.GetConnection())
            {
                var count = await connection.QuerySingleAsync<int>(sql, 
                    new { NomUtilisateur = nomUtilisateur, ExcludeId = excludeId });
                return count > 0;
            }
        }

        /// <summary>
        /// Obtient les utilisateurs actifs
        /// </summary>
        public async Task<IEnumerable<Utilisateur>> GetActiveUsersAsync()
        {
            var sql = @"
                SELECT * FROM Utilisateurs 
                WHERE EstActif = 1 AND EstSupprime = 0 
                ORDER BY NomComplet";

            using (var connection = _context.GetConnection())
            {
                return await connection.QueryAsync<Utilisateur>(sql);
            }
        }

        /// <summary>
        /// Met à jour le mot de passe d'un utilisateur
        /// </summary>
        public async Task<bool> UpdatePasswordAsync(int userId, string newPassword)
        {
            var sql = "UPDATE Utilisateurs SET MotDePasse = @MotDePasse WHERE Id = @Id";

            using (var connection = _context.GetConnection())
            {
                var affectedRows = await connection.ExecuteAsync(sql, 
                    new { Id = userId, MotDePasse = newPassword });
                return affectedRows > 0;
            }
        }

        /// <summary>
        /// Active ou désactive un utilisateur
        /// </summary>
        public async Task<bool> ToggleActiveStatusAsync(int userId, bool isActive)
        {
            var sql = "UPDATE Utilisateurs SET EstActif = @EstActif WHERE Id = @Id";

            using (var connection = _context.GetConnection())
            {
                var affectedRows = await connection.ExecuteAsync(sql, 
                    new { Id = userId, EstActif = isActive });
                return affectedRows > 0;
            }
        }
    }
}
