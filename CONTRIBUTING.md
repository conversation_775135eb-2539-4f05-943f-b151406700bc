# Guide de Contribution - ZinStore

Merci de votre intérêt pour contribuer à ZinStore ! Ce guide vous aidera à comprendre comment participer au développement du projet.

## 🤝 Comment Contribuer

### Types de Contributions Acceptées

- 🐛 **Correction de bugs**
- ✨ **Nouvelles fonctionnalités**
- 📚 **Amélioration de la documentation**
- 🌐 **Traductions**
- 🎨 **Améliorations de l'interface**
- ⚡ **Optimisations de performance**
- 🧪 **Tests unitaires**

## 🚀 Démarrage Rapide

### 1. Fork et Clone
```bash
# Fork le projet sur GitHub, puis clonez votre fork
git clone https://github.com/VOTRE-USERNAME/zinstore.git
cd zinstore

# Ajoutez le repository original comme remote
git remote add upstream https://github.com/zinstore/zinstore.git
```

### 2. Configuration de l'Environnement
```bash
# Installez les dépendances
nuget restore ZinStore.sln

# Compilez le projet
msbuild ZinStore.sln /p:Configuration=Debug
```

### 3. Créer une Branche
```bash
# Créez une branche pour votre fonctionnalité
git checkout -b feature/ma-nouvelle-fonctionnalite

# Ou pour un bug fix
git checkout -b fix/correction-bug-important
```

## 📝 Standards de Code

### Conventions de Nommage C#
- **Classes** : PascalCase (`ClientService`, `ProduitRepository`)
- **Méthodes** : PascalCase (`GetClientById`, `AddNewProduct`)
- **Variables** : camelCase (`clientId`, `productName`)
- **Constantes** : UPPER_CASE (`MAX_RETRY_COUNT`)
- **Interfaces** : Préfixe I (`IRepository`, `IClientService`)

### Structure des Fichiers
```
ZinStore.Module/
├── Models/           # Entités et modèles
├── Services/         # Logique métier
├── Repositories/     # Accès aux données
├── ViewModels/       # ViewModels MVVM
├── Views/           # Vues XAML
├── Helpers/         # Classes utilitaires
└── Properties/      # Métadonnées d'assembly
```

### Commentaires et Documentation
```csharp
/// <summary>
/// Service pour la gestion des clients
/// </summary>
public class ClientService
{
    /// <summary>
    /// Ajoute un nouveau client
    /// </summary>
    /// <param name="client">Le client à ajouter</param>
    /// <returns>Résultat de l'opération avec l'ID du client créé</returns>
    public async Task<(bool Success, string Message, int ClientId)> AddClientAsync(Client client)
    {
        // Implémentation...
    }
}
```

## 🧪 Tests

### Écriture de Tests
- Utilisez MSTest ou NUnit
- Nommage : `MethodName_Scenario_ExpectedResult`
- Arrangez-Act-Assert pattern

```csharp
[TestMethod]
public async Task AddClient_ValidClient_ReturnsSuccess()
{
    // Arrange
    var client = new Client { Nom = "Test Client" };
    var service = new ClientService(mockContext);

    // Act
    var result = await service.AddClientAsync(client);

    // Assert
    Assert.IsTrue(result.Success);
    Assert.IsTrue(result.ClientId > 0);
}
```

### Exécution des Tests
```bash
# Exécuter tous les tests
dotnet test

# Exécuter avec couverture
dotnet test --collect:"XPlat Code Coverage"
```

## 📋 Processus de Pull Request

### 1. Avant de Soumettre
- [ ] Le code compile sans erreurs
- [ ] Tous les tests passent
- [ ] Le code suit les conventions du projet
- [ ] La documentation est mise à jour si nécessaire
- [ ] Les commits sont bien formatés

### 2. Format des Commits
```
type(scope): description courte

Description plus détaillée si nécessaire.

Fixes #123
```

**Types de commits :**
- `feat`: nouvelle fonctionnalité
- `fix`: correction de bug
- `docs`: documentation
- `style`: formatage, point-virgules manquants, etc.
- `refactor`: refactoring du code
- `test`: ajout de tests
- `chore`: maintenance

**Exemples :**
```
feat(client): ajouter validation email client
fix(stock): corriger calcul stock disponible
docs(readme): mettre à jour guide installation
```

### 3. Description de la Pull Request
```markdown
## Description
Brève description des changements apportés.

## Type de Changement
- [ ] Bug fix (changement non-breaking qui corrige un problème)
- [ ] Nouvelle fonctionnalité (changement non-breaking qui ajoute une fonctionnalité)
- [ ] Breaking change (fix ou fonctionnalité qui casserait la fonctionnalité existante)
- [ ] Mise à jour de documentation

## Tests
- [ ] Tests unitaires ajoutés/mis à jour
- [ ] Tests d'intégration ajoutés/mis à jour
- [ ] Tests manuels effectués

## Checklist
- [ ] Mon code suit les guidelines du projet
- [ ] J'ai effectué une auto-review de mon code
- [ ] J'ai commenté mon code, particulièrement dans les zones difficiles
- [ ] J'ai mis à jour la documentation correspondante
- [ ] Mes changements ne génèrent pas de nouveaux warnings
- [ ] J'ai ajouté des tests qui prouvent que mon fix est efficace ou que ma fonctionnalité marche
- [ ] Les tests unitaires nouveaux et existants passent localement
```

## 🐛 Signalement de Bugs

### Template de Bug Report
```markdown
**Description du Bug**
Description claire et concise du bug.

**Étapes pour Reproduire**
1. Aller à '...'
2. Cliquer sur '....'
3. Faire défiler jusqu'à '....'
4. Voir l'erreur

**Comportement Attendu**
Description claire de ce qui devrait se passer.

**Captures d'Écran**
Si applicable, ajoutez des captures d'écran.

**Environnement:**
 - OS: [ex. Windows 10]
 - Version ZinStore: [ex. 1.0.0]
 - .NET Framework: [ex. 4.7.2]

**Contexte Additionnel**
Tout autre contexte sur le problème.
```

## 💡 Demande de Fonctionnalité

### Template de Feature Request
```markdown
**La fonctionnalité est-elle liée à un problème ?**
Description claire du problème. Ex. Je suis toujours frustré quand [...]

**Décrivez la solution que vous aimeriez**
Description claire de ce que vous voulez qu'il se passe.

**Décrivez les alternatives que vous avez considérées**
Description des solutions ou fonctionnalités alternatives.

**Contexte Additionnel**
Tout autre contexte ou captures d'écran sur la demande de fonctionnalité.
```

## 🎯 Priorités de Développement

### Haute Priorité
- Corrections de bugs critiques
- Problèmes de sécurité
- Performance de l'application

### Moyenne Priorité
- Nouvelles fonctionnalités demandées
- Améliorations UX/UI
- Optimisations

### Basse Priorité
- Refactoring du code
- Documentation
- Tests supplémentaires

## 📞 Communication

### Canaux de Communication
- **GitHub Issues** : Pour les bugs et demandes de fonctionnalités
- **GitHub Discussions** : Pour les questions générales
- **Email** : <EMAIL> pour les questions privées

### Code de Conduite
- Soyez respectueux et professionnel
- Acceptez les critiques constructives
- Concentrez-vous sur ce qui est le mieux pour la communauté
- Montrez de l'empathie envers les autres membres

## 🏆 Reconnaissance

Les contributeurs seront reconnus dans :
- Le fichier AUTHORS.md
- Les notes de version
- La page "À propos" de l'application

## 📚 Ressources Utiles

- [Documentation C#](https://docs.microsoft.com/fr-fr/dotnet/csharp/)
- [Guide WPF](https://docs.microsoft.com/fr-fr/dotnet/desktop/wpf/)
- [Material Design](https://material.io/design)
- [Dapper Documentation](https://dapper-tutorial.net/)

---

Merci de contribuer à ZinStore ! 🚀
