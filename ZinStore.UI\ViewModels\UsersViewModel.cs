using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.Core.Models;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class UsersViewModel : BaseViewModel
    {
        private string _searchText;
        private string _selectedRoleFilter;
        private string _statusFilter = "All";
        private DateTime _lastUpdateTime;

        public UsersViewModel()
        {
            Users = new ObservableCollection<User>();
            Roles = new ObservableCollection<Role>();

            // Commandes
            AddUserCommand = new RelayCommand(AddUser);
            EditUserCommand = new RelayCommand(param => EditUser(param as User));
            DeleteUserCommand = new RelayCommand(param => DeleteUser(param as User));
            ManagePermissionsCommand = new RelayCommand(param => ManagePermissions(param as User));
            ResetPasswordCommand = new RelayCommand(param => ResetPassword(param as User));
            ToggleUserStatusCommand = new RelayCommand(param => ToggleUserStatus(param as User));
            ManageRolesCommand = new RelayCommand(ManageRoles);
            SearchCommand = new RelayCommand(async () => await SearchUsersAsync());
            RefreshCommand = new RelayCommand(async () => await LoadUsersAsync());

            _ = LoadDataAsync();
        }

        // Propriétés
        public string SearchText
        {
            get => _searchText;
            set => SetProperty(ref _searchText, value);
        }

        public string SelectedRoleFilter
        {
            get => _selectedRoleFilter;
            set
            {
                SetProperty(ref _selectedRoleFilter, value);
                _ = SearchUsersAsync();
            }
        }

        public string StatusFilter
        {
            get => _statusFilter;
            set
            {
                SetProperty(ref _statusFilter, value);
                _ = SearchUsersAsync();
            }
        }

        public DateTime LastUpdateTime
        {
            get => _lastUpdateTime;
            set => SetProperty(ref _lastUpdateTime, value);
        }

        public bool IsEmpty => !IsBusy && Users.Count == 0;

        // Collections
        public ObservableCollection<User> Users { get; }
        public ObservableCollection<Role> Roles { get; }

        // Commandes
        public ICommand AddUserCommand { get; }
        public ICommand EditUserCommand { get; }
        public ICommand DeleteUserCommand { get; }
        public ICommand ManagePermissionsCommand { get; }
        public ICommand ResetPasswordCommand { get; }
        public ICommand ToggleUserStatusCommand { get; }
        public ICommand ManageRolesCommand { get; }
        public ICommand SearchCommand { get; }
        public ICommand RefreshCommand { get; }

        private async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                await LoadRolesAsync();
                await LoadUsersAsync();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task LoadRolesAsync()
        {
            try
            {
                // TODO: Charger depuis la base de données
                await Task.Delay(100); // Simulation

                Roles.Clear();
                Roles.Add(new Role { Id = 1, Name = "Administrateur", Description = "Accès complet au système" });
                Roles.Add(new Role { Id = 2, Name = "Manager", Description = "Gestion des ventes et du stock" });
                Roles.Add(new Role { Id = 3, Name = "Vendeur", Description = "Accès aux ventes uniquement" });
                Roles.Add(new Role { Id = 4, Name = "Magasinier", Description = "Gestion du stock uniquement" });
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des rôles: {ex.Message}");
            }
        }

        private async Task LoadUsersAsync()
        {
            try
            {
                // TODO: Charger depuis la base de données
                await Task.Delay(500); // Simulation

                Users.Clear();
                
                // Données d'exemple
                Users.Add(new User
                {
                    Id = 1,
                    Username = "admin",
                    FullName = "Administrateur Système",
                    Email = "<EMAIL>",
                    RoleId = 1,
                    RoleName = "Administrateur",
                    Status = "Actif",
                    IsActive = true,
                    LastLogin = DateTime.Now.AddHours(-2),
                    CreatedDate = DateTime.Now.AddMonths(-6)
                });

                Users.Add(new User
                {
                    Id = 2,
                    Username = "manager1",
                    FullName = "Ahmed Benali",
                    Email = "<EMAIL>",
                    RoleId = 2,
                    RoleName = "Manager",
                    Status = "Actif",
                    IsActive = true,
                    LastLogin = DateTime.Now.AddDays(-1),
                    CreatedDate = DateTime.Now.AddMonths(-3)
                });

                Users.Add(new User
                {
                    Id = 3,
                    Username = "vendeur1",
                    FullName = "Fatima Zohra",
                    Email = "<EMAIL>",
                    RoleId = 3,
                    RoleName = "Vendeur",
                    Status = "Actif",
                    IsActive = true,
                    LastLogin = DateTime.Now.AddHours(-4),
                    CreatedDate = DateTime.Now.AddMonths(-2)
                });

                Users.Add(new User
                {
                    Id = 4,
                    Username = "magasinier1",
                    FullName = "Mohamed Khelil",
                    Email = "<EMAIL>",
                    RoleId = 4,
                    RoleName = "Magasinier",
                    Status = "Inactif",
                    IsActive = false,
                    LastLogin = DateTime.Now.AddDays(-7),
                    CreatedDate = DateTime.Now.AddMonths(-1)
                });

                LastUpdateTime = DateTime.Now;
                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des utilisateurs: {ex.Message}");
            }
        }

        private async Task SearchUsersAsync()
        {
            try
            {
                // TODO: Implémenter la recherche dans la base de données
                await Task.Delay(200); // Simulation

                // Pour l'instant, on recharge tout et on filtre localement
                await LoadUsersAsync();
                
                // Filtrer par texte de recherche
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    var filteredUsers = Users.Where(u => 
                        u.Username.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        u.FullName.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        u.Email.Contains(SearchText, StringComparison.OrdinalIgnoreCase)).ToList();
                    
                    Users.Clear();
                    foreach (var user in filteredUsers)
                    {
                        Users.Add(user);
                    }
                }

                OnPropertyChanged(nameof(IsEmpty));
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la recherche: {ex.Message}");
            }
        }

        private void AddUser()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'ajout d'utilisateur en cours de développement.");
        }

        private void EditUser(User user)
        {
            if (user == null) return;
            MessageBoxHelper.ShowInfo($"Modification de l'utilisateur '{user.Username}' en cours de développement.");
        }

        private void DeleteUser(User user)
        {
            if (user == null) return;
            
            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir supprimer l'utilisateur '{user.Username}' ?"))
            {
                try
                {
                    // TODO: Supprimer de la base de données
                    Users.Remove(user);
                    MessageBoxHelper.ShowSuccess("Utilisateur supprimé avec succès.");
                    OnPropertyChanged(nameof(IsEmpty));
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la suppression: {ex.Message}");
                }
            }
        }

        private void ManagePermissions(User user)
        {
            if (user == null) return;
            MessageBoxHelper.ShowInfo($"Gestion des permissions pour '{user.Username}' en cours de développement.");
        }

        private void ResetPassword(User user)
        {
            if (user == null) return;
            
            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir réinitialiser le mot de passe de '{user.Username}' ?"))
            {
                try
                {
                    // TODO: Réinitialiser le mot de passe
                    string newPassword = "123456"; // Mot de passe temporaire
                    MessageBoxHelper.ShowInfo($"Mot de passe réinitialisé avec succès.\nNouveau mot de passe temporaire: {newPassword}");
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la réinitialisation: {ex.Message}");
                }
            }
        }

        private void ToggleUserStatus(User user)
        {
            if (user == null) return;
            
            try
            {
                user.IsActive = !user.IsActive;
                user.Status = user.IsActive ? "Actif" : "Inactif";
                
                // TODO: Mettre à jour dans la base de données
                
                string action = user.IsActive ? "activé" : "désactivé";
                MessageBoxHelper.ShowSuccess($"Utilisateur {action} avec succès.");
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors de la modification du statut: {ex.Message}");
            }
        }

        private void ManageRoles()
        {
            MessageBoxHelper.ShowInfo("Gestion des rôles en cours de développement.");
        }
    }

    // Modèles pour les utilisateurs et rôles
    public class User
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public int RoleId { get; set; }
        public string RoleName { get; set; }
        public string Status { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastLogin { get; set; }
        public DateTime CreatedDate { get; set; }
        public string ProfilePicture { get; set; }
        public string Notes { get; set; }
    }

    public class Role
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
    }
}
