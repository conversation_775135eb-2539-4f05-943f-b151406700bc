<Window x:Class="ZinStore.UI.Views.Common.BarcodeInputDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Scanner Code-Barres"
        Height="300"
        Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0"
                   Text="📷 Scanner Code-Barres"
                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>

        <!-- Zone de saisie -->
        <materialDesign:Card Grid.Row="1"
                           Padding="20"
                           Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="Scannez le code-barres ou saisissez-le manuellement:"
                          Style="{StaticResource MaterialDesignBody1TextBlock}"
                          Margin="0,0,0,15"/>
                
                <TextBox x:Name="BarcodeTextBox"
                        materialDesign:HintAssist.Hint="Code-barres"
                        materialDesign:HintAssist.IsFloating="True"
                        FontSize="16"
                        FontFamily="Consolas"
                        Text="{Binding ScannedBarcode, UpdateSourceTrigger=PropertyChanged}"
                        KeyDown="BarcodeTextBox_KeyDown"
                        Margin="0,0,0,15"/>
                
                <StackPanel Orientation="Horizontal"
                           HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Barcode"
                                           Width="24"
                                           Height="24"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           Margin="0,0,10,0"/>
                    <TextBlock Text="Placez le curseur dans le champ et scannez"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- Instructions -->
        <materialDesign:Card Grid.Row="2"
                           Background="{DynamicResource MaterialDesignToolBarBackground}"
                           Padding="15"
                           Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="💡 Instructions:"
                          FontWeight="Bold"
                          Margin="0,0,0,5"/>
                <TextBlock Text="• Utilisez un scanner USB ou Bluetooth connecté"
                          Margin="0,0,0,2"/>
                <TextBlock Text="• Ou saisissez le code manuellement"
                          Margin="0,0,0,2"/>
                <TextBlock Text="• Appuyez sur Entrée ou cliquez sur Rechercher"
                          Margin="0,0,0,2"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Boutons -->
        <StackPanel Grid.Row="3"
                   Orientation="Horizontal"
                   HorizontalAlignment="Right">
            <Button Content="Rechercher"
                   Style="{StaticResource MaterialDesignRaisedButton}"
                   Command="{Binding SearchCommand}"
                   IsDefault="True"
                   Margin="0,0,10,0"
                   Width="120"/>
            <Button Content="Annuler"
                   Style="{StaticResource MaterialDesignOutlinedButton}"
                   IsCancel="True"
                   Click="CancelButton_Click"
                   Width="120"/>
        </StackPanel>
    </Grid>
</Window>
