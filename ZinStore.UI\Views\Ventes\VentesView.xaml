<UserControl x:Class="ZinStore.UI.Views.VentesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">

    <UserControl.DataContext>
        <viewModels:VentesViewModel />
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0"
                  Text="Gestion des Ventes"
                  Style="{StaticResource TitleText}"/>

        <!-- Barre d'outils -->
        <materialDesign:Card Grid.Row="1"
                           Margin="0,0,0,20"
                           Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox materialDesign:HintAssist.Hint="Rechercher une vente..."
                            Style="{StaticResource CustomTextBox}"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Width="250"
                            Margin="0,0,10,0"/>

                    <DatePicker materialDesign:HintAssist.Hint="Date début"
                               SelectedDate="{Binding DateDebut}"
                               Width="120"
                               Margin="0,0,10,0"/>

                    <DatePicker materialDesign:HintAssist.Hint="Date fin"
                               SelectedDate="{Binding DateFin}"
                               Width="120"/>
                </StackPanel>

                <!-- Boutons -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal">
                    <Button Content="Nouvelle Vente"
                           Style="{StaticResource PrimaryButton}"
                           Command="{Binding AddVenteCommand}"/>
                    <Button Content="Voir Détails"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding ViewDetailsCommand}"/>
                    <Button Content="Imprimer"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding PrintCommand}"/>
                    <Button Content="Actualiser"
                           Style="{StaticResource SecondaryButton}"
                           Command="{Binding RefreshCommand}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des ventes -->
        <materialDesign:Card Grid.Row="2"
                           Padding="15">
            <DataGrid ItemsSource="{Binding Ventes}"
                     SelectedItem="{Binding SelectedVente}"
                     Style="{StaticResource CustomDataGrid}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="N° Facture"
                                      Binding="{Binding NumeroFacture}"
                                      Width="120"/>
                    <DataGridTextColumn Header="Date"
                                      Binding="{Binding DateVente, StringFormat='dd/MM/yyyy HH:mm'}"
                                      Width="140"/>
                    <DataGridTextColumn Header="Client"
                                      Binding="{Binding NomClient}"
                                      Width="200"/>
                    <DataGridTextColumn Header="Montant HT"
                                      Binding="{Binding MontantHT, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridTextColumn Header="TVA"
                                      Binding="{Binding MontantTVA, StringFormat='{}{0:C}'}"
                                      Width="80"/>
                    <DataGridTextColumn Header="Montant TTC"
                                      Binding="{Binding MontantTotal, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Statut"
                                      Binding="{Binding StatutVente}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Mode Paiement"
                                      Binding="{Binding ModePaiement}"
                                      Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
