<UserControl x:Class="ZinStore.UI.Views.VentesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:viewModels="clr-namespace:ZinStore.UI.ViewModels">

    <UserControl.DataContext>
        <viewModels:VentesViewModel />
    </UserControl.DataContext>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Titre -->
        <TextBlock Grid.Row="0"
                  Text="💰 Gestion des Ventes"
                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                  Margin="0,0,0,20"
                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                  FontWeight="Bold"/>

        <!-- Barre d'outils -->
        <materialDesign:Card Grid.Row="1"
                           Margin="0,0,0,20"
                           Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Recherche -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBox materialDesign:HintAssist.Hint="Rechercher une vente (N° facture, client)..."
                            materialDesign:HintAssist.IsFloating="True"
                            Style="{StaticResource MaterialDesignOutlinedTextBox}"
                            Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                            Width="300"
                            Height="56"
                            FontSize="14"
                            VerticalContentAlignment="Center"
                            Margin="0,0,10,0">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>

                    <DatePicker materialDesign:HintAssist.Hint="Date début"
                               materialDesign:HintAssist.IsFloating="True"
                               Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                               SelectedDate="{Binding DateDebut}"
                               Width="140"
                               Height="56"
                               Margin="0,0,10,0"/>

                    <DatePicker materialDesign:HintAssist.Hint="Date fin"
                               materialDesign:HintAssist.IsFloating="True"
                               Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                               SelectedDate="{Binding DateFin}"
                               Width="140"
                               Height="56"/>
                </StackPanel>

                <!-- Boutons -->
                <StackPanel Grid.Column="1"
                           Orientation="Horizontal"
                           VerticalAlignment="Center">
                    <Button Content="➕ Nouvelle Vente"
                           Style="{StaticResource MaterialDesignRaisedButton}"
                           Command="{Binding AddVenteCommand}"
                           Background="{DynamicResource PrimaryHueMidBrush}"
                           Foreground="White"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="👁️ Voir Détails"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding ViewDetailsCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="🖨️ Imprimer"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding PrintCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                    <Button Content="🔄 Actualiser"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Command="{Binding RefreshCommand}"
                           Margin="5,0"
                           Height="40"
                           Padding="15,0"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- Liste des ventes -->
        <materialDesign:Card Grid.Row="2"
                           Padding="15">
            <DataGrid ItemsSource="{Binding Ventes}"
                     SelectedItem="{Binding SelectedVente}"
                     Style="{StaticResource CustomDataGrid}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="N° Facture"
                                      Binding="{Binding NumeroFacture}"
                                      Width="120"/>
                    <DataGridTextColumn Header="Date"
                                      Binding="{Binding DateVente, StringFormat='dd/MM/yyyy HH:mm'}"
                                      Width="140"/>
                    <DataGridTextColumn Header="Client"
                                      Binding="{Binding NomClient}"
                                      Width="200"/>
                    <DataGridTextColumn Header="Montant HT"
                                      Binding="{Binding MontantHT, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridTextColumn Header="TVA"
                                      Binding="{Binding MontantTVA, StringFormat='{}{0:C}'}"
                                      Width="80"/>
                    <DataGridTextColumn Header="Montant TTC"
                                      Binding="{Binding MontantTotal, StringFormat='{}{0:C}'}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Statut"
                                      Binding="{Binding StatutVente}"
                                      Width="100"/>
                    <DataGridTextColumn Header="Mode Paiement"
                                      Binding="{Binding ModePaiement}"
                                      Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </materialDesign:Card>
    </Grid>
</UserControl>
