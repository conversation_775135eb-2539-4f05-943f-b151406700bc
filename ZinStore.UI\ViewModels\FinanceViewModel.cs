using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZinStore.UI.Helpers;

namespace ZinStore.UI.ViewModels
{
    public class FinanceViewModel : BaseViewModel
    {
        private decimal _totalIncome;
        private decimal _totalExpenses;
        private decimal _netProfit;
        private decimal _cashBalance;
        private decimal _incomeGrowth;
        private decimal _expenseGrowth;
        private decimal _profitMargin;
        private string _typeFilter = "All";
        private DateTime? _dateFrom;
        private DateTime? _dateTo;

        public FinanceViewModel()
        {
            Transactions = new ObservableCollection<FinancialTransaction>();
            ExpenseCategories = new ObservableCollection<ExpenseCategory>();

            // Commandes
            AddIncomeCommand = new RelayCommand(AddIncome);
            AddExpenseCommand = new RelayCommand(AddExpense);
            EditTransactionCommand = new RelayCommand(param => EditTransaction(param as FinancialTransaction));
            DeleteTransactionCommand = new RelayCommand(param => DeleteTransaction(param as FinancialTransaction));
            RefreshCommand = new RelayCommand(async () => await LoadDataAsync());
            ExportCommand = new RelayCommand(ExportData);
            GenerateReportCommand = new RelayCommand(GenerateReport);
            ManageCategoriesCommand = new RelayCommand(ManageCategories);

            // Initialiser les dates par défaut (mois actuel)
            DateFrom = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            DateTo = DateTime.Now;

            _ = LoadDataAsync();
        }

        // Propriétés financières
        public decimal TotalIncome
        {
            get => _totalIncome;
            set => SetProperty(ref _totalIncome, value);
        }

        public decimal TotalExpenses
        {
            get => _totalExpenses;
            set => SetProperty(ref _totalExpenses, value);
        }

        public decimal NetProfit
        {
            get => _netProfit;
            set => SetProperty(ref _netProfit, value);
        }

        public decimal CashBalance
        {
            get => _cashBalance;
            set => SetProperty(ref _cashBalance, value);
        }

        public decimal IncomeGrowth
        {
            get => _incomeGrowth;
            set => SetProperty(ref _incomeGrowth, value);
        }

        public decimal ExpenseGrowth
        {
            get => _expenseGrowth;
            set => SetProperty(ref _expenseGrowth, value);
        }

        public decimal ProfitMargin
        {
            get => _profitMargin;
            set => SetProperty(ref _profitMargin, value);
        }

        // Filtres
        public string TypeFilter
        {
            get => _typeFilter;
            set
            {
                SetProperty(ref _typeFilter, value);
                _ = FilterTransactionsAsync();
            }
        }

        public DateTime? DateFrom
        {
            get => _dateFrom;
            set
            {
                SetProperty(ref _dateFrom, value);
                _ = FilterTransactionsAsync();
            }
        }

        public DateTime? DateTo
        {
            get => _dateTo;
            set
            {
                SetProperty(ref _dateTo, value);
                _ = FilterTransactionsAsync();
            }
        }

        // Collections
        public ObservableCollection<FinancialTransaction> Transactions { get; }
        public ObservableCollection<ExpenseCategory> ExpenseCategories { get; }

        // Commandes
        public ICommand AddIncomeCommand { get; }
        public ICommand AddExpenseCommand { get; }
        public ICommand EditTransactionCommand { get; }
        public ICommand DeleteTransactionCommand { get; }
        public ICommand RefreshCommand { get; }
        public ICommand ExportCommand { get; }
        public ICommand GenerateReportCommand { get; }
        public ICommand ManageCategoriesCommand { get; }

        private async Task LoadDataAsync()
        {
            try
            {
                IsBusy = true;
                await LoadTransactionsAsync();
                await LoadExpenseCategoriesAsync();
                CalculateFinancialSummary();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des données: {ex.Message}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task LoadTransactionsAsync()
        {
            try
            {
                // TODO: Charger depuis la base de données
                await Task.Delay(500); // Simulation

                Transactions.Clear();

                // Données d'exemple
                var sampleTransactions = new[]
                {
                    new FinancialTransaction
                    {
                        Id = 1,
                        Type = "Revenu",
                        Date = DateTime.Now.AddDays(-1),
                        Description = "Vente produits électroniques",
                        Category = "Ventes",
                        Amount = 15000,
                        PaymentMethod = "Espèces"
                    },
                    new FinancialTransaction
                    {
                        Id = 2,
                        Type = "Dépense",
                        Date = DateTime.Now.AddDays(-2),
                        Description = "Achat marchandises",
                        Category = "Achats",
                        Amount = 8000,
                        PaymentMethod = "Virement"
                    },
                    new FinancialTransaction
                    {
                        Id = 3,
                        Type = "Dépense",
                        Date = DateTime.Now.AddDays(-3),
                        Description = "Facture électricité",
                        Category = "Charges",
                        Amount = 1200,
                        PaymentMethod = "Chèque"
                    },
                    new FinancialTransaction
                    {
                        Id = 4,
                        Type = "Revenu",
                        Date = DateTime.Now.AddDays(-4),
                        Description = "Vente accessoires",
                        Category = "Ventes",
                        Amount = 3500,
                        PaymentMethod = "Carte"
                    },
                    new FinancialTransaction
                    {
                        Id = 5,
                        Type = "Dépense",
                        Date = DateTime.Now.AddDays(-5),
                        Description = "Salaires personnel",
                        Category = "Personnel",
                        Amount = 25000,
                        PaymentMethod = "Virement"
                    }
                };

                foreach (var transaction in sampleTransactions)
                {
                    Transactions.Add(transaction);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des transactions: {ex.Message}");
            }
        }

        private async Task LoadExpenseCategoriesAsync()
        {
            try
            {
                // TODO: Calculer depuis la base de données
                await Task.Delay(200); // Simulation

                ExpenseCategories.Clear();

                var totalExpenses = Transactions.Where(t => t.Type == "Dépense").Sum(t => t.Amount);

                var categories = Transactions
                    .Where(t => t.Type == "Dépense")
                    .GroupBy(t => t.Category)
                    .Select(g => new ExpenseCategory
                    {
                        Name = g.Key,
                        Amount = g.Sum(t => t.Amount),
                        Percentage = totalExpenses > 0 ? (g.Sum(t => t.Amount) / totalExpenses) * 100 : 0,
                        Color = GetCategoryColor(g.Key)
                    })
                    .OrderByDescending(c => c.Amount);

                foreach (var category in categories)
                {
                    ExpenseCategories.Add(category);
                }
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du chargement des catégories: {ex.Message}");
            }
        }

        private void CalculateFinancialSummary()
        {
            try
            {
                TotalIncome = Transactions.Where(t => t.Type == "Revenu").Sum(t => t.Amount);
                TotalExpenses = Transactions.Where(t => t.Type == "Dépense").Sum(t => t.Amount);
                NetProfit = TotalIncome - TotalExpenses;
                ProfitMargin = TotalIncome > 0 ? (NetProfit / TotalIncome) * 100 : 0;

                // Simulation de croissance
                IncomeGrowth = 12.5m;
                ExpenseGrowth = 8.3m;
                CashBalance = 45000m; // Simulation
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du calcul du résumé financier: {ex.Message}");
            }
        }

        private async Task FilterTransactionsAsync()
        {
            try
            {
                // TODO: Implémenter le filtrage dans la base de données
                await Task.Delay(100); // Simulation
                await LoadTransactionsAsync();
                CalculateFinancialSummary();
            }
            catch (Exception ex)
            {
                MessageBoxHelper.ShowError($"Erreur lors du filtrage: {ex.Message}");
            }
        }

        private void AddIncome()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'ajout de revenu en cours de développement.");
        }

        private void AddExpense()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'ajout de dépense en cours de développement.");
        }

        private void EditTransaction(FinancialTransaction transaction)
        {
            if (transaction == null) return;
            MessageBoxHelper.ShowInfo($"Modification de la transaction '{transaction.Description}' en cours de développement.");
        }

        private void DeleteTransaction(FinancialTransaction transaction)
        {
            if (transaction == null) return;

            if (MessageBoxHelper.ShowConfirmation($"Êtes-vous sûr de vouloir supprimer cette transaction ?"))
            {
                try
                {
                    // TODO: Supprimer de la base de données
                    Transactions.Remove(transaction);
                    CalculateFinancialSummary();
                    MessageBoxHelper.ShowSuccess("Transaction supprimée avec succès.");
                }
                catch (Exception ex)
                {
                    MessageBoxHelper.ShowError($"Erreur lors de la suppression: {ex.Message}");
                }
            }
        }

        private void ExportData()
        {
            MessageBoxHelper.ShowInfo("Fonctionnalité d'export en cours de développement.");
        }

        private void GenerateReport()
        {
            MessageBoxHelper.ShowInfo("Génération de rapport en cours de développement.");
        }

        private void ManageCategories()
        {
            MessageBoxHelper.ShowInfo("Gestion des catégories en cours de développement.");
        }

        private string GetCategoryColor(string category)
        {
            return category switch
            {
                "Achats" => "#FF5722",
                "Personnel" => "#9C27B0",
                "Charges" => "#FF9800",
                "Marketing" => "#4CAF50",
                "Transport" => "#2196F3",
                _ => "#607D8B"
            };
        }
    }

    // Modèles pour les transactions financières
    public class FinancialTransaction
    {
        public int Id { get; set; }
        public string Type { get; set; } // "Revenu" ou "Dépense"
        public DateTime Date { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; }
        public string Reference { get; set; }
        public string Notes { get; set; }
        public int? UserId { get; set; }
        public string UserName { get; set; }
    }

    public class ExpenseCategory
    {
        public string Name { get; set; }
        public decimal Amount { get; set; }
        public decimal Percentage { get; set; }
        public string Color { get; set; }
    }
}
