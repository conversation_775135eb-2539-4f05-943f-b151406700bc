using System;
using System.ComponentModel.DataAnnotations;

namespace ZinStore.Core.Models
{
    /// <summary>
    /// Modèle pour les lignes de vente
    /// </summary>
    public class LigneVente : BaseEntity
    {
        [Required]
        public int VenteId { get; set; }

        [Required]
        public int ProduitId { get; set; }

        [Required]
        [StringLength(50)]
        public string CodeProduit { get; set; }

        [Required]
        [StringLength(200)]
        public string NomProduit { get; set; }

        [StringLength(500)]
        public string DescriptionProduit { get; set; }

        [Required]
        public decimal PrixUnitaire { get; set; }

        [Required]
        public decimal Quantite { get; set; }

        public decimal PourcentageRemise { get; set; } = 0;

        public decimal MontantRemise { get; set; } = 0;

        public decimal TauxTVA { get; set; } = 19;

        public decimal MontantTVA { get; set; } = 0;

        public decimal TotalHT { get; set; }

        public decimal TotalTTC { get; set; }

        [StringLength(50)]
        public string Unite { get; set; }

        public string Notes { get; set; }

        // Propriétés de navigation
        public virtual Vente Vente { get; set; }
        public virtual Produit Produit { get; set; }

        // Méthodes de calcul
        public void CalculerTotal()
        {
            decimal sousTotal = PrixUnitaire * Quantite;
            MontantRemise = sousTotal * (PourcentageRemise / 100);
            TotalHT = sousTotal - MontantRemise;
            MontantTVA = TotalHT * (TauxTVA / 100);
            TotalTTC = TotalHT + MontantTVA;
        }

        public void CalculerTotalAvecTVA(decimal tauxTVA)
        {
            TauxTVA = tauxTVA;
            CalculerTotal();
        }
    }
}
