-- =====================================================
-- Script de création de la base de données ZinStore
-- Système de Gestion de Magasin
-- Version: MySQL 8.0+
-- =====================================================

-- Créer la base de données
CREATE DATABASE IF NOT EXISTS zinstore
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- Utiliser la base de données
USE zinstore;

-- =====================================================
-- Configuration des paramètres MySQL
-- =====================================================

-- Activer les clés étrangères
SET FOREIGN_KEY_CHECKS = 1;

-- Définir le mode SQL strict
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- =====================================================
-- Commentaires sur la structure
-- =====================================================

/*
Structure de la base de données ZinStore:

1. GESTION DES UTILISATEURS
   - utilisateurs: Comptes utilisateurs du système

2. GESTION DES CLIENTS ET FOURNISSEURS
   - clients: Informations des clients
   - fournisseurs: Informations des fournisseurs

3. GESTION DES PRODUITS
   - categories: Catégories de produits
   - produits: Catalogue des produits
   - stock: Gestion des stocks

4. GESTION DES TRANSACTIONS
   - ventes: En-têtes des ventes
   - ventes_details: Détails des lignes de vente
   - achats: En-têtes des achats
   - achats_details: Détails des lignes d'achat

5. GESTION FINANCIÈRE
   - revenus: Enregistrement des revenus
   - depenses: Enregistrement des dépenses
   - mouvements_stock: Historique des mouvements

6. CONFIGURATION
   - parametres: Paramètres système
   - comptes_generaux: Plan comptable
*/

-- =====================================================
-- Fin du script de création de base de données
-- =====================================================
